#!/usr/bin/env python3
"""
enhanced_auto_youtube.py - Professional Automated YouTube Video Creation System

This enhanced system automatically:
1. Fetches trending keywords with fallback options
2. Generates high-quality video scripts using Ollama
3. Creates professional audio with enhancement
4. Generates videos using Blender or FFmpeg
5. Creates attractive thumbnails with gradients
6. Uploads to YouTube with optimized metadata

Features:
- Modular architecture for easy maintenance
- Robust error handling and fallback systems
- Professional video quality with Blender integration
- Enhanced audio processing
- Comprehensive logging and monitoring
- Configurable settings
"""

import os
import sys
import time
import logging
import json
import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# Import our enhanced modules
from config import get_config, create_directories, validate_config
from ollama_manager import OllamaManager
from audio_manager import AudioManager
from video_manager import VideoManager

# Try to import optional modules
try:
    from pytrends.request import TrendReq
    PYTRENDS_AVAILABLE = True
except ImportError:
    PYTRENDS_AVAILABLE = False

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.auth.transport.requests import Request
    from googleapiclient.discovery import build
    import googleapiclient.http
    GOOGLE_API_AVAILABLE = True
except ImportError:
    GOOGLE_API_AVAILABLE = False

class EnhancedAutoYouTube:
    """Enhanced YouTube automation system with professional features."""
    
    def __init__(self):
        """Initialize the enhanced automation system."""
        self.config = get_config()
        self.setup_logging()
        
        # Initialize managers
        self.ollama_manager = OllamaManager()
        self.audio_manager = AudioManager()
        self.video_manager = VideoManager()
        
        # YouTube API
        self.youtube = None
        
        logger.info("Enhanced Auto YouTube system initialized")
        logger.info(f"Ollama available: {self.ollama_manager.available}")
        logger.info(f"PyTrends available: {PYTRENDS_AVAILABLE}")
        logger.info(f"PIL available: {PIL_AVAILABLE}")
        logger.info(f"Google API available: {GOOGLE_API_AVAILABLE}")
    
    def setup_logging(self):
        """Setup enhanced logging."""
        log_config = self.config["logging"]
        
        # Create logs directory
        log_config["file"].parent.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_config["level"]),
            format=log_config["format"],
            handlers=[
                logging.FileHandler(log_config["file"]),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        global logger
        logger = logging.getLogger(__name__)
    
    def get_trending_keywords(self) -> List[str]:
        """Get trending keywords with enhanced fallback."""
        try:
            if PYTRENDS_AVAILABLE:
                logger.info("Fetching trending keywords from Google Trends")
                pytrends = TrendReq(hl='en-US', tz=360)
                
                # Get trending searches
                trending_searches = pytrends.trending_searches(pn='united_states')
                
                if not trending_searches.empty:
                    keywords = trending_searches[0].head(5).tolist()
                    logger.info(f"Found {len(keywords)} trending keywords: {keywords}")
                    return keywords
                else:
                    logger.warning("No trending keywords found")
            else:
                logger.warning("PyTrends not available")
                
        except Exception as e:
            logger.error(f"Error fetching trending keywords: {e}")
        
        # Fallback to configured keywords
        fallback_keywords = self.config["fallback_keywords"][:3]
        logger.info(f"Using fallback keywords: {fallback_keywords}")
        return fallback_keywords
    
    def create_thumbnail(self, script: Dict[str, str], keyword: str) -> Optional[str]:
        """Create professional thumbnail."""
        if not PIL_AVAILABLE:
            logger.error("PIL not available for thumbnail creation")
            return None
        
        try:
            thumbnail_config = self.config["thumbnail"]
            thumbnail_path = self.config["directories"]["thumbnails"] / f"{keyword.replace(' ', '_')}.jpg"
            
            # Create image
            width, height = thumbnail_config["width"], thumbnail_config["height"]
            image = Image.new('RGB', (width, height), (0, 0, 0))
            draw = ImageDraw.Draw(image)
            
            # Create gradient background
            for y in range(height):
                r = int(25 + (y / height) * 30)
                g = int(25 + (y / height) * 10)
                b = int(50 + (y / height) * 70)
                draw.line([(0, y), (width, y)], fill=(r, g, b))
            
            # Load fonts
            try:
                title_font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 80)
                subtitle_font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 40)
            except IOError:
                title_font = ImageFont.load_default()
                subtitle_font = ImageFont.load_default()
            
            # Add title
            title = script["title"]
            if len(title) > 50:
                title = title[:47] + "..."
            
            # Calculate text position
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            title_height = title_bbox[3] - title_bbox[1]
            title_position = ((width - title_width) // 2, (height - title_height) // 2 - 50)
            
            # Draw title with shadow
            draw.text((title_position[0]+3, title_position[1]+3), title, font=title_font, fill=(0, 0, 0))
            draw.text(title_position, title, font=title_font, fill=(255, 255, 255))
            
            # Add subtitle
            subtitle = f"#{keyword.replace(' ', '')}"
            subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
            subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
            subtitle_position = ((width - subtitle_width) // 2, title_position[1] + title_height + 40)
            
            # Draw subtitle with shadow
            draw.text((subtitle_position[0]+2, subtitle_position[1]+2), subtitle, font=subtitle_font, fill=(0, 0, 0))
            draw.text(subtitle_position, subtitle, font=subtitle_font, fill=(255, 215, 0))
            
            # Add play button
            play_button_size = 100
            play_button_x = width // 2 - play_button_size // 2
            play_button_y = height - 180
            
            # Draw play button circle
            draw.ellipse(
                [(play_button_x, play_button_y), 
                 (play_button_x + play_button_size, play_button_y + play_button_size)], 
                fill=(200, 0, 0)
            )
            
            # Draw play triangle
            triangle_points = [
                (play_button_x + play_button_size // 3, play_button_y + play_button_size // 4),
                (play_button_x + play_button_size // 3, play_button_y + play_button_size * 3 // 4),
                (play_button_x + play_button_size * 3 // 4, play_button_y + play_button_size // 2)
            ]
            draw.polygon(triangle_points, fill=(255, 255, 255))
            
            # Save thumbnail
            image.save(str(thumbnail_path), quality=thumbnail_config["quality"])
            
            logger.info(f"Created thumbnail: {thumbnail_path}")
            return str(thumbnail_path)
            
        except Exception as e:
            logger.error(f"Error creating thumbnail: {e}")
            return None
    
    def process_keyword(self, keyword: str) -> bool:
        """Process a single keyword through the enhanced pipeline."""
        try:
            logger.info(f"Processing keyword: '{keyword}'")
            
            # Generate script
            script = self.ollama_manager.generate_script(keyword)
            if not script:
                logger.warning(f"Using fallback script for '{keyword}'")
                script = self.ollama_manager.get_fallback_script(keyword)
            
            # Save script
            script_path = self.config["directories"]["scripts"] / f"{keyword.replace(' ', '_')}.json"
            with open(script_path, 'w', encoding='utf-8') as f:
                json.dump(script, f, ensure_ascii=False, indent=4)
            
            # Generate audio
            audio_path = self.audio_manager.generate_audio(script, keyword)
            if not audio_path:
                logger.error(f"Failed to generate audio for '{keyword}'")
                return False
            
            # Validate audio
            if not self.audio_manager.validate_audio(audio_path):
                logger.error(f"Audio validation failed for '{keyword}'")
                return False
            
            # Create video
            video_path = self.video_manager.create_video(script, audio_path, keyword)
            if not video_path:
                logger.error(f"Failed to create video for '{keyword}'")
                return False
            
            # Validate video
            if not self.video_manager.validate_video(video_path):
                logger.error(f"Video validation failed for '{keyword}'")
                return False
            
            # Create thumbnail
            thumbnail_path = self.create_thumbnail(script, keyword)
            
            # Upload to YouTube if available
            if GOOGLE_API_AVAILABLE:
                video_url = self.upload_to_youtube(video_path, thumbnail_path, script, keyword)
                if video_url:
                    logger.info(f"Successfully uploaded '{keyword}' to YouTube: {video_url}")
                else:
                    logger.warning(f"YouTube upload failed for '{keyword}'")
            else:
                logger.info(f"YouTube upload skipped (API not available). Video saved: {video_path}")
            
            logger.info(f"Successfully processed keyword: '{keyword}'")
            return True
            
        except Exception as e:
            logger.error(f"Error processing keyword '{keyword}': {e}")
            return False
    
    def upload_to_youtube(self, video_path: str, thumbnail_path: Optional[str], 
                         script: Dict[str, str], keyword: str) -> Optional[str]:
        """Upload video to YouTube with optimized metadata."""
        # This is a placeholder - implement YouTube upload logic here
        logger.info(f"YouTube upload placeholder for '{keyword}'")
        return None
    
    def run(self):
        """Run the enhanced automation system."""
        try:
            logger.info("Starting Enhanced Auto YouTube system")
            
            # Validate configuration
            if not validate_config():
                logger.error("Configuration validation failed")
                return False
            
            # Create directories
            create_directories()
            
            # Get trending keywords
            keywords = self.get_trending_keywords()
            if not keywords:
                logger.error("No keywords available for processing")
                return False
            
            # Process each keyword
            success_count = 0
            for i, keyword in enumerate(keywords):
                try:
                    logger.info(f"Processing keyword {i+1}/{len(keywords)}: '{keyword}'")
                    
                    if self.process_keyword(keyword):
                        success_count += 1
                        logger.info(f"Successfully processed '{keyword}'")
                    else:
                        logger.warning(f"Failed to process '{keyword}'")
                    
                    # Add delay between keywords
                    if i < len(keywords) - 1:
                        delay = self.config["performance"]["delay_between_keywords"]
                        logger.info(f"Waiting {delay} seconds before next keyword...")
                        time.sleep(delay)
                        
                except Exception as e:
                    logger.error(f"Error processing keyword '{keyword}': {e}")
                    continue
            
            logger.info(f"Completed processing. Success rate: {success_count}/{len(keywords)}")
            return success_count > 0
            
        except KeyboardInterrupt:
            logger.info("Process interrupted by user")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in main process: {e}")
            return False

def main():
    """Main function."""
    try:
        # Initialize and run the system
        auto_youtube = EnhancedAutoYouTube()
        success = auto_youtube.run()
        
        if success:
            logger.info("Enhanced Auto YouTube system completed successfully")
            sys.exit(0)
        else:
            logger.error("Enhanced Auto YouTube system failed")
            sys.exit(1)
            
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
