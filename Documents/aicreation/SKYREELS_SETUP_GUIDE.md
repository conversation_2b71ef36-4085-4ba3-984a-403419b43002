# 🎭 Guide d'Installation SkyReels-A1 Avatar Creator

**Système de création d'avatars parlants avec IA générative**

## 🎯 Vue d'Ensemble

SkyReels-A1 permet de créer des **avatars parlants réalistes** à partir de :
- 📸 **Une image de portrait** (votre avatar)
- 🎤 **Un fichier audio** (génère automatiquement les mouvements de lèvres)
- 🎬 **Une vidéo de conduite** (copie les mouvements sur votre avatar)

## 📋 Prérequis Système

### 💻 **Matériel Recommandé**
- **GPU NVIDIA** avec 8GB+ VRAM (RTX 3070/4070+)
- **16GB RAM** système minimum
- **50GB espace disque** libre
- **CUDA 11.8+** installé

### 🐍 **Logiciels Requis**
- **Python 3.10** (recommandé)
- **Git LFS** pour télécharger les modèles
- **FFmpeg** pour traitement vidéo/audio

## 🚀 Installation Complète

### 1. **Clone du Repository SkyReels-A1**

```bash
# Clone du repository officiel
git clone https://github.com/SkyworkAI/SkyReels-A1.git
cd SkyReels-A1

# Copier notre intégration
cp ../skyreels_avatar_creator.py .
```

### 2. **Environnement Python**

```bash
# Créer environnement conda
conda create -n skyreels-a1 python=3.10
conda activate skyreels-a1

# Installer les dépendances
pip install -r requirements.txt

# Dépendances supplémentaires
pip install loguru moviepy decord insightface facexlib
```

### 3. **Téléchargement des Modèles (50GB)**

```bash
# Installer Hugging Face CLI
pip install -U "huggingface_hub[cli]"

# Télécharger SkyReels-A1 (modèle principal)
huggingface-cli download Skywork/SkyReels-A1 \
  --local-dir pretrained_models/SkyReels-A1-5B \
  --exclude "*.git*" "README.md" "docs"
```

### 4. **Modèles Supplémentaires**

#### 🔥 **FLAME, MediaPipe, SMIRK**
```bash
# Ces modèles sont inclus dans le téléchargement SkyReels-A1
# Vérifier la structure :
ls pretrained_models/
# ├── FLAME/
# ├── SkyReels-A1-5B/
# ├── mediapipe/
# └── smirk/
```

#### 🎤 **DiffPoseTalk (pour audio)**
```bash
# Clone DiffPoseTalk
git clone https://github.com/DiffPoseTalk/DiffPoseTalk.git
cd DiffPoseTalk

# Suivre leur README pour télécharger les poids
# Puis copier dans notre structure :
cp -r style ../pretrained_models/diffposetalk/
cp experiments/DPT/head-SA-hubert-WM/checkpoints/iter_0110000.pt ../pretrained_models/diffposetalk/
cp datasets/HDTF_TFHP/lmdb/stats_train.npz ../pretrained_models/diffposetalk/
```

#### 🎬 **FILM (interpolation de frames)**
```bash
# Télécharger le modèle FILM
mkdir -p pretrained_models/film_net
cd pretrained_models/film_net
wget https://github.com/dajes/frame-interpolation-pytorch/releases/download/v1.0.0/film_net_fp16.pt
```

### 5. **Structure Finale des Modèles**

```
pretrained_models/
├── FLAME/                    # Modèle de visage 3D
├── SkyReels-A1-5B/          # Modèle principal
│   ├── pose_guider/
│   ├── scheduler/
│   ├── tokenizer/
│   ├── siglip-so400m-patch14-384/
│   ├── transformer/
│   ├── vae/
│   └── text_encoder/
├── mediapipe/               # Détection de landmarks
├── smirk/                   # Analyse d'expression
├── diffposetalk/            # Audio vers mouvement
│   ├── style/
│   ├── iter_0110000.pt
│   └── stats_train.npz
└── film_net/                # Interpolation de frames
    └── film_net_fp16.pt
```

## 🧪 Test de l'Installation

```bash
# Vérifier l'installation
python skyreels_avatar_creator.py --check

# Sortie attendue :
# ✅ Installation SkyReels-A1 complète
# ✅ Modules SkyReels-A1 importés
```

## 🎭 Utilisation

### 📸 **Préparer votre Image Avatar**

**Critères pour une image optimale :**
- **Format :** JPG ou PNG
- **Résolution :** Minimum 512x512, idéal 1024x1024
- **Contenu :** Portrait face caméra, bien éclairé
- **Qualité :** Visage net, expression neutre
- **Cadrage :** Visage centré, épaules visibles

**Exemples d'images idéales :**
```
✅ Portrait professionnel face caméra
✅ Selfie bien éclairé, fond neutre
✅ Photo d'identité haute qualité
✅ Webcam HD avec bon éclairage

❌ Profil ou 3/4
❌ Visage partiellement caché
❌ Éclairage trop sombre/fort
❌ Résolution trop faible
```

### 🎤 **Mode Avatar Parlant (Audio)**

```bash
# Créer un avatar parlant à partir d'audio
python skyreels_avatar_creator.py \
  --image "mon_portrait.jpg" \
  --audio "mon_discours.wav" \
  --output "outputs"

# Formats audio supportés : WAV, MP3, M4A
# Durée recommandée : 10-60 secondes
```

### 🎬 **Mode Copie de Mouvements (Vidéo)**

```bash
# Copier les mouvements d'une vidéo sur votre avatar
python skyreels_avatar_creator.py \
  --image "mon_portrait.jpg" \
  --video "video_reference.mp4" \
  --output "outputs"

# La vidéo de référence doit contenir un visage parlant
```

### 🎭 **Mode Démonstration**

```bash
# Créer un avatar de démonstration sans audio/vidéo
python skyreels_avatar_creator.py \
  --image "mon_portrait.jpg" \
  --output "outputs"
```

## 📊 Paramètres Avancés

### ⚙️ **Configuration Performance**

Modifiez dans `skyreels_avatar_creator.py` :

```python
# Pour GPU puissant (RTX 4090)
self.num_inference_steps = 20  # Plus de qualité
self.sample_size = [720, 1280]  # Résolution plus haute

# Pour GPU moyen (RTX 3070)
self.num_inference_steps = 10   # Défaut
self.sample_size = [480, 720]   # Défaut

# Pour GPU faible ou CPU
self.num_inference_steps = 5    # Plus rapide
self.sample_size = [360, 640]   # Résolution réduite
```

### 🎯 **Qualité vs Vitesse**

| Paramètre | Rapide | Équilibré | Qualité |
|-----------|--------|-----------|---------|
| **inference_steps** | 5 | 10 | 20 |
| **sample_size** | 360x640 | 480x720 | 720x1280 |
| **target_fps** | 12 | 12 | 24 |
| **Temps GPU** | 2-3 min | 5-8 min | 15-20 min |

## 🔧 Résolution de Problèmes

### ❌ **Erreurs Communes**

#### **"CUDA out of memory"**
```bash
# Réduire la résolution
self.sample_size = [360, 640]
# Ou utiliser CPU
self.device = "cpu"
```

#### **"Module not found: skyreels_a1"**
```bash
# Vérifier que vous êtes dans le bon dossier
cd SkyReels-A1
python skyreels_avatar_creator.py --check
```

#### **"Pretrained models not found"**
```bash
# Re-télécharger les modèles
huggingface-cli download Skywork/SkyReels-A1 \
  --local-dir pretrained_models/SkyReels-A1-5B
```

### 🐛 **Debug Mode**

```bash
# Activer les logs détaillés
export LOGURU_LEVEL=DEBUG
python skyreels_avatar_creator.py --image test.jpg --audio test.wav
```

## 📈 Optimisations

### 🚀 **Performance GPU**

```python
# Dans skyreels_avatar_creator.py
# Activer les optimisations mémoire
pipe.enable_model_cpu_offload()
pipe.vae.enable_tiling()
pipe.enable_attention_slicing()

# Utiliser la compilation torch (PyTorch 2.0+)
pipe.transformer = torch.compile(pipe.transformer)
```

### 💾 **Gestion Mémoire**

```python
# Libérer la mémoire entre générations
torch.cuda.empty_cache()

# Utiliser la précision mixte
torch.autocast(device_type='cuda', dtype=torch.float16)
```

## 🎯 Exemples d'Utilisation

### 📺 **Création de Contenu YouTube**
```bash
# Avatar présentateur avec script audio
python skyreels_avatar_creator.py \
  --image "presentateur.jpg" \
  --audio "script_youtube.wav" \
  --output "youtube_videos"
```

### 🎓 **Formation en Ligne**
```bash
# Instructeur virtuel
python skyreels_avatar_creator.py \
  --image "instructeur.jpg" \
  --audio "cours_audio.wav" \
  --output "formations"
```

### 💼 **Présentation Corporate**
```bash
# Porte-parole d'entreprise
python skyreels_avatar_creator.py \
  --image "ceo_portrait.jpg" \
  --audio "message_entreprise.wav" \
  --output "corporate"
```

## 🔄 Intégration avec le Système Principal

```python
# Dans professional_video_creator.py
from skyreels_avatar_creator import SkyReelsAvatarCreator

# Créer un avatar pour la vidéo
avatar_creator = SkyReelsAvatarCreator()
avatar_video = avatar_creator.create_talking_avatar(
    image_path="avatar.jpg",
    audio_path="script_audio.wav"
)

# Intégrer dans la vidéo finale
# ... (code d'intégration)
```

## 📋 Checklist de Production

### ✅ **Avant de Commencer**
- [ ] GPU avec 8GB+ VRAM disponible
- [ ] Tous les modèles téléchargés (50GB)
- [ ] Image de portrait haute qualité préparée
- [ ] Audio/vidéo de conduite prêts
- [ ] Espace disque suffisant pour outputs

### ✅ **Test de Qualité**
- [ ] Visage bien détecté et aligné
- [ ] Mouvements de lèvres synchronisés
- [ ] Qualité vidéo satisfaisante
- [ ] Audio correctement intégré
- [ ] Durée conforme aux attentes

### ✅ **Optimisation**
- [ ] Paramètres ajustés selon le GPU
- [ ] Temps de génération acceptable
- [ ] Qualité/performance équilibrées
- [ ] Mémoire GPU sous contrôle

---

## 🎉 Félicitations !

Vous avez maintenant un système complet de création d'avatars parlants avec SkyReels-A1 ! 

**Prochaines étapes :**
1. Tester avec votre propre portrait
2. Expérimenter avec différents audios
3. Intégrer dans vos projets vidéo
4. Optimiser selon vos besoins

**Support :** Consultez le [repository officiel](https://github.com/SkyworkAI/SkyReels-A1) pour les dernières mises à jour.
