# 🚀 Guide de Démarrage Rapide

## Installation en 3 étapes

### 1. P<PERSON>requis
```bash
# Vérifiez Python 3.8+
python --version

# Installez Ollama (pour l'IA)
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull phi3
```

### 2. Installation
```bash
cd aicreation
pip install -r requirements.txt
```

### 3. Premier test
```bash
python start.py
# Choisissez option 3 pour tester
```

## 🎬 Créer votre première vidéo

### Méthode 1: Interface simple
```bash
python start.py
# Choisissez option 1 et entrez votre sujet
```

### Méthode 2: Ligne de commande
```bash
python test_single_keyword.py "intelligence artificielle"
```

## 📁 Où trouver vos vidéos

Tous les fichiers sont générés dans `output/` :
- **Vidéos** : `output/videos/`
- **Audio** : `output/audio/`
- **Miniatures** : `output/thumbnails/`
- **Scripts** : `output/scripts/`

## 🌟 Améliorer la qualité vocale

### OpenAI TTS (Recommandé)
```bash
# 1. Créez un compte: https://platform.openai.com/
# 2. Générez une API key
export OPENAI_API_KEY="votre_cle"
# 3. Relancez le système
```

### ElevenLabs (Qualité maximale)
```bash
# 1. Créez un compte: https://elevenlabs.io/
# 2. Obtenez votre API key
export ELEVENLABS_API_KEY="votre_cle"
# 3. Relancez le système
```

## 🔧 Résolution de problèmes

### Ollama ne fonctionne pas
```bash
ollama serve
ollama pull phi3
```

### Erreur FFmpeg
```bash
# macOS
brew install ffmpeg

# Ubuntu
sudo apt install ffmpeg
```

### Erreur de dépendances
```bash
pip install --upgrade -r requirements.txt
```

## 📖 Documentation complète

- **README.md** - Guide complet
- **TTS_COMPARISON_GUIDE.md** - Guide des voix
- **start.py** - Interface simple

## 🎯 Exemples de sujets

- "intelligence artificielle"
- "changement climatique"
- "exploration spatiale"
- "énergie renouvelable"
- "technologie blockchain"
- "réalité virtuelle"

---

🎬 **Créez des vidéos YouTube professionnelles en quelques minutes !**
