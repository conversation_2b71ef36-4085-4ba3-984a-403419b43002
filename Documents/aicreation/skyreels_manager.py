"""
Module de gestion de SkyReels-A1 pour la génération de contenus d'influenceur virtuel.
"""

import os
import yaml
import torch
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, cast
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer
from diffusers.pipelines.stable_diffusion.pipeline_stable_diffusion import StableDiffusionPipeline
from diffusers.pipelines.pipeline_utils import DiffusionPipeline
import gc
import numpy as np
from PIL import Image
from contextlib import contextmanager
import torch

class SkyReelsManager:
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialise le gestionnaire SkyReels-A1.
        
        Args:
            config_path: Chemin vers le fichier de configuration
        """
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        self.device = "cuda" if torch.cuda.is_available() and self.config["performance"]["use_gpu"] else "cpu"
        
        # Initialiser les modèles à la demande
        self._siglip = None
        self._text_encoder = None
        self._tokenizer = None
        self._pipeline = None
        self._pose_guider = None
        
    @property
    def siglip(self):
        if self._siglip is None:
            self._init_siglip()
        return self._siglip
        
    @property
    def text_encoder(self):
        if self._text_encoder is None:
            self._init_text_encoder()
        return self._text_encoder
        
    @property
    def tokenizer(self):
        if self._tokenizer is None:
            self._init_text_encoder()  # Initialise aussi le tokenizer
        return self._tokenizer
        
    @property
    def pipeline(self):
        if self._pipeline is None:
            self._init_pipeline()
        return self._pipeline
        
    @property
    def pose_guider(self):
        if self._pose_guider is None:
            self._init_pose_guider()
        return self._pose_guider
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Charge la configuration depuis le fichier YAML."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
                
            base_path = config["models"]["base_path"]
            for key in config["models"]:
                if key != "base_path":
                    config["models"][key] = config["models"][key].replace("${base_path}", base_path)
                    
            return config
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement de la configuration: {str(e)}")
            raise
            
    def _get_model_config(self):
        """Retourne la configuration commune des modèles."""
        return {
            "device_map": "balanced" if self.config["performance"].get("offload_to_cpu", False) else None,
            "torch_dtype": torch.float16 if self.config["performance"].get("use_fp16", False) else torch.float32,
            "low_cpu_mem_usage": True
        }
        
    def _init_siglip(self):
        """Initialise le modèle SIGLIP."""
        self.logger.info("Chargement du modèle SIGLIP...")
        model_config = self._get_model_config()
        self._siglip = AutoModel.from_pretrained(
            self.config["models"]["siglip"],
            **model_config
        )
        if model_config["device_map"] is None:
            self._siglip = self._siglip.to(self.device)
        self._cleanup_memory()
        
    def _init_text_encoder(self):
        """Initialise le text encoder et le tokenizer."""
        self.logger.info("Chargement du text encoder...")
        model_config = self._get_model_config()
        self._text_encoder = AutoModel.from_pretrained(
            self.config["models"]["text_encoder"],
            **model_config
        )
        if model_config["device_map"] is None:
            self._text_encoder = self._text_encoder.to(self.device)
            
        self._tokenizer = AutoTokenizer.from_pretrained(
            self.config["models"]["text_encoder"]
        )
        self._cleanup_memory()
        
    def _init_pipeline(self):
        """Initialise le pipeline de génération principal."""
        self.logger.info("Initialisation du pipeline de génération...")
        model_config = self._get_model_config()
        
        try:
            self._pipeline = StableDiffusionPipeline.from_pretrained(
                self.config["models"]["transformer"],
                use_safetensors=True,
                **model_config
            )
            
            if model_config["device_map"] is None:
                self._pipeline = self._pipeline.to(self.device)
                
            # Optimisations mémoire
            if self.config["performance"].get("use_attention_slicing", True):
                self._pipeline.enable_attention_slicing()
            if self.config["performance"].get("use_vae_slicing", True):
                self._pipeline.enable_vae_slicing()
            if self.config["performance"].get("use_xformers", False):
                self._pipeline.enable_xformers_memory_efficient_attention()
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation du pipeline: {str(e)}")
            raise
            
        self._cleanup_memory()

    def _init_pose_guider(self):
        """Initialise le modèle de guidage de pose."""
        self.logger.info("Chargement du modèle de guidage de pose...")
        model_config = self._get_model_config()
        
        try:
            self._pose_guider = AutoModel.from_pretrained(
                self.config["models"]["pose_guider"],
                **model_config
            )
            
            if model_config["device_map"] is None:
                self._pose_guider = self._pose_guider.to(self.device)
                
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement du modèle de pose: {str(e)}")
            raise
            
        self._cleanup_memory()

    def _cleanup_memory(self):
        """Nettoie la mémoire GPU/CPU non utilisée."""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

    @contextmanager
    def memory_management(self):
        """Context manager pour la gestion automatique de la mémoire."""
        try:
            yield
        finally:
            self._cleanup_memory()

    @torch.no_grad()
    def generate_content(
        self,
        prompt: str,
        num_images: int = 1,
        guidance_scale: float = 7.5,
        num_inference_steps: int = 50,
        height: int = 768,
        width: int = 512,
        pose_condition: Optional[torch.Tensor] = None
    ) -> Dict[str, Any]:
        """
        Génère du contenu avec SkyReels-A1.
        
        Args:
            prompt: Description textuelle du contenu à générer
            num_images: Nombre d'images à générer
            guidance_scale: Échelle de guidage pour la génération
            num_inference_steps: Nombre d'étapes d'inférence
            height: Hauteur des images générées
            width: Largeur des images générées
            pose_condition: Tenseur de condition de pose optionnel
            
        Returns:
            Dict contenant les résultats de la génération
        """
        try:
            with self.memory_management():
                # Générer les images avec la pipeline
                output = self.pipeline(
                    prompt=prompt,
                    num_images_per_prompt=num_images,
                    guidance_scale=guidance_scale,
                    num_inference_steps=num_inference_steps,
                    height=height,
                    width=width,
                    generator=torch.Generator(device=self.device).manual_seed(42)
                )
                
                # Convertir les images en numpy arrays
                images = []
                for image in output.images:
                    # L'image est déjà un PIL.Image, la convertir en numpy array
                    img_array = np.array(image)
                    images.append(img_array)
                
                return {
                    "images": images,
                    "prompt": prompt,
                    "parameters": {
                        "guidance_scale": guidance_scale,
                        "num_inference_steps": num_inference_steps,
                        "height": height,
                        "width": width
                    }
                }
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération: {str(e)}")
            raise

    def generate_image(
        self,
        prompt: str,
        negative_prompt: str = "",
        num_inference_steps: int = 50,
        guidance_scale: float = 7.5,
        width: int = 512,
        height: int = 512,
        seed: Optional[int] = None,
    ) -> np.ndarray:
        """
        Génère une image à partir d'un prompt textuel.
        
        Args:
            prompt: Description textuelle de l'image souhaitée
            negative_prompt: Prompt négatif pour guider la génération
            num_inference_steps: Nombre d'étapes d'inférence
            guidance_scale: Échelle de guidance pour la génération
            width: Largeur de l'image
            height: Hauteur de l'image
            seed: Graine pour la reproductibilité
            
        Returns:
            Image générée sous forme de tableau numpy
        """
        with self.memory_management():
            if seed is not None:
                generator = torch.Generator(device=self.device).manual_seed(seed)
            else:
                generator = None
                
            output = self.pipeline(
                prompt=prompt,
                negative_prompt=negative_prompt,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                width=width,
                height=height,
                generator=generator,
            )
            
            # Extraire l'image du résultat
            if isinstance(output, tuple):
                image = output[0]
            else:
                image = output["images"][0] if "images" in output else output[0]
                
            # Convertir en numpy array si nécessaire
            if isinstance(image, torch.Tensor):
                image = image.cpu().numpy()
            elif isinstance(image, Image.Image):
                image = np.array(image)
                
            return cast(np.ndarray, image)

    def generate_batch(
        self,
        prompts: List[str],
        negative_prompt: str = "",
        num_inference_steps: int = 50,
        guidance_scale: float = 7.5,
        width: int = 512,
        height: int = 512,
        seed: Optional[int] = None,
    ) -> List[np.ndarray]:
        """
        Génère un lot d'images à partir d'une liste de prompts.
        
        Args:
            prompts: Liste des descriptions textuelles des images souhaitées
            negative_prompt: Prompt négatif pour guider la génération
            num_inference_steps: Nombre d'étapes d'inférence
            guidance_scale: Échelle de guidance pour la génération
            width: Largeur des images
            height: Hauteur des images
            seed: Graine pour la reproductibilité
            
        Returns:
            Liste des images générées sous forme de tableaux numpy
        """
        images = []
        with self.memory_management():
            for prompt in prompts:
                img = self.generate_image(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    width=width,
                    height=height,
                    seed=seed,
                )
                images.append(img)
                
        return images

    def cleanup(self):
        """Libère les ressources utilisées par le gestionnaire."""
        self._cleanup_memory()
        if hasattr(self, "_pipeline") and self._pipeline is not None:
            del self._pipeline
        if hasattr(self, "_siglip") and self._siglip is not None:
            del self._siglip
        if hasattr(self, "_text_encoder") and self._text_encoder is not None:
            del self._text_encoder
        if hasattr(self, "_pose_guider") and self._pose_guider is not None:
            del self._pose_guider
        self._pipeline = None
        self._siglip = None
        self._text_encoder = None
        self._tokenizer = None
        self._pose_guider = None
        torch.cuda.empty_cache()

    def __enter__(self):
        """Context manager entry point."""
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        """Context manager exit point."""
        self.cleanup()
