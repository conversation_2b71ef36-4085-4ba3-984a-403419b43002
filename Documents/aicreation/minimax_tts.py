#!/usr/bin/env python3
"""
minimax_tts.py - Minimax Text-to-Speech integration for realistic voice generation
"""

import os
import json
import logging
import requests
import time
from pathlib import Path
from typing import Dict, Optional, List, Union
import tempfile

from config import get_config

logger = logging.getLogger(__name__)

class MinimaxTTS:
    """Minimax Text-to-Speech manager for ultra-realistic voice generation."""
    
    def __init__(self, api_key: Optional[str] = None, group_id: Optional[str] = None):
        """Initialize Minimax TTS client."""
        self.config = get_config()
        
        # API credentials
        self.api_key = api_key or os.getenv('MINIMAX_API_KEY')
        self.group_id = group_id or os.getenv('MINIMAX_GROUP_ID')
        
        # API endpoints
        self.base_url = "https://api.minimaxi.chat/v1"
        self.tts_endpoint = f"{self.base_url}/t2a_v2"
        self.voice_clone_endpoint = f"{self.base_url}/voice_clone"
        self.file_upload_endpoint = f"{self.base_url}/files/upload"
        
        # Available voices
        self.available_voices = {
            # English voices
            "male_confident": "male-qn-qingse",
            "female_warm": "female-shaonv",
            "male_professional": "male-qn-jingying",
            "female_energetic": "female-yujie",
            "male_narrator": "male-qn-badao",
            "female_gentle": "female-tianmei",
            
            # Multilingual voices
            "english_us_male": "en-US-male-1",
            "english_us_female": "en-US-female-1",
            "english_uk_male": "en-GB-male-1",
            "english_uk_female": "en-GB-female-1",
        }
        
        # Check availability
        self.available = self._check_availability()
        
        if self.available:
            logger.info("Minimax TTS initialized successfully")
        else:
            logger.warning("Minimax TTS not available - missing credentials or connection failed")
    
    def _check_availability(self) -> bool:
        """Check if Minimax TTS is available."""
        if not self.api_key or not self.group_id:
            logger.warning("Minimax API key or Group ID not provided")
            return False
        
        try:
            # Test API connection with a simple request
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # Make a test request (this might fail but we can check the response)
            test_url = f"{self.tts_endpoint}?GroupId={self.group_id}"
            response = requests.post(test_url, headers=headers, json={
                "text": "test",
                "model": "speech-01-turbo",
                "voice_id": self.available_voices["male_confident"]
            }, timeout=10)
            
            # Even if it fails, if we get a proper API response structure, it means the API is accessible
            if response.status_code in [200, 400, 401, 403]:  # Valid API responses
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking Minimax availability: {e}")
            return False
    
    def _make_request(self, endpoint: str, data: Dict, files: Optional[Dict] = None) -> Optional[Dict]:
        """Make a request to Minimax API."""
        try:
            headers = {'Authorization': f'Bearer {self.api_key}'}
            
            if files:
                # For file uploads, don't set Content-Type (requests will set it automatically)
                response = requests.post(endpoint, headers=headers, data=data, files=files)
            else:
                headers['Content-Type'] = 'application/json'
                response = requests.post(endpoint, headers=headers, json=data)
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Minimax API request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response content: {e.response.text}")
            return None
    
    def generate_speech(self, text: str, voice_id: Optional[str] = None, 
                       model: str = "speech-01-turbo") -> Optional[bytes]:
        """Generate speech from text using Minimax TTS."""
        if not self.available:
            logger.error("Minimax TTS not available")
            return None
        
        # Use default voice if none specified
        if not voice_id:
            voice_id = self.available_voices["female_warm"]
        
        # Prepare request data
        endpoint = f"{self.tts_endpoint}?GroupId={self.group_id}"
        data = {
            "text": text,
            "model": model,
            "voice_id": voice_id,
            "speed": 1.0,
            "vol": 0.8,
            "pitch": 0,
            "audio_sample_rate": 24000,
            "bitrate": 128000
        }
        
        try:
            logger.info(f"Generating speech with Minimax TTS (voice: {voice_id})")
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(endpoint, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            
            # Check if we have audio data
            if 'audio' in result:
                # If audio is base64 encoded
                import base64
                audio_data = base64.b64decode(result['audio'])
                logger.info("Successfully generated speech with Minimax TTS")
                return audio_data
            elif 'audio_url' in result:
                # If we get a URL, download the audio
                audio_response = requests.get(result['audio_url'])
                audio_response.raise_for_status()
                logger.info("Successfully generated speech with Minimax TTS")
                return audio_response.content
            else:
                logger.error("No audio data in Minimax response")
                return None
                
        except Exception as e:
            logger.error(f"Error generating speech with Minimax: {e}")
            return None
    
    def clone_voice_from_file(self, audio_file_path: str, voice_id: str) -> bool:
        """Clone a voice from an audio file."""
        if not self.available:
            logger.error("Minimax TTS not available")
            return False
        
        try:
            # Step 1: Upload audio file
            logger.info(f"Uploading audio file for voice cloning: {audio_file_path}")
            
            upload_endpoint = f"{self.file_upload_endpoint}?GroupId={self.group_id}"
            
            with open(audio_file_path, 'rb') as audio_file:
                files = {'file': audio_file}
                data = {'purpose': 'voice_clone'}
                
                upload_response = self._make_request(upload_endpoint, data, files)
                
                if not upload_response or 'file' not in upload_response:
                    logger.error("Failed to upload audio file")
                    return False
                
                file_id = upload_response['file']['file_id']
                logger.info(f"Audio file uploaded successfully, file_id: {file_id}")
            
            # Step 2: Clone the voice
            logger.info(f"Cloning voice with voice_id: {voice_id}")
            
            clone_endpoint = f"{self.voice_clone_endpoint}?GroupId={self.group_id}"
            clone_data = {
                "file_id": file_id,
                "voice_id": voice_id,
                "noise_reduction": True,
                "need_volume_normalization": True
            }
            
            clone_response = self._make_request(clone_endpoint, clone_data)
            
            if clone_response and clone_response.get('base_resp', {}).get('status_code') == 0:
                logger.info(f"Voice cloned successfully with voice_id: {voice_id}")
                return True
            else:
                logger.error(f"Voice cloning failed: {clone_response}")
                return False
                
        except Exception as e:
            logger.error(f"Error cloning voice: {e}")
            return False
    
    def generate_enhanced_speech(self, script: Dict[str, str], keyword: str, 
                                voice_id: Optional[str] = None) -> Optional[str]:
        """Generate enhanced speech from script using Minimax TTS."""
        try:
            # Create speech text from script
            speech_parts = []
            
            # Helper function to extract text
            def extract_text(value):
                if isinstance(value, str):
                    return value
                elif isinstance(value, dict):
                    return value.get("text", str(value))
                else:
                    return str(value)
            
            # Build speech with natural pauses
            intro = extract_text(script.get("introduction", ""))
            if intro:
                speech_parts.append(f"{intro}")
            
            for i in range(1, 4):
                key = f"key_point_{i}"
                point = script.get(key, "")
                if point:
                    point_text = extract_text(point)
                    speech_parts.append(f"{point_text}")
            
            conclusion = extract_text(script.get("conclusion", ""))
            if conclusion:
                speech_parts.append(conclusion)
            
            # Join with natural pauses
            full_text = " ... ".join(speech_parts)
            
            # Limit text length (Minimax has a 5000 character limit)
            if len(full_text) > 4500:
                full_text = full_text[:4500] + "..."
                logger.warning("Text truncated to fit Minimax character limit")
            
            # Generate speech
            audio_data = self.generate_speech(full_text, voice_id)
            
            if not audio_data:
                logger.error("Failed to generate speech with Minimax")
                return None
            
            # Save audio file
            output_path = self.config["directories"]["audio"] / f"{keyword.replace(' ', '_')}.mp3"
            
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            logger.info(f"Enhanced speech generated with Minimax: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error generating enhanced speech: {e}")
            return None
    
    def get_available_voices(self) -> Dict[str, str]:
        """Get available voice IDs."""
        return self.available_voices.copy()
    
    def set_voice_for_keyword(self, keyword: str) -> str:
        """Select appropriate voice based on keyword."""
        keyword_lower = keyword.lower()
        
        # Voice selection logic based on content type
        if any(word in keyword_lower for word in ['business', 'finance', 'professional', 'corporate']):
            return self.available_voices["male_professional"]
        elif any(word in keyword_lower for word in ['technology', 'ai', 'science', 'research']):
            return self.available_voices["male_confident"]
        elif any(word in keyword_lower for word in ['lifestyle', 'health', 'wellness', 'beauty']):
            return self.available_voices["female_gentle"]
        elif any(word in keyword_lower for word in ['entertainment', 'gaming', 'fun', 'trending']):
            return self.available_voices["female_energetic"]
        elif any(word in keyword_lower for word in ['education', 'tutorial', 'learning', 'guide']):
            return self.available_voices["male_narrator"]
        else:
            # Default to warm female voice
            return self.available_voices["female_warm"]

# Example usage and testing
if __name__ == "__main__":
    # Test Minimax TTS
    logging.basicConfig(level=logging.INFO)
    
    tts = MinimaxTTS()
    
    if tts.available:
        print("✅ Minimax TTS is available")
        print(f"Available voices: {list(tts.available_voices.keys())}")
        
        # Test speech generation
        test_script = {
            "title": "Test Video",
            "introduction": "Welcome to this amazing test video.",
            "key_point_1": "This is the first important point.",
            "key_point_2": "Here's the second crucial insight.",
            "key_point_3": "Finally, the third key takeaway.",
            "conclusion": "Thanks for watching this test!"
        }
        
        audio_path = tts.generate_enhanced_speech(test_script, "test_keyword")
        if audio_path:
            print(f"✅ Test audio generated: {audio_path}")
        else:
            print("❌ Test audio generation failed")
    else:
        print("❌ Minimax TTS not available")
        print("Make sure to set MINIMAX_API_KEY and MINIMAX_GROUP_ID environment variables")
