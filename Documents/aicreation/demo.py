#!/usr/bin/env python3
"""
demo.py - Demonstration script showing all improvements
"""

import sys
import time
import logging
from pathlib import Path
from enhanced_auto_youtube import EnhancedAutoYouTube

def print_banner():
    """Print a nice banner."""
    print("=" * 80)
    print("🚀 ENHANCED AUTO YOUTUBE VIDEO GENERATOR 🚀")
    print("=" * 80)
    print("✨ Professional Video Creation with AI")
    print("🎬 Blender Integration for 3D Graphics")
    print("🎵 Enhanced Audio Processing")
    print("🖼️  Beautiful Gradient Thumbnails")
    print("🤖 Ollama AI Script Generation")
    print("=" * 80)
    print()

def show_features():
    """Show system features."""
    print("🔧 SYSTEM FEATURES:")
    print("   • Modular architecture for easy maintenance")
    print("   • Robust error handling with fallback systems")
    print("   • Professional video quality with Blender")
    print("   • Enhanced audio with pydub processing")
    print("   • Comprehensive logging and monitoring")
    print("   • Configurable settings via config.py")
    print("   • Multiple AI models support (phi3, mistral, etc.)")
    print("   • Automatic thumbnail generation with gradients")
    print("   • YouTube API integration (when configured)")
    print()

def show_generated_files():
    """Show generated files."""
    output_dir = Path("output")
    
    print("📁 GENERATED FILES:")
    
    # Videos
    video_dir = output_dir / "videos"
    if video_dir.exists():
        videos = list(video_dir.glob("*.mp4"))
        print(f"   🎬 Videos ({len(videos)}):")
        for video in sorted(videos)[-5:]:  # Show last 5
            size = video.stat().st_size / 1024 / 1024  # MB
            print(f"      • {video.name} ({size:.1f} MB)")
    
    # Thumbnails
    thumb_dir = output_dir / "thumbnails"
    if thumb_dir.exists():
        thumbs = list(thumb_dir.glob("*.jpg"))
        print(f"   🖼️  Thumbnails ({len(thumbs)}):")
        for thumb in sorted(thumbs)[-5:]:  # Show last 5
            size = thumb.stat().st_size / 1024  # KB
            print(f"      • {thumb.name} ({size:.0f} KB)")
    
    # Scripts
    script_dir = output_dir / "scripts"
    if script_dir.exists():
        scripts = list(script_dir.glob("*.json"))
        print(f"   📝 Scripts ({len(scripts)}):")
        for script in sorted(scripts)[-5:]:  # Show last 5
            print(f"      • {script.name}")
    
    # Audio
    audio_dir = output_dir / "audio"
    if audio_dir.exists():
        audios = list(audio_dir.glob("*.mp3"))
        print(f"   🎵 Audio ({len(audios)}):")
        for audio in sorted(audios)[-5:]:  # Show last 5
            size = audio.stat().st_size / 1024 / 1024  # MB
            print(f"      • {audio.name} ({size:.1f} MB)")
    
    print()

def demo_single_keyword(keyword):
    """Demonstrate processing a single keyword."""
    print(f"🎯 PROCESSING KEYWORD: '{keyword}'")
    print("-" * 50)
    
    # Initialize system
    auto_youtube = EnhancedAutoYouTube()
    
    start_time = time.time()
    
    # Process keyword
    success = auto_youtube.process_keyword(keyword)
    
    elapsed_time = time.time() - start_time
    
    if success:
        print(f"✅ Successfully processed '{keyword}' in {elapsed_time:.1f} seconds")
        print(f"📁 Files saved in output/ directory")
    else:
        print(f"❌ Failed to process '{keyword}'")
    
    print()
    return success

def demo_multiple_keywords():
    """Demonstrate processing multiple keywords."""
    keywords = ["quantum computing", "sustainable agriculture", "digital art"]
    
    print(f"🎯 PROCESSING MULTIPLE KEYWORDS: {keywords}")
    print("-" * 50)
    
    auto_youtube = EnhancedAutoYouTube()
    
    success_count = 0
    total_time = 0
    
    for i, keyword in enumerate(keywords):
        print(f"Processing {i+1}/{len(keywords)}: '{keyword}'")
        
        start_time = time.time()
        success = auto_youtube.process_keyword(keyword)
        elapsed_time = time.time() - start_time
        total_time += elapsed_time
        
        if success:
            success_count += 1
            print(f"✅ Completed in {elapsed_time:.1f}s")
        else:
            print(f"❌ Failed")
        
        # Small delay between keywords
        if i < len(keywords) - 1:
            print("⏳ Waiting 10 seconds...")
            time.sleep(10)
        
        print()
    
    print(f"📊 RESULTS: {success_count}/{len(keywords)} successful")
    print(f"⏱️  Total time: {total_time:.1f} seconds")
    print()

def main():
    """Main demo function."""
    print_banner()
    show_features()
    
    if len(sys.argv) > 1:
        # Process specific keyword
        keyword = " ".join(sys.argv[1:])
        demo_single_keyword(keyword)
    else:
        # Interactive demo
        print("🎮 DEMO OPTIONS:")
        print("1. Process single keyword")
        print("2. Process multiple keywords")
        print("3. Show generated files only")
        print()
        
        choice = input("Choose option (1-3): ").strip()
        
        if choice == "1":
            keyword = input("Enter keyword: ").strip()
            if keyword:
                demo_single_keyword(keyword)
        elif choice == "2":
            demo_multiple_keywords()
        elif choice == "3":
            pass  # Just show files
        else:
            print("Invalid choice. Showing files only.")
    
    show_generated_files()
    
    print("🎉 DEMO COMPLETED!")
    print("💡 To process a specific keyword: python demo.py 'your keyword'")
    print("📖 Check the logs in output/logs/ for detailed information")
    print()

if __name__ == "__main__":
    # Setup minimal logging for demo
    logging.basicConfig(level=logging.WARNING)
    main()
