#!/usr/bin/env python3
"""
piper_tts.py - Intégration Piper TTS pour voix locales de haute qualité
"""

import os
import logging
import subprocess
import tempfile
import json
from pathlib import Path
from typing import Optional, Dict, List
import shutil

from config import get_config

logger = logging.getLogger(__name__)

class PiperTTS:
    """Gestionnaire Piper TTS pour synthèse vocale locale de haute qualité."""
    
    def __init__(self):
        """Initialize Piper TTS."""
        self.config = get_config()
        
        # Piper configuration
        self.piper_executable = self._find_piper_executable()
        self.models_dir = Path.home() / ".local" / "share" / "piper" / "models"
        
        # Voice models available for different languages and styles
        self.voice_models = {
            # French voices
            "fr": {
                "female": {
                    "gilles": "fr_FR-gilles-low.onnx",
                    "siwis": "fr_FR-siwis-medium.onnx",
                    "upmc": "fr_FR-upmc-medium.onnx"
                },
                "male": {
                    "tom": "fr_FR-tom-medium.onnx"
                }
            },
            # English voices (backup)
            "en": {
                "female": {
                    "amy": "en_US-amy-medium.onnx",
                    "ljspeech": "en_US-ljspeech-medium.onnx",
                    "lessac": "en_US-lessac-medium.onnx"
                },
                "male": {
                    "ryan": "en_US-ryan-medium.onnx",
                    "danny": "en_US-danny-low.onnx"
                }
            }
        }
        
        # Voice selection based on content
        self.content_voice_mapping = {
            "technology": {"lang": "fr", "gender": "male", "voice": "tom"},
            "science": {"lang": "fr", "gender": "male", "voice": "tom"},
            "business": {"lang": "fr", "gender": "male", "voice": "tom"},
            "health": {"lang": "fr", "gender": "female", "voice": "siwis"},
            "wellness": {"lang": "fr", "gender": "female", "voice": "siwis"},
            "nature": {"lang": "fr", "gender": "female", "voice": "gilles"},
            "education": {"lang": "fr", "gender": "female", "voice": "upmc"},
            "default": {"lang": "fr", "gender": "female", "voice": "gilles"}
        }
        
        # Check availability
        self.available = self._check_availability()
        
        if self.available:
            logger.info("✅ Piper TTS initialized successfully")
            self._ensure_models_downloaded()
        else:
            logger.warning("⚠️ Piper TTS not available")
    
    def _find_piper_executable(self) -> Optional[str]:
        """Find Piper executable."""
        # Common locations for Piper
        possible_paths = [
            "./piper_wrapper.sh",  # Wrapper script with proper environment
            "./piper/piper",  # Local installation (user moved here)
            "./piper_arm/piper",  # Local ARM version
            "/Users/<USER>/Downloads/piper/piper",  # User's download location
            "piper",  # In PATH
            "/usr/local/bin/piper",
            "/opt/homebrew/bin/piper",
            str(Path.home() / ".local" / "bin" / "piper"),
        ]

        for path in possible_paths:
            if Path(path).exists() and os.access(path, os.X_OK):
                return path
            elif shutil.which(path):
                return path

        return None
    
    def _check_availability(self) -> bool:
        """Check if Piper is available."""
        if not self.piper_executable:
            logger.warning("Piper executable not found")
            return False

        # Check if file exists and is executable
        piper_path = Path(self.piper_executable)
        if not piper_path.exists():
            logger.warning(f"Piper executable not found: {piper_path}")
            return False

        if not os.access(piper_path, os.X_OK):
            logger.warning(f"Piper executable not executable: {piper_path}")
            # Try to fix permissions
            try:
                piper_path.chmod(0o755)
                logger.info("Fixed Piper executable permissions")
            except:
                logger.error("Could not fix Piper permissions")
                return False

        # For now, assume it's available if file exists and is executable
        # We'll test actual functionality when synthesizing
        logger.info("Piper TTS executable found and appears ready")
        return True
    
    def _ensure_models_downloaded(self):
        """Ensure required voice models are downloaded."""
        try:
            # Create models directory if it doesn't exist
            self.models_dir.mkdir(parents=True, exist_ok=True)
            
            # Check for essential French models
            essential_models = [
                "fr_FR-gilles-low.onnx",  # Good quality, small size
                "fr_FR-siwis-medium.onnx"  # Higher quality
            ]
            
            missing_models = []
            for model in essential_models:
                model_path = self.models_dir / model
                if not model_path.exists():
                    missing_models.append(model)
            
            if missing_models:
                logger.warning(f"Missing Piper models: {missing_models}")
                logger.info("Download models from: https://github.com/rhasspy/piper/releases")
                logger.info(f"Place them in: {self.models_dir}")
            else:
                logger.info("✅ Essential Piper models found")
                
        except Exception as e:
            logger.error(f"Error checking models: {e}")
    
    def get_voice_for_content(self, text: str, keyword: str = "") -> Dict[str, str]:
        """Get appropriate voice based on content."""
        text_lower = text.lower()
        keyword_lower = keyword.lower()
        
        # Analyze content to select voice
        for content_type, voice_config in self.content_voice_mapping.items():
            if content_type in text_lower or content_type in keyword_lower:
                return voice_config
        
        # Default voice
        return self.content_voice_mapping["default"]
    
    def synthesize_speech(self, text: str, output_path: str, keyword: str = "") -> bool:
        """Synthesize speech using Piper TTS."""
        if not self.available:
            logger.error("Piper TTS not available")
            return False
        
        try:
            # Get appropriate voice
            voice_config = self.get_voice_for_content(text, keyword)
            
            # Get model path
            lang = voice_config["lang"]
            gender = voice_config["gender"]
            voice = voice_config["voice"]
            
            if lang not in self.voice_models or gender not in self.voice_models[lang]:
                logger.error(f"Voice configuration not found: {voice_config}")
                return False
            
            model_name = self.voice_models[lang][gender][voice]
            model_path = self.models_dir / model_name
            
            if not model_path.exists():
                logger.error(f"Model not found: {model_path}")
                logger.info("Download from: https://github.com/rhasspy/piper/releases")
                return False
            
            # Prepare text (clean and optimize for TTS)
            clean_text = self._prepare_text_for_tts(text)
            
            logger.info(f"Synthesizing with Piper: {voice} ({gender}, {lang})")
            
            # Create temporary text file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(clean_text)
                text_file = f.name
            
            try:
                # Run Piper TTS
                cmd = [
                    self.piper_executable,
                    "--model", str(model_path),
                    "--output_file", output_path
                ]
                
                # Add text via stdin
                with open(text_file, 'r') as f:
                    result = subprocess.run(cmd, stdin=f, capture_output=True, 
                                          text=True, timeout=120)
                
                if result.returncode == 0:
                    # Verify output file
                    if Path(output_path).exists() and Path(output_path).stat().st_size > 1000:
                        file_size = Path(output_path).stat().st_size
                        logger.info(f"✅ Piper TTS synthesis successful: {output_path} ({file_size} bytes)")
                        return True
                    else:
                        logger.error("Output file too small or missing")
                        return False
                else:
                    logger.error(f"Piper TTS failed: {result.stderr}")
                    return False
                    
            finally:
                # Cleanup temp file
                Path(text_file).unlink(missing_ok=True)
                
        except Exception as e:
            logger.error(f"Error in Piper TTS synthesis: {e}")
            return False
    
    def _prepare_text_for_tts(self, text: str) -> str:
        """Prepare text for optimal TTS synthesis."""
        # Remove excessive whitespace
        text = " ".join(text.split())
        
        # Add pauses for better speech rhythm
        text = text.replace(". ", ". <break time='0.5s'/> ")
        text = text.replace("! ", "! <break time='0.5s'/> ")
        text = text.replace("? ", "? <break time='0.5s'/> ")
        text = text.replace(", ", ", <break time='0.2s'/> ")
        
        # Ensure proper ending
        if not text.endswith(('.', '!', '?')):
            text += "."
        
        return text
    
    def list_available_voices(self) -> Dict[str, List[str]]:
        """List available voice models."""
        available = {}
        
        for lang, genders in self.voice_models.items():
            available[lang] = []
            for gender, voices in genders.items():
                for voice_name, model_file in voices.items():
                    model_path = self.models_dir / model_file
                    if model_path.exists():
                        available[lang].append(f"{voice_name} ({gender})")
        
        return available
    
    def get_installation_info(self) -> Dict[str, str]:
        """Get installation information and instructions."""
        return {
            "executable": self.piper_executable or "Not found",
            "models_dir": str(self.models_dir),
            "available": str(self.available),
            "install_instructions": """
# Installation Piper TTS:

## 1. Installer Piper
# macOS avec Homebrew:
brew install piper-tts

# Ou télécharger depuis:
# https://github.com/rhasspy/piper/releases

## 2. Télécharger les modèles de voix
# Créer le dossier:
mkdir -p ~/.local/share/piper/models

# Télécharger les modèles français:
cd ~/.local/share/piper/models

# Voix féminine de qualité (recommandée):
wget https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/gilles/low/fr_FR-gilles-low.onnx
wget https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/gilles/low/fr_FR-gilles-low.onnx.json

# Voix féminine haute qualité:
wget https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/siwis/medium/fr_FR-siwis-medium.onnx
wget https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/siwis/medium/fr_FR-siwis-medium.onnx.json

## 3. Tester
python test_piper.py
            """
        }

# Example usage and testing
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    piper = PiperTTS()
    
    print("🎤 PIPER TTS - SYNTHÈSE VOCALE LOCALE")
    print("=" * 50)
    
    # Show installation info
    info = piper.get_installation_info()
    print(f"📍 Exécutable: {info['executable']}")
    print(f"📁 Dossier modèles: {info['models_dir']}")
    print(f"✅ Disponible: {info['available']}")
    
    if piper.available:
        print("\n🎵 Voix disponibles:")
        voices = piper.list_available_voices()
        for lang, voice_list in voices.items():
            if voice_list:
                print(f"  {lang}: {', '.join(voice_list)}")
            else:
                print(f"  {lang}: Aucune voix trouvée")
        
        # Test synthesis
        test_text = "Bonjour ! Ceci est un test de synthèse vocale avec Piper TTS. La qualité est excellente pour un système local."
        output_path = "test_piper_output.wav"
        
        print(f"\n🧪 Test de synthèse...")
        success = piper.synthesize_speech(test_text, output_path, "test")
        
        if success:
            print(f"✅ Test réussi: {output_path}")
            # Cleanup
            Path(output_path).unlink(missing_ok=True)
        else:
            print("❌ Test échoué")
    else:
        print("\n📋 Instructions d'installation:")
        print(info['install_instructions'])
