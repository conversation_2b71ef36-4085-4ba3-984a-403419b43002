#!/usr/bin/env python3
"""
final_demo.py - Démonstration finale du système amélioré
"""

import sys
import time
import json
from pathlib import Path
from enhanced_auto_youtube import EnhancedAutoYouTube

def print_header():
    """Print demo header."""
    print("🎬" * 20)
    print("🚀 SYSTÈME AUTOMATISÉ DE CRÉATION VIDÉOS YOUTUBE 🚀")
    print("🎬" * 20)
    print()
    print("✨ NOUVELLES FONCTIONNALITÉS PROFESSIONNELLES ✨")
    print("🤖 Prompt YouTube optimisé inspiré de vidIQ & TubeBuddy")
    print("🎯 Titres clickbait avec open loops et power words")
    print("🎣 Hooks émotionnels et statistiques percutantes")
    print("📈 Scripts optimisés pour l'engagement et la rétention")
    print("🎵 Audio professionnel avec enhancement pydub")
    print("🎬 Vidéos HD avec Blender ou FFmpeg")
    print("🖼️  Miniatures avec gradients et effets visuels")
    print("⚙️  Architecture modulaire et configuration flexible")
    print()

def show_script_analysis(script_data, keyword):
    """Show detailed script analysis."""
    print(f"📝 ANALYSE DU SCRIPT GÉNÉRÉ POUR '{keyword.upper()}':")
    print("-" * 60)
    
    # Title analysis
    title = script_data.get("title", "")
    print(f"🎯 TITRE: {title}")
    print(f"   • Longueur: {len(title)} caractères")
    print(f"   • Clickbait: {'✅' if any(word in title.lower() for word in ['shocking', 'secret', 'truth', 'revealed', 'crash', '!', '?']) else '❌'}")
    print(f"   • Open loop: {'✅' if '?' in title or '!' in title else '❌'}")
    print(f"   • Power words: {'✅' if any(word in title.lower() for word in ['ultimate', 'shocking', 'secret', 'revealed', 'truth', 'exposed']) else '❌'}")
    print()
    
    # Hook analysis
    intro = script_data.get("introduction", "")
    print(f"🎣 HOOK (Introduction):")
    print(f"   {intro[:100]}...")
    print(f"   • Émotion: {'✅' if any(word in intro.lower() for word in ['imagine', 'shocking', 'did you know']) else '❌'}")
    print(f"   • Statistiques: {'✅' if any(char.isdigit() for char in intro) else '❌'}")
    print()
    
    # Content analysis
    total_words = sum(len(script_data.get(key, "").split()) for key in ["introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion"])
    print(f"📊 CONTENU:")
    print(f"   • Mots totaux: {total_words}")
    print(f"   • Structure: Introduction + 3 points clés + Conclusion")
    
    # Check for engagement elements
    conclusion = script_data.get("conclusion", "")
    print(f"   • Call-to-action: {'✅' if any(word in conclusion.lower() for word in ['subscribe', 'comment', 'like', 'share']) else '❌'}")
    print(f"   • Question finale: {'✅' if '?' in conclusion else '❌'}")
    print()

def demo_professional_generation(keyword):
    """Demonstrate professional video generation."""
    print(f"🎬 GÉNÉRATION PROFESSIONNELLE POUR: '{keyword}'")
    print("=" * 60)
    
    # Initialize system
    auto_youtube = EnhancedAutoYouTube()
    
    print("🔄 Étapes de génération:")
    print("   1. 🤖 Génération script avec Ollama (prompt YouTube optimisé)")
    print("   2. 🎵 Création audio avec gTTS + enhancement pydub")
    print("   3. 🎬 Création vidéo avec Blender (fallback FFmpeg)")
    print("   4. 🖼️  Génération miniature avec gradients")
    print("   5. ✅ Validation et sauvegarde")
    print()
    
    start_time = time.time()
    
    # Process keyword
    success = auto_youtube.process_keyword(keyword)
    
    total_time = time.time() - start_time
    
    if success:
        print(f"✅ SUCCÈS en {total_time:.1f} secondes!")
        
        # Show generated script
        script_path = Path("output/scripts") / f"{keyword.replace(' ', '_')}.json"
        if script_path.exists():
            with open(script_path, 'r', encoding='utf-8') as f:
                script_data = json.load(f)
            show_script_analysis(script_data, keyword)
        
        # Show file sizes
        video_path = Path("output/videos") / f"{keyword.replace(' ', '_')}.mp4"
        audio_path = Path("output/audio") / f"{keyword.replace(' ', '_')}.mp3"
        thumb_path = Path("output/thumbnails") / f"{keyword.replace(' ', '_')}.jpg"
        
        print("📁 FICHIERS GÉNÉRÉS:")
        if video_path.exists():
            size_mb = video_path.stat().st_size / 1024 / 1024
            print(f"   🎬 Vidéo: {video_path.name} ({size_mb:.1f} MB)")
        
        if audio_path.exists():
            size_mb = audio_path.stat().st_size / 1024 / 1024
            print(f"   🎵 Audio: {audio_path.name} ({size_mb:.1f} MB)")
        
        if thumb_path.exists():
            size_kb = thumb_path.stat().st_size / 1024
            print(f"   🖼️  Miniature: {thumb_path.name} ({size_kb:.0f} KB)")
        
        print()
        print(f"🌐 Pour voir la vidéo: file://{video_path.absolute()}")
        print(f"🖼️  Pour voir la miniature: file://{thumb_path.absolute()}")
        
    else:
        print(f"❌ ÉCHEC après {total_time:.1f} secondes")
    
    return success

def show_system_stats():
    """Show system statistics."""
    print("📊 STATISTIQUES DU SYSTÈME:")
    print("-" * 40)
    
    output_dir = Path("output")
    
    # Count files
    videos = list((output_dir / "videos").glob("*.mp4")) if (output_dir / "videos").exists() else []
    audios = list((output_dir / "audio").glob("*.mp3")) if (output_dir / "audio").exists() else []
    thumbs = list((output_dir / "thumbnails").glob("*.jpg")) if (output_dir / "thumbnails").exists() else []
    scripts = list((output_dir / "scripts").glob("*.json")) if (output_dir / "scripts").exists() else []
    
    print(f"🎬 Vidéos générées: {len(videos)}")
    print(f"🎵 Fichiers audio: {len(audios)}")
    print(f"🖼️  Miniatures: {len(thumbs)}")
    print(f"📝 Scripts: {len(scripts)}")
    
    # Calculate total sizes
    total_video_size = sum(v.stat().st_size for v in videos) / 1024 / 1024
    total_audio_size = sum(a.stat().st_size for a in audios) / 1024 / 1024
    
    print(f"💾 Taille totale vidéos: {total_video_size:.1f} MB")
    print(f"💾 Taille totale audio: {total_audio_size:.1f} MB")
    print()

def main():
    """Main demo function."""
    print_header()
    
    if len(sys.argv) > 1:
        # Demo with specific keyword
        keyword = " ".join(sys.argv[1:])
        demo_professional_generation(keyword)
    else:
        # Interactive demo
        print("🎮 MODES DE DÉMONSTRATION:")
        print("1. 🚀 Démonstration rapide (mot-clé prédéfini)")
        print("2. 🎯 Démonstration personnalisée (votre mot-clé)")
        print("3. 📊 Voir les statistiques seulement")
        print()
        
        choice = input("Choisissez une option (1-3): ").strip()
        
        if choice == "1":
            # Quick demo with trending topic
            trending_keywords = [
                "intelligence artificielle 2024",
                "crypto bull run",
                "space tourism",
                "green technology",
                "metaverse future"
            ]
            
            import random
            keyword = random.choice(trending_keywords)
            print(f"🎲 Mot-clé sélectionné: '{keyword}'")
            print()
            demo_professional_generation(keyword)
            
        elif choice == "2":
            # Custom demo
            keyword = input("🎯 Entrez votre mot-clé: ").strip()
            if keyword:
                demo_professional_generation(keyword)
            else:
                print("❌ Mot-clé vide")
        
        elif choice == "3":
            # Stats only
            pass
        
        else:
            print("❌ Choix invalide")
    
    # Always show stats at the end
    show_system_stats()
    
    print("🎉 DÉMONSTRATION TERMINÉE!")
    print("💡 Utilisation: python final_demo.py 'votre mot-clé'")
    print("📖 Documentation complète dans README.md")
    print()

if __name__ == "__main__":
    main()
