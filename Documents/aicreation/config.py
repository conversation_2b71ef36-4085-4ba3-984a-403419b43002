#!/usr/bin/env python3
"""
config.py - Configuration file for the YouTube automation system
"""

import os
from pathlib import Path
from typing import Dict, List, Any

# Base configuration
BASE_DIR = Path(__file__).parent
OUTPUT_DIR = BASE_DIR / "output"

# Directories
DIRECTORIES = {
    "videos": OUTPUT_DIR / "videos",
    "audio": OUTPUT_DIR / "audio", 
    "thumbnails": OUTPUT_DIR / "thumbnails",
    "scripts": OUTPUT_DIR / "scripts",
    "transcripts": OUTPUT_DIR / "transcripts",
    "temp": OUTPUT_DIR / "temp"
}

# Ollama configuration
OLLAMA_CONFIG = {
    "model": "phi3",  # Default model
    "available_models": ["phi3", "mistral", "qwen2.5-coder", "deepseek-r1"],
    "timeout": 300,  # 5 minutes timeout
    "max_retries": 3
}

# Video configuration
VIDEO_CONFIG = {
    "resolution": {
        "width": 1920,
        "height": 1080
    },
    "fps": 30,
    "duration": 180,  # 3 minutes in seconds
    "quality": "high",  # high, medium, low
    "format": "mp4"
}

# Audio configuration
AUDIO_CONFIG = {
    "language": "en",
    "slow": False,
    "format": "mp3",
    "quality": "high"
}

# Thumbnail configuration
THUMBNAIL_CONFIG = {
    "width": 1280,
    "height": 720,
    "quality": 95,
    "format": "JPEG"
}

# Blender configuration
BLENDER_CONFIG = {
    "executable": "/Applications/Blender.app/Contents/MacOS/Blender",
    "render_engine": "CYCLES",
    "samples": 64,  # Lower for faster rendering
    "use_gpu": True,
    "background_mode": True
}

# YouTube configuration
YOUTUBE_CONFIG = {
    "publication_hour": 18,
    "category_id": "27",  # Education
    "privacy_status": "private",
    "made_for_kids": False,
    "max_title_length": 100,
    "max_description_length": 5000
}

# Trending keywords fallback
FALLBACK_KEYWORDS = [
    "artificial intelligence",
    "climate change",
    "space exploration", 
    "renewable energy",
    "cryptocurrency",
    "virtual reality",
    "quantum computing",
    "biotechnology",
    "sustainable living",
    "digital transformation"
]

# Logging configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": OUTPUT_DIR / "logs" / "automation.log"
}

# Performance settings
PERFORMANCE_CONFIG = {
    "max_concurrent_processes": 2,
    "delay_between_keywords": 60,  # seconds
    "memory_limit_mb": 4096,
    "temp_cleanup": True
}

# Error handling
ERROR_CONFIG = {
    "max_retries": 3,
    "retry_delay": 5,  # seconds
    "fallback_enabled": True,
    "continue_on_error": True
}

def create_directories():
    """Create all necessary directories."""
    for dir_path in DIRECTORIES.values():
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # Create logs directory
    (OUTPUT_DIR / "logs").mkdir(parents=True, exist_ok=True)

def get_config() -> Dict[str, Any]:
    """Get the complete configuration."""
    return {
        "base_dir": BASE_DIR,
        "output_dir": OUTPUT_DIR,
        "directories": DIRECTORIES,
        "ollama": OLLAMA_CONFIG,
        "video": VIDEO_CONFIG,
        "audio": AUDIO_CONFIG,
        "thumbnail": THUMBNAIL_CONFIG,
        "blender": BLENDER_CONFIG,
        "youtube": YOUTUBE_CONFIG,
        "fallback_keywords": FALLBACK_KEYWORDS,
        "logging": LOGGING_CONFIG,
        "performance": PERFORMANCE_CONFIG,
        "error": ERROR_CONFIG
    }

def validate_config() -> bool:
    """Validate the configuration."""
    try:
        # Check if Blender exists
        if not os.path.exists(BLENDER_CONFIG["executable"]):
            print(f"Warning: Blender not found at {BLENDER_CONFIG['executable']}")
            return False
        
        # Create directories
        create_directories()
        
        return True
    except Exception as e:
        print(f"Configuration validation failed: {e}")
        return False

if __name__ == "__main__":
    # Test configuration
    config = get_config()
    valid = validate_config()
    print(f"Configuration valid: {valid}")
    print(f"Output directory: {config['output_dir']}")
