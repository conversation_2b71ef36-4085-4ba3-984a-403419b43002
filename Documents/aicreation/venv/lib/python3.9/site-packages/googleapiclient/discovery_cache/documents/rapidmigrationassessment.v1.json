{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://rapidmigrationassessment.googleapis.com/", "batchPath": "batch", "canonicalName": "Rapid Migration Assessment", "description": "The Rapid Migration Assessment service is our first-party migration assessment and planning tool.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/migration-center", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "rapidmigrationassessment:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://rapidmigrationassessment.mtls.googleapis.com/", "name": "rapidmigrationassessment", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "rapidmigrationassessment.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "rapidmigrationassessment.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"annotations": {"methods": {"create": {"description": "Creates an Annotation", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/annotations", "httpMethod": "POST", "id": "rapidmigrationassessment.projects.locations.annotations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the parent (project+location).", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/annotations", "request": {"$ref": "Annotation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Annotation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/annotations/{annotationsId}", "httpMethod": "GET", "id": "rapidmigrationassessment.projects.locations.annotations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/annotations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Annotation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "collectors": {"methods": {"create": {"description": "Create a Collector to manage the on-prem appliance which collects information about Customer assets.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/collectors", "httpMethod": "POST", "id": "rapidmigrationassessment.projects.locations.collectors.create", "parameterOrder": ["parent"], "parameters": {"collectorId": {"description": "Required. Id of the requesting object.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the parent (project+location).", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/collectors", "request": {"$ref": "Collector"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Collector - changes state of collector to \"Deleting\". Background jobs does final deletion through producer API.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/collectors/{collectorsId}", "httpMethod": "DELETE", "id": "rapidmigrationassessment.projects.locations.collectors.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collectors/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Collector.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/collectors/{collectorsId}", "httpMethod": "GET", "id": "rapidmigrationassessment.projects.locations.collectors.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Collector"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Collectors in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/collectors", "httpMethod": "GET", "id": "rapidmigrationassessment.projects.locations.collectors.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for ListCollectorsRequest.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/collectors", "response": {"$ref": "ListCollectorsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Collector.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/collectors/{collectorsId}", "httpMethod": "PATCH", "id": "rapidmigrationassessment.projects.locations.collectors.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "name of resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collectors/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Collector resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Collector"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "pause": {"description": "Pauses the given collector.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/collectors/{collectorsId}:pause", "httpMethod": "POST", "id": "rapidmigrationassessment.projects.locations.collectors.pause", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:pause", "request": {"$ref": "PauseCollectorRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "register": {"description": "Registers the given collector.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/collectors/{collectorsId}:register", "httpMethod": "POST", "id": "rapidmigrationassessment.projects.locations.collectors.register", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:register", "request": {"$ref": "RegisterCollectorRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resume": {"description": "Resumes the given collector.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/collectors/{collectorsId}:resume", "httpMethod": "POST", "id": "rapidmigrationassessment.projects.locations.collectors.resume", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:resume", "request": {"$ref": "ResumeCollectorRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "rapidmigrationassessment.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "rapidmigrationassessment.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "rapidmigrationassessment.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "rapidmigrationassessment.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250324", "rootUrl": "https://rapidmigrationassessment.googleapis.com/", "schemas": {"Annotation": {"description": "Message describing an Annotation", "id": "Annotation", "properties": {"createTime": {"description": "Output only. Create time stamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs.", "type": "object"}, "name": {"description": "name of resource.", "type": "string"}, "type": {"description": "Type of an annotation.", "enum": ["TYPE_UNSPECIFIED", "TYPE_LEGACY_EXPORT_CONSENT", "TYPE_QWIKLAB"], "enumDescriptions": ["Unknown type", "Indicates that this project has opted into StratoZone export.", "Indicates that this project is created by Qwiklab."], "type": "string"}, "updateTime": {"description": "Output only. Update time stamp.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "Collector": {"description": "Message describing Collector object.", "id": "Collector", "properties": {"bucket": {"description": "Output only. Store cloud storage bucket name (which is a guid) created with this Collector.", "readOnly": true, "type": "string"}, "clientVersion": {"description": "Output only. Client version.", "readOnly": true, "type": "string"}, "collectionDays": {"description": "How many days to collect data.", "format": "int32", "type": "integer"}, "createTime": {"description": "Output only. Create time stamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "User specified description of the Collector.", "type": "string"}, "displayName": {"description": "User specified name of the Collector.", "type": "string"}, "eulaUri": {"description": "Uri for EULA (End User License Agreement) from customer.", "type": "string"}, "expectedAssetCount": {"description": "User specified expected asset count.", "format": "int64", "type": "string"}, "guestOsScan": {"$ref": "GuestOsScan", "description": "Output only. Reference to MC Source Guest <PERSON>.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs.", "type": "object"}, "name": {"description": "name of resource.", "type": "string"}, "serviceAccount": {"description": "Service Account email used to ingest data to this Collector.", "type": "string"}, "state": {"description": "Output only. State of the Collector.", "enum": ["STATE_UNSPECIFIED", "STATE_INITIALIZING", "STATE_READY_TO_USE", "STATE_REGISTERED", "STATE_ACTIVE", "STATE_PAUSED", "STATE_DELETING", "STATE_DECOMMISSIONED", "STATE_ERROR"], "enumDescriptions": ["Collector state is not recognized.", "Collector started to create, but hasn't been completed MC source creation and db object creation.", "Collector has been created, MC source creation and db object creation completed.", "Collector client has been registered with client.", "Collector client is actively scanning.", "Collector is not actively scanning.", "Collector is starting background job for deletion.", "Collector completed all tasks for deletion.", "Collector is in error state."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Update time stamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vsphereScan": {"$ref": "VSphereScan", "description": "Output only. Reference to MC Source vsphere_scan.", "readOnly": true}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "GuestOsScan": {"description": "Message describing a MC Source of type Guest OS Scan.", "id": "GuestOsScan", "properties": {"coreSource": {"description": "reference to the corresponding Guest <PERSON> Scan in MC Source.", "type": "string"}}, "type": "object"}, "ListCollectorsResponse": {"description": "Message for response to listing Collectors.", "id": "ListCollectorsResponse", "properties": {"collectors": {"description": "The list of Collectors.", "items": {"$ref": "Collector"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "PauseCollectorRequest": {"description": "Message for pausing a Collector.", "id": "PauseCollectorRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "RegisterCollectorRequest": {"description": "Message for registering a Collector.", "id": "RegisterCollectorRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "ResumeCollectorRequest": {"description": "Message for resuming a Collector.", "id": "ResumeCollectorRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "VSphereScan": {"description": "Message describing a MC Source of type VSphere Scan.", "id": "VSphereScan", "properties": {"coreSource": {"description": "reference to the corresponding VSphere Scan in MC Source.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Rapid Migration Assessment API", "version": "v1", "version_module": true}