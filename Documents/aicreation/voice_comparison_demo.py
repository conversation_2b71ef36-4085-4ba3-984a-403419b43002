#!/usr/bin/env python3
"""
voice_comparison_demo.py - Démonstration comparative entre gTTS et Minimax TTS
"""

import sys
import time
import logging
from pathlib import Path
from enhanced_auto_youtube import EnhancedAutoYouTube

def print_header():
    """Print demo header."""
    print("🎤" * 20)
    print("🔊 COMPARAISON VOCALE : gTTS vs Minimax TTS 🔊")
    print("🎤" * 20)
    print()
    print("Cette démonstration compare la qualité vocale entre :")
    print("🤖 gTTS (Google Text-to-Speech) - Gratuit")
    print("✨ Minimax TTS - Premium ultra-réaliste")
    print()

def check_minimax_status():
    """Check if Minimax is available."""
    try:
        from minimax_tts import MinimaxTTS
        import os
        
        api_key = os.getenv('MINIMAX_API_KEY')
        group_id = os.getenv('MINIMAX_GROUP_ID')
        
        if api_key and group_id:
            tts = MinimaxTTS()
            return tts.available
        return False
    except:
        return False

def generate_comparison_videos(keyword):
    """Generate videos with both TTS systems for comparison."""
    print(f"🎬 GÉNÉRATION COMPARATIVE POUR: '{keyword}'")
    print("=" * 60)
    
    auto_youtube = EnhancedAutoYouTube()
    
    # Check Minimax availability
    minimax_available = check_minimax_status()
    
    print(f"🔍 Status des systèmes TTS:")
    print(f"   🤖 gTTS: ✅ Disponible")
    print(f"   ✨ Minimax: {'✅ Disponible' if minimax_available else '❌ Non configuré'}")
    print()
    
    if not minimax_available:
        print("⚠️  Minimax TTS n'est pas configuré.")
        print("📖 Consultez MINIMAX_SETUP.md pour l'installation.")
        print("🔄 Génération avec gTTS seulement...")
        print()
    
    # Generate with current system (will use Minimax if available, gTTS otherwise)
    print("🎵 Génération audio en cours...")
    start_time = time.time()
    
    success = auto_youtube.process_keyword(keyword)
    
    generation_time = time.time() - start_time
    
    if success:
        print(f"✅ Génération réussie en {generation_time:.1f} secondes")
        
        # Show file information
        video_path = Path("output/videos") / f"{keyword.replace(' ', '_')}.mp4"
        audio_path = Path("output/audio") / f"{keyword.replace(' ', '_')}.mp3"
        
        if video_path.exists() and audio_path.exists():
            video_size = video_path.stat().st_size / 1024 / 1024  # MB
            audio_size = audio_path.stat().st_size / 1024 / 1024  # MB
            
            print(f"📁 Fichiers générés:")
            print(f"   🎬 Vidéo: {video_path.name} ({video_size:.1f} MB)")
            print(f"   🎵 Audio: {audio_path.name} ({audio_size:.1f} MB)")
            print()
            
            # Show which TTS was used
            tts_used = "Minimax TTS" if minimax_available else "gTTS"
            print(f"🔊 Système TTS utilisé: {tts_used}")
            
            return True
    else:
        print(f"❌ Génération échouée après {generation_time:.1f} secondes")
        return False

def show_quality_comparison():
    """Show quality comparison information."""
    print("📊 COMPARAISON DE QUALITÉ:")
    print("-" * 40)
    print()
    
    print("🤖 gTTS (Google Text-to-Speech):")
    print("   ✅ Gratuit et illimité")
    print("   ✅ Facile à utiliser")
    print("   ✅ Stable et fiable")
    print("   ❌ Voix robotique")
    print("   ❌ Pas d'expression émotionnelle")
    print("   ❌ Choix limité de voix")
    print()
    
    print("✨ Minimax TTS:")
    print("   ✅ Voix ultra-réalistes")
    print("   ✅ Expression émotionnelle")
    print("   ✅ 300+ voix disponibles")
    print("   ✅ Clonage vocal possible")
    print("   ✅ Support multilingue avancé")
    print("   ❌ Payant (coût par utilisation)")
    print("   ❌ Nécessite configuration")
    print()

def show_usage_recommendations():
    """Show usage recommendations."""
    print("💡 RECOMMANDATIONS D'UTILISATION:")
    print("-" * 40)
    print()
    
    print("🎯 Utilisez gTTS pour:")
    print("   • Tests et développement")
    print("   • Contenus à faible budget")
    print("   • Prototypage rapide")
    print("   • Contenus informatifs simples")
    print()
    
    print("🎯 Utilisez Minimax TTS pour:")
    print("   • Contenus professionnels")
    print("   • Vidéos de haute qualité")
    print("   • Engagement maximum")
    print("   • Différenciation concurrentielle")
    print("   • Contenus émotionnels")
    print()

def demo_voice_selection():
    """Demonstrate voice selection for different topics."""
    print("🎭 SÉLECTION AUTOMATIQUE DES VOIX:")
    print("-" * 40)
    
    try:
        from minimax_tts import MinimaxTTS
        
        tts = MinimaxTTS()
        
        test_topics = [
            ("business strategy", "Stratégie d'entreprise"),
            ("artificial intelligence", "Intelligence artificielle"),
            ("health and wellness", "Santé et bien-être"),
            ("gaming review", "Test de jeu"),
            ("cooking tutorial", "Tutoriel cuisine"),
            ("breaking news", "Actualités")
        ]
        
        print("Exemples de sélection automatique de voix:")
        for topic, description in test_topics:
            voice_id = tts.set_voice_for_keyword(topic)
            print(f"   📝 '{description}' → {voice_id}")
        
        print()
        print("💡 La sélection se base sur le contenu du mot-clé")
        
    except ImportError:
        print("⚠️  Module Minimax non disponible pour la démonstration")

def main():
    """Main demo function."""
    print_header()
    
    # Check system status
    minimax_available = check_minimax_status()
    
    if len(sys.argv) > 1:
        # Demo with specific keyword
        keyword = " ".join(sys.argv[1:])
        generate_comparison_videos(keyword)
    else:
        # Interactive demo
        print("🎮 OPTIONS DE DÉMONSTRATION:")
        print("1. 🎤 Générer une vidéo de test")
        print("2. 📊 Voir la comparaison de qualité")
        print("3. 🎭 Démonstration sélection de voix")
        print("4. 💡 Recommandations d'utilisation")
        print()
        
        choice = input("Choisissez une option (1-4): ").strip()
        
        if choice == "1":
            keyword = input("🎯 Entrez votre mot-clé: ").strip()
            if keyword:
                generate_comparison_videos(keyword)
            else:
                print("❌ Mot-clé vide")
        
        elif choice == "2":
            show_quality_comparison()
        
        elif choice == "3":
            demo_voice_selection()
        
        elif choice == "4":
            show_usage_recommendations()
        
        else:
            print("❌ Choix invalide")
            return
    
    print()
    print("🎉 DÉMONSTRATION TERMINÉE!")
    print()
    print("📖 Pour configurer Minimax TTS:")
    print("   cat MINIMAX_SETUP.md")
    print()
    print("🚀 Pour tester Minimax:")
    print("   python test_minimax.py")
    print()
    print("💡 Utilisation:")
    print("   python voice_comparison_demo.py 'votre mot-clé'")

if __name__ == "__main__":
    # Setup minimal logging
    logging.basicConfig(level=logging.WARNING)
    main()
