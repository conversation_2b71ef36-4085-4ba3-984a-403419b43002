#!/usr/bin/env python3
"""
SkyReels-A1 Avatar Creator - Version Réelle
Basé sur le code officiel SkyworkAI/SkyReels-A1
"""

import torch
import os
import numpy as np
from PIL import Image
import glob
import insightface
import cv2
import subprocess
import argparse
from pathlib import Path
import time
from loguru import logger

# Imports conditionnels pour éviter les erreurs
try:
    from decord import VideoReader
    DECORD_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ decord non disponible, utilisation de cv2 pour la vidéo")
    DECORD_AVAILABLE = False

try:
    from moviepy.editor import ImageSequenceClip, AudioFileClip, VideoFileClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ moviepy non disponible")
    MOVIEPY_AVAILABLE = False

try:
    from facexlib.parsing import init_parsing_model
    from facexlib.utils.face_restoration_helper import FaceRestoreHelper
    FACEXLIB_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ facexlib non disponible")
    FACEXLIB_AVAILABLE = False

try:
    from insightface.app import FaceAnalysis
    INSIGHTFACE_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ insightface non disponible")
    INSIGHTFACE_AVAILABLE = False

try:
    from diffusers.models import AutoencoderKLCogVideoX
    from diffusers.utils import export_to_video, load_image
    from transformers import AutoModelForDepthEstimation, AutoProcessor, SiglipImageProcessor, SiglipVisionModel
    from transformers import CLIPVisionModelWithProjection, CLIPImageProcessor
    DIFFUSERS_AVAILABLE = True
except ImportError:
    logger.error("❌ diffusers/transformers non disponibles - installation requise")
    DIFFUSERS_AVAILABLE = False

# Imports SkyReels-A1 (si disponibles)
try:
    from skyreels_a1.models.transformer3d import CogVideoXTransformer3DModel
    from skyreels_a1.skyreels_a1_i2v_pipeline import SkyReelsA1ImagePoseToVideoPipeline
    from skyreels_a1.pre_process_lmk3d import FaceAnimationProcessor
    from skyreels_a1.src.media_pipe.mp_utils import LMKExtractor
    from skyreels_a1.src.media_pipe.draw_util_2d import FaceMeshVisualizer2d
    SKYREELS_AVAILABLE = True
except ImportError:
    logger.error("❌ SkyReels-A1 modules non disponibles")
    SKYREELS_AVAILABLE = False

try:
    from skyreels_a1.src.frame_interpolation import init_frame_interpolation_model, batch_images_interpolation_tool
    from skyreels_a1.src.multi_fps import multi_fps_tool
    FRAME_INTERPOLATION_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ Frame interpolation non disponible")
    FRAME_INTERPOLATION_AVAILABLE = False

try:
    import moviepy.editor as mp
    MP_AVAILABLE = True
except ImportError:
    MP_AVAILABLE = False

# DiffPoseTalk pour audio
try:
    from diffposetalk.diffposetalk import DiffPoseTalk
    DIFFPOSETALK_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ DiffPoseTalk non disponible")
    DIFFPOSETALK_AVAILABLE = False

def check_requirements():
    """Vérifie les prérequis pour SkyReels-A1"""
    logger.info("🔍 Vérification des prérequis SkyReels-A1...")
    
    requirements = {
        "PyTorch": torch.cuda.is_available() if hasattr(torch, 'cuda') else False,
        "Diffusers": DIFFUSERS_AVAILABLE,
        "SkyReels-A1": SKYREELS_AVAILABLE,
        "DiffPoseTalk": DIFFPOSETALK_AVAILABLE,
        "FaceXLib": FACEXLIB_AVAILABLE,
        "InsightFace": INSIGHTFACE_AVAILABLE,
    }
    
    all_ok = True
    for name, available in requirements.items():
        if available:
            logger.info(f"✅ {name}: Disponible")
        else:
            logger.warning(f"⚠️ {name}: Non disponible")
            if name in ["Diffusers", "SkyReels-A1"]:
                all_ok = False
    
    return all_ok

def crop_and_resize(image, height, width):
    """Recadre et redimensionne l'image"""
    image = np.array(image)
    image_height, image_width, _ = image.shape
    
    if image_height / image_width < height / width:
        croped_width = int(image_height / height * width)
        left = (image_width - croped_width) // 2
        image = image[:, left: left+croped_width]
        image = Image.fromarray(image).resize((width, height))
    else:
        pad = int((((width / height) * image_height) - image_width) / 2.)
        padded_image = np.zeros((image_height, image_width + pad * 2, 3), dtype=np.uint8)
        padded_image[:, pad:pad+image_width] = image
        image = Image.fromarray(padded_image).resize((width, height))
    
    return image

def write_mp4(video_path, samples, fps=12, audio_bitrate="192k"):
    """Écrit une vidéo MP4"""
    if MOVIEPY_AVAILABLE:
        clip = ImageSequenceClip(samples, fps=fps)
        clip.write_videofile(video_path, audio_codec="aac", audio_bitrate=audio_bitrate, 
                           ffmpeg_params=["-crf", "18", "-preset", "slow"])
    else:
        logger.error("❌ MoviePy non disponible pour écrire la vidéo")

def parse_video(driving_frames, max_frame_num, fps=25):
    """Parse les frames de la vidéo"""
    video_length = len(driving_frames)
    duration = video_length / fps
    target_times = np.arange(0, duration, 1/12)
    frame_indices = (target_times * fps).astype(np.int32)
    frame_indices = frame_indices[frame_indices < video_length]
    
    new_driving_frames = []
    for idx in frame_indices:
        new_driving_frames.append(driving_frames[idx])
        if len(new_driving_frames) >= max_frame_num - 1:
            break
    
    video_lenght_add = max_frame_num - len(new_driving_frames) - 1
    new_driving_frames = [new_driving_frames[0]]*2 + new_driving_frames[1:len(new_driving_frames)-1] + [new_driving_frames[-1]] * video_lenght_add
    
    return new_driving_frames

def exec_cmd(cmd):
    """Exécute une commande shell"""
    return subprocess.run(cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)

def add_audio_to_video(silent_video_path, audio_video_path, output_video_path):
    """Ajoute l'audio à la vidéo"""
    cmd = [
        'ffmpeg', '-y',
        '-i', f'"{silent_video_path}"',
        '-i', f'"{audio_video_path}"',
        '-map', '0:v', '-map', '1:a',
        '-c:v', 'copy', '-shortest',
        f'"{output_video_path}"'
    ]
    
    try:
        exec_cmd(' '.join(cmd))
        logger.success(f"✅ Vidéo avec audio générée: {output_video_path}")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Erreur: {e}")

def create_skyreels_avatar(image_path, audio_path, output_path="outputs_skyreels"):
    """
    Crée un avatar parlant avec SkyReels-A1
    
    Args:
        image_path: Chemin vers l'image de portrait
        audio_path: Chemin vers l'audio
        output_path: Dossier de sortie
    """
    logger.info("🎭 Création d'avatar SkyReels-A1...")
    logger.info(f"📸 Image: {image_path}")
    logger.info(f"🎤 Audio: {audio_path}")
    
    # Vérifier les prérequis
    if not check_requirements():
        logger.error("❌ Prérequis manquants pour SkyReels-A1")
        return None
    
    # Paramètres
    guidance_scale = 3.0
    seed = 43
    num_inference_steps = 10
    sample_size = [480, 720]
    max_frame_num = 49
    target_fps = 12
    weight_dtype = torch.bfloat16 if torch.cuda.is_available() else torch.float32
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    logger.info(f"💻 Device: {device}")
    logger.info(f"🔢 Dtype: {weight_dtype}")
    
    try:
        # Générateur
        generator = torch.Generator(device=device).manual_seed(seed)
        
        # Chemins des modèles
        model_name = "pretrained_models/SkyReels-A1-5B/"
        siglip_name = "pretrained_models/SkyReels-A1-5B/siglip-so400m-patch14-384"
        
        # Vérifier que les modèles existent
        if not Path(model_name).exists():
            logger.error(f"❌ Modèle non trouvé: {model_name}")
            return None
        
        # Initialiser les composants
        logger.info("🔧 Initialisation des composants...")
        
        # LMK Extractor
        lmk_extractor = LMKExtractor()
        logger.info("✅ LMK Extractor initialisé")
        
        # Face Animation Processor
        processor = FaceAnimationProcessor(checkpoint='pretrained_models/smirk/SMIRK_em1.pt')
        logger.info("✅ Face Animation Processor initialisé")
        
        # Visualiseur
        vis = FaceMeshVisualizer2d(forehead_edge=False, draw_head=False, draw_iris=False)
        logger.info("✅ Visualiseur initialisé")
        
        # Face Helper (si disponible)
        if FACEXLIB_AVAILABLE:
            face_helper = FaceRestoreHelper(
                upscale_factor=1, face_size=512, crop_ratio=(1, 1),
                det_model='retinaface_resnet50', save_ext='png', device=device
            )
            logger.info("✅ Face Helper initialisé")
        else:
            face_helper = None
            logger.warning("⚠️ Face Helper non disponible")
        
        # SigLIP Visual Encoder
        siglip = SiglipVisionModel.from_pretrained(siglip_name)
        siglip_normalize = SiglipImageProcessor.from_pretrained(siglip_name)
        logger.info("✅ SigLIP initialisé")
        
        # Frame Interpolation (si disponible)
        if FRAME_INTERPOLATION_AVAILABLE and target_fps != 12:
            frame_inter_model = init_frame_interpolation_model(
                'pretrained_models/film_net/film_net_fp16.pt', device=device
            )
            logger.info("✅ Frame Interpolation initialisé")
        else:
            frame_inter_model = None
        
        # DiffPoseTalk (si disponible)
        if DIFFPOSETALK_AVAILABLE:
            diffposetalk = DiffPoseTalk()
            logger.info("✅ DiffPoseTalk initialisé")
        else:
            logger.error("❌ DiffPoseTalk requis pour l'audio")
            return None
        
        # Modèles SkyReels-A1
        logger.info("🔧 Chargement des modèles SkyReels-A1...")
        
        transformer = CogVideoXTransformer3DModel.from_pretrained(
            model_name, subfolder="transformer"
        ).to(weight_dtype)
        logger.info("✅ Transformer chargé")
        
        vae = AutoencoderKLCogVideoX.from_pretrained(
            model_name, subfolder="vae"
        ).to(weight_dtype)
        logger.info("✅ VAE chargé")
        
        lmk_encoder = AutoencoderKLCogVideoX.from_pretrained(
            model_name, subfolder="pose_guider"
        ).to(weight_dtype)
        logger.info("✅ LMK Encoder chargé")
        
        # Pipeline principal
        pipe = SkyReelsA1ImagePoseToVideoPipeline.from_pretrained(
            model_name,
            transformer=transformer,
            vae=vae,
            lmk_encoder=lmk_encoder,
            image_encoder=siglip,
            feature_extractor=siglip_normalize,
            torch_dtype=weight_dtype
        )
        
        pipe.to(device)
        pipe.enable_model_cpu_offload()
        pipe.vae.enable_tiling()
        logger.success("✅ Pipeline SkyReels-A1 initialisé")
        
        # Traitement de l'image
        logger.info("📸 Traitement de l'image...")
        image = load_image(image=image_path)
        image = processor.crop_and_resize(image, sample_size[0], sample_size[1])
        
        # Crop du visage
        ref_image, x1, y1 = processor.face_crop(np.array(image))
        face_h, face_w, _ = ref_image.shape
        source_image = ref_image
        
        # Traitement de l'image source
        source_outputs, source_tform, image_original = processor.process_source_image(source_image)
        logger.info("✅ Image traitée")
        
        # Génération des coefficients à partir de l'audio
        logger.info("🎤 Traitement de l'audio...")
        driving_outputs = diffposetalk.infer_from_file(
            audio_path, 
            source_outputs["shape_params"].view(-1)[:100].detach().cpu().numpy()
        )
        logger.info("✅ Audio traité")
        
        # Préprocessing des landmarks 3D
        logger.info("🎯 Génération des landmarks 3D...")
        out_frames = processor.preprocess_lmk3d_from_coef(
            source_outputs, source_tform, image_original.shape, driving_outputs
        )
        out_frames = parse_video(out_frames, max_frame_num)
        logger.info("✅ Landmarks 3D générés")
        
        # Préparation des motions
        rescale_motions = np.zeros_like(image)[np.newaxis, :].repeat(48, axis=0)
        for ii in range(rescale_motions.shape[0]):
            rescale_motions[ii][y1:y1+face_h, x1:x1+face_w] = out_frames[ii]
        
        ref_image = cv2.resize(ref_image, (512, 512))
        ref_lmk = lmk_extractor(ref_image[:, :, ::-1])
        ref_img = vis.draw_landmarks_v3(
            (512, 512), (face_w, face_h), 
            ref_lmk['lmks'].astype(np.float32), normed=True
        )
        
        first_motion = np.zeros_like(np.array(image))
        first_motion[y1:y1+face_h, x1:x1+face_w] = ref_img
        first_motion = first_motion[np.newaxis, :]
        motions = np.concatenate([first_motion, rescale_motions])
        
        input_video = motions[:max_frame_num]
        
        # Préparation du visage aligné
        if face_helper:
            face_helper.clean_all()
            face_helper.read_image(np.array(image)[:, :, ::-1])
            face_helper.get_face_landmarks_5(only_center_face=True)
            face_helper.align_warp_face()
            align_face = face_helper.cropped_faces[0]
            image_face = align_face[:, :, ::-1]
        else:
            # Fallback si face_helper n'est pas disponible
            image_face = np.array(image)
        
        # Préparation de l'input vidéo
        input_video = input_video[:max_frame_num]
        motions = np.array(input_video)
        input_video = torch.from_numpy(np.array(input_video)).permute([3, 0, 1, 2]).unsqueeze(0)
        input_video = input_video / 255
        
        # Génération avec le pipeline
        logger.info("🎬 Génération de la vidéo...")
        out_samples = []
        
        with torch.no_grad():
            sample = pipe(
                image=image,
                image_face=image_face,
                control_video=input_video,
                prompt="",
                negative_prompt="",
                height=sample_size[0],
                width=sample_size[1],
                num_frames=49,
                generator=generator,
                guidance_scale=guidance_scale,
                num_inference_steps=num_inference_steps,
            )
        
        out_samples.extend(sample.frames[0])
        out_samples = out_samples[2:]  # Enlever les 2 premières frames
        
        # Sauvegarde
        save_path_name = os.path.basename(image_path).split(".")[0] + "-" + os.path.basename(audio_path).split(".")[0] + ".mp4"
        
        if not os.path.exists(output_path):
            os.makedirs(output_path, exist_ok=True)
        
        video_path = os.path.join(output_path, save_path_name.split(".")[0] + "_output.mp4")
        
        # Interpolation de frames si nécessaire
        if target_fps != 12 and frame_inter_model:
            out_samples = multi_fps_tool(out_samples, frame_inter_model, target_fps)
        
        # Export de la vidéo
        export_to_video(out_samples, video_path, fps=target_fps)
        logger.success(f"✅ Vidéo générée: {video_path}")
        
        # Ajout de l'audio
        audio_video_path = video_path.split(".")[0] + "_audio.mp4"
        add_audio_to_video(video_path, audio_path, audio_video_path)
        
        logger.success(f"🎉 Avatar SkyReels-A1 créé: {audio_video_path}")
        return audio_video_path
        
    except Exception as e:
        logger.error(f"❌ Erreur création avatar: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="SkyReels-A1 Avatar Creator")
    parser.add_argument('--image_path', type=str, 
                       default="/Users/<USER>/Documents/aicreation/assets/music/le-journaliste-parle-du-segment-d-actualite.jpg",
                       help='Chemin vers l\'image de portrait')
    parser.add_argument('--driving_audio_path', type=str, 
                       default="assets/driving_audio/journaliste_test.wav",
                       help='Chemin vers l\'audio')
    parser.add_argument('--output_path', type=str, 
                       default="outputs_skyreels_real",
                       help='Dossier de sortie')
    
    args = parser.parse_args()
    
    print("🎭 SkyReels-A1 Avatar Creator - Version Réelle")
    print("=" * 60)
    print("🎯 Création d'avatar parlant avec IA générative")
    print("🚀 Basé sur le code officiel SkyworkAI/SkyReels-A1")
    print()
    
    # Vérifier les fichiers d'entrée
    if not Path(args.image_path).exists():
        logger.error(f"❌ Image non trouvée: {args.image_path}")
        return
    
    if not Path(args.driving_audio_path).exists():
        logger.error(f"❌ Audio non trouvé: {args.driving_audio_path}")
        return
    
    # Créer l'avatar
    try:
        result = create_skyreels_avatar(
            image_path=args.image_path,
            audio_path=args.driving_audio_path,
            output_path=args.output_path
        )
        
        if result:
            print("\n🎉 AVATAR SKYREELS-A1 CRÉÉ AVEC SUCCÈS!")
            print("=" * 50)
            print(f"📁 Vidéo finale: {result}")
            print(f"🎭 Avatar: Journaliste parlant réaliste")
            print(f"🎤 Audio: Synchronisé avec mouvements de lèvres")
            print(f"🤖 IA: SkyReels-A1 + DiffPoseTalk")
            print(f"📐 Format: 480x720, 12 FPS")
            
        else:
            print("❌ ÉCHEC - Impossible de créer l'avatar")
            
    except KeyboardInterrupt:
        print("\n👋 Arrêt demandé par l'utilisateur")
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")

if __name__ == "__main__":
    main()
