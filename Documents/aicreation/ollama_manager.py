#!/usr/bin/env python3
"""
ollama_manager.py - Enhanced Ollama management for script generation
"""

import json
import logging
import subprocess
import tempfile
import time
import re
from typing import Dict, Optional, List
from pathlib import Path

from config import get_config

logger = logging.getLogger(__name__)

class OllamaManager:
    """Enhanced Ollama manager for generating video scripts."""
    
    def __init__(self):
        self.config = get_config()
        self.ollama_config = self.config["ollama"]
        self.available = self._check_availability()
        
    def _check_availability(self) -> bool:
        """Check if Ollama is available and working."""
        try:
            result = subprocess.run(
                ["/Applications/Ollama.app/Contents/Resources/ollama", "list"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                logger.info("Ollama is available and working")
                return True
            else:
                logger.warning("Ollama command failed")
                return False
        except Exception as e:
            logger.error(f"Error checking Ollama availability: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available Ollama models."""
        if not self.available:
            return []
        
        try:
            result = subprocess.run(
                ["/Applications/Ollama.app/Contents/Resources/ollama", "list"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                models = []
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        models.append(model_name)
                return models
            return []
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            return []
    
    def _create_enhanced_prompt(self, keyword: str) -> str:
        """Create an enhanced prompt using professional YouTube optimization techniques."""
        return f"""
You are an expert YouTube content strategist and scriptwriter, similar to vidIQ & TubeBuddy professionals. Your goal is to help create highly successful YouTube videos that maximize engagement, retention, and monetization.

MISSION: Create a compelling 3-minute video script about "{keyword}" that will perform exceptionally well on YouTube.

TITLE REQUIREMENTS:
- Create an almost clickbait title that's familiar and polarizing to the audience
- Use an open loop structure (create mystery, contradiction, enigma, counter-intuitive elements)
- Include power words that create emotional reactions
- Apply negativity bias when appropriate
- Make it unique and different from typical competition
- Maximum 60 characters for optimal display

SCRIPT STRUCTURE & PSYCHOLOGY:
- Hook (0-15s): MUST provoke immediate emotional reaction with a striking fact or controversial opinion
- Introduction (15-30s): Build intrigue while establishing credibility
- Key Point 1 (30s-1m10s): First major revelation with emotional impact
- Key Point 2 (1m10s-1m50s): Second revelation that builds on the first
- Key Point 3 (1m50s-2m30s): Third revelation that creates anticipation
- Conclusion (2m30s-3m): End with a punchline STRONGER than the hook to provoke maximum reaction

ENGAGEMENT TECHNIQUES:
- Use familiar yet polarizing subjects that create strong opinions
- Include rhythm in the script (repetition of first words, similar syllable counts, or rhyming endings)
- Create multiple emotional peaks throughout the video
- Use pattern interrupts to maintain attention
- Include social proof and authority signals
- End with a call-to-action that encourages comments and engagement

PSYCHOLOGICAL TRIGGERS:
- Curiosity gaps that keep viewers watching
- Social validation and FOMO (fear of missing out)
- Controversy that sparks debate in comments
- Surprising revelations that challenge common beliefs
- Personal stories that create emotional connection

FORMAT REQUIREMENTS:
Respond with valid JSON using these exact keys:
{{
    "title": "Clickbait-optimized title with open loop and power words (max 60 chars)",
    "introduction": "Hook + intro that creates immediate emotional reaction and builds intrigue (30 seconds of speech)",
    "key_point_1": "First major revelation with emotional impact and supporting details (40 seconds of speech)",
    "key_point_2": "Second revelation that builds tension and provides new perspective (40 seconds of speech)",
    "key_point_3": "Third revelation that creates maximum anticipation for the conclusion (40 seconds of speech)",
    "conclusion": "Powerful punchline stronger than the hook + engagement call-to-action (30 seconds of speech)"
}}

TARGET TOPIC: {keyword}

IMPORTANT: The script should feel like it was written by a top-performing YouTuber who understands audience psychology, retention tactics, and monetization strategies. Every sentence should serve the purpose of keeping viewers engaged until the very end.

Respond ONLY with the JSON object, no additional text or formatting.
"""
    
    def _call_ollama(self, prompt: str, model: Optional[str] = None) -> Optional[str]:
        """Call Ollama with the given prompt."""
        if not self.available:
            logger.error("Ollama is not available")
            return None
        
        model = model or self.ollama_config["model"]
        
        try:
            # Create temporary file for the prompt
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp:
                temp.write(prompt)
                temp_path = temp.name
            
            # Read the content
            with open(temp_path, 'r') as f:
                prompt_content = f.read()
            
            # Call Ollama
            cmd = ["/Applications/Ollama.app/Contents/Resources/ollama", "run", model, prompt_content]
            
            logger.info(f"Calling Ollama with model '{model}'")
            start_time = time.time()
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.ollama_config["timeout"]
            )
            
            # Clean up temp file
            Path(temp_path).unlink(missing_ok=True)
            
            elapsed_time = time.time() - start_time
            logger.info(f"Ollama call completed in {elapsed_time:.2f} seconds")
            
            if result.returncode != 0:
                logger.error(f"Ollama command failed: {result.stderr}")
                return None
            
            return result.stdout.strip()
            
        except subprocess.TimeoutExpired:
            logger.error(f"Ollama call timed out after {self.ollama_config['timeout']} seconds")
            return None
        except Exception as e:
            logger.error(f"Error calling Ollama: {e}")
            return None
    
    def _parse_json_response(self, response: str, keyword: str = "content") -> Optional[Dict]:
        """Parse JSON response from Ollama with enhanced error handling."""
        if not response:
            return None
        
        # Clean the response
        response = response.strip()
        
        # Try to extract JSON from various formats
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',  # JSON in code blocks
            r'```\s*(\{.*?\})\s*```',      # JSON in generic code blocks
            r'(\{.*?\})',                   # Direct JSON
        ]
        
        json_str = None
        for pattern in json_patterns:
            match = re.search(pattern, response, re.DOTALL)
            if match:
                json_str = match.group(1)
                break
        
        if not json_str:
            # If no JSON pattern found, try the whole response
            json_str = response
        
        # Clean up the JSON string
        json_str = json_str.strip()
        
        # Fix common JSON issues
        json_str = re.sub(r',\s*}', '}', json_str)  # Remove trailing commas
        json_str = re.sub(r',\s*]', ']', json_str)  # Remove trailing commas in arrays
        
        try:
            parsed = json.loads(json_str)
            
            # Validate required keys
            required_keys = ["title", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion"]
            missing_keys = [key for key in required_keys if key not in parsed]
            
            if missing_keys:
                logger.warning(f"Missing keys in JSON response: {missing_keys}")
                # Fill missing keys with defaults
                for key in missing_keys:
                    if key == "title":
                        parsed[key] = f"Understanding {keyword.title()}"
                    elif key == "introduction":
                        parsed[key] = f"Welcome to today's exploration of {keyword}."
                    elif key.startswith("key_point"):
                        point_num = key.split("_")[-1]
                        parsed[key] = f"Key point {point_num} about {keyword}."
                    elif key == "conclusion":
                        parsed[key] = f"Thank you for learning about {keyword} with us today!"
            
            return parsed
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed: {e}")
            logger.debug(f"Raw response: {response[:200]}...")
            
            # Try to extract content using regex as fallback
            return self._extract_content_with_regex(response, keyword)
    
    def _extract_content_with_regex(self, response: str, keyword: str = "content") -> Optional[Dict]:
        """Extract content using regex as fallback when JSON parsing fails."""
        try:
            content = {}
            
            # Extract title
            title_match = re.search(r'"title":\s*"([^"]+)"', response)
            content["title"] = title_match.group(1) if title_match else "Generated Content"
            
            # Extract introduction
            intro_match = re.search(r'"introduction":\s*"([^"]+)"', response)
            content["introduction"] = intro_match.group(1) if intro_match else "Welcome to today's content."
            
            # Extract key points
            for i in range(1, 4):
                key = f"key_point_{i}"
                pattern = rf'"{key}":\s*"([^"]+)"'
                match = re.search(pattern, response)
                content[key] = match.group(1) if match else f"Key point {i}."
            
            # Extract conclusion
            conclusion_match = re.search(r'"conclusion":\s*"([^"]+)"', response)
            content["conclusion"] = conclusion_match.group(1) if conclusion_match else "Thank you for watching!"
            
            logger.info("Successfully extracted content using regex fallback")
            return content
            
        except Exception as e:
            logger.error(f"Regex extraction failed: {e}")
            return None
    
    def generate_script(self, keyword: str) -> Optional[Dict]:
        """Generate a video script for the given keyword."""
        if not self.available:
            logger.error("Ollama is not available for script generation")
            return None
        
        prompt = self._create_enhanced_prompt(keyword)
        
        # Try with multiple models if the first one fails
        models_to_try = [self.ollama_config["model"]] + self.ollama_config["available_models"]
        models_to_try = list(dict.fromkeys(models_to_try))  # Remove duplicates
        
        for attempt, model in enumerate(models_to_try):
            try:
                logger.info(f"Attempt {attempt + 1}: Generating script with model '{model}'")
                
                response = self._call_ollama(prompt, model)
                if not response:
                    continue
                
                script = self._parse_json_response(response, keyword)
                if script:
                    logger.info(f"Successfully generated script with model '{model}'")
                    return script
                
            except Exception as e:
                logger.error(f"Error with model '{model}': {e}")
                continue
        
        logger.error("Failed to generate script with all available models")
        return None
    
    def get_fallback_script(self, keyword: str) -> Dict:
        """Generate a fallback script when Ollama is not available."""
        return {
            "title": f"Understanding {keyword.title()}: A Quick Guide",
            "introduction": f"Welcome to today's exploration of {keyword}. In this video, we'll dive deep into this fascinating topic and uncover the key insights you need to know.",
            "key_point_1": f"First, let's examine the fundamental aspects of {keyword} and why it matters in today's world.",
            "key_point_2": f"Next, we'll explore the practical applications and real-world implications of {keyword}.",
            "key_point_3": f"Finally, we'll discuss the future trends and what to expect regarding {keyword}.",
            "conclusion": f"That wraps up our comprehensive look at {keyword}. Thanks for watching, and don't forget to subscribe for more insightful content!"
        }
