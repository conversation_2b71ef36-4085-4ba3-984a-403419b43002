#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Auto YouTube Video Generator

This script automates the creation and publishing of YouTube videos based on trending topics.
It fetches trending keywords, generates scripts using Ollama, converts text to speech,
creates videos with visual elements, and uploads them to YouTube.
"""

import os
import time
import logging
import random
import json
import datetime
import math
import re
import tempfile
import shutil
from typing import List, Dict, Any, Tuple, Optional, Union
import requests
from pathlib import Path
import urllib.request

# Third-party imports
import numpy as np
import ollama
from pytrends.request import TrendReq
from gtts import gTTS
from pydub import AudioSegment
import librosa
import librosa.display
import cv2
from colorthief import ColorThief
from scipy.io import wavfile
import matplotlib.pyplot as plt
from matplotlib import cm

# MoviePy imports
from moviepy.editor import (
    TextClip, ColorClip, CompositeVideoClip, AudioFileClip,
    concatenate_videoclips, VideoFileClip, CompositeAudioClip,
    ImageClip, clips_array, vfx, afx, transfx, VideoClip
)
from moviepy.video.tools.segmenting import findObjects
from moviepy.video.io.bindings import mplfig_to_npimage

# PIL imports
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance, ImageChops

# Google API imports
import google_auth_oauthlib.flow
import googleapiclient.discovery
import googleapiclient.errors
import googleapiclient.http
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("auto_youtube.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
CATEGORIES = ["history", "economy", "news"]
OUTPUT_DIR = Path("output")
ASSETS_DIR = Path("assets")
TEMPLATES_DIR = ASSETS_DIR / "templates"
MUSIC_DIR = ASSETS_DIR / "music"
FONTS_DIR = ASSETS_DIR / "fonts"
TRANSITIONS_DIR = ASSETS_DIR / "transitions"
OVERLAYS_DIR = ASSETS_DIR / "overlays"

# API credentials
CREDENTIALS_FILE = "client_secrets.json"
TOKEN_FILE = "token.json"
SCOPES = ["https://www.googleapis.com/auth/youtube.upload",
          "https://www.googleapis.com/auth/youtube.readonly"]
OLLAMA_MODEL = "phi3:medium"  # Change to your preferred model

# Video settings
VIDEO_WIDTH = 1920
VIDEO_HEIGHT = 1080
VIDEO_FPS = 30
VIDEO_BITRATE = "8000k"
AUDIO_BITRATE = "192k"
PUBLICATION_HOUR = 18  # 6:00 PM

# Visual style
COLOR_SCHEMES = {
    "modern": {
        "background": (240, 240, 245),
        "primary": (41, 128, 185),
        "secondary": (39, 174, 96),
        "accent": (231, 76, 60),
        "text": (52, 73, 94),
        "text_light": (255, 255, 255)
    },
    "dark": {
        "background": (30, 30, 30),
        "primary": (52, 152, 219),
        "secondary": (46, 204, 113),
        "accent": (231, 76, 60),
        "text": (236, 240, 241),
        "text_light": (255, 255, 255)
    },
    "elegant": {
        "background": (245, 245, 245),
        "primary": (155, 89, 182),
        "secondary": (52, 152, 219),
        "accent": (230, 126, 34),
        "text": (44, 62, 80),
        "text_light": (236, 240, 241)
    }
}

class AutoYouTube:
    """Main class for YouTube video automation."""

    def __init__(self, style="modern", language="en"):
        """Initialize the AutoYouTube class.

        Args:
            style (str): Visual style to use ("modern", "dark", or "elegant")
            language (str): Language code for TTS and content (e.g., "en", "fr")
        """
        self.pytrends = TrendReq(hl=f'{language}-US', tz=0)
        self.youtube = None
        self.style = style
        self.language = language
        self.colors = COLOR_SCHEMES.get(style, COLOR_SCHEMES["modern"])
        self.fonts = {}
        self.transitions = []
        self.music_files = []
        self.setup_directories()
        self.load_assets()

    def setup_directories(self):
        """Create necessary directories if they don't exist."""
        # Create output directories
        OUTPUT_DIR.mkdir(exist_ok=True)
        for subdir in ["scripts", "audio", "videos", "thumbnails", "temp"]:
            (OUTPUT_DIR / subdir).mkdir(exist_ok=True)

        # Create asset directories
        ASSETS_DIR.mkdir(exist_ok=True)
        for asset_dir in [TEMPLATES_DIR, MUSIC_DIR, FONTS_DIR, TRANSITIONS_DIR, OVERLAYS_DIR]:
            asset_dir.mkdir(exist_ok=True)

    def load_assets(self):
        """Load assets like fonts, transitions, and music files."""
        # Load fonts
        try:
            for font_file in FONTS_DIR.glob("*.ttf"):
                font_name = font_file.stem
                self.fonts[font_name] = str(font_file)

            # If no fonts found, download some free fonts
            if not self.fonts:
                self.download_default_assets()

            # Load transitions
            for trans_file in TRANSITIONS_DIR.glob("*.mp4"):
                self.transitions.append(str(trans_file))

            # Load music files
            for music_file in MUSIC_DIR.glob("*.mp3"):
                self.music_files.append(str(music_file))

            logger.info(f"Loaded {len(self.fonts)} fonts, {len(self.transitions)} transitions, and {len(self.music_files)} music files")

        except Exception as e:
            logger.error(f"Error loading assets: {str(e)}")

    def download_default_assets(self):
        """Download default assets if none are available."""
        try:
            # URLs for free fonts, music, and transitions
            default_assets = {
                "fonts": [
                    ("https://github.com/google/fonts/raw/main/ofl/roboto/Roboto-Regular.ttf", "Roboto-Regular.ttf"),
                    ("https://github.com/google/fonts/raw/main/ofl/roboto/Roboto-Bold.ttf", "Roboto-Bold.ttf"),
                    ("https://github.com/google/fonts/raw/main/ofl/montserrat/Montserrat-Regular.ttf", "Montserrat-Regular.ttf"),
                    ("https://github.com/google/fonts/raw/main/ofl/montserrat/Montserrat-Bold.ttf", "Montserrat-Bold.ttf")
                ],
                "music": [
                    ("https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3", "ambient-background.mp3"),
                    ("https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3", "upbeat-background.mp3"),
                    ("https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3", "corporate-background.mp3")
                ]
            }

            # Download fonts
            for url, filename in default_assets["fonts"]:
                target_path = FONTS_DIR / filename
                if not target_path.exists():
                    logger.info(f"Downloading font: {filename}")
                    urllib.request.urlretrieve(url, target_path)
                    self.fonts[target_path.stem] = str(target_path)

            # Download music
            for url, filename in default_assets["music"]:
                target_path = MUSIC_DIR / filename
                if not target_path.exists():
                    logger.info(f"Downloading music: {filename}")
                    urllib.request.urlretrieve(url, target_path)
                    self.music_files.append(str(target_path))

            # Create simple transition effects
            self.create_default_transitions()

            logger.info("Downloaded default assets successfully")

        except Exception as e:
            logger.error(f"Error downloading default assets: {str(e)}")

    def create_default_transitions(self):
        """Create simple transition video clips."""
        try:
            # Create a fade transition
            fade_path = TRANSITIONS_DIR / "fade.mp4"
            if not fade_path.exists():
                # Create a 1-second black to transparent fade
                black_clip = ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=(0, 0, 0), duration=1)
                black_clip = black_clip.fx(vfx.fadeout, 1.0)
                black_clip.write_videofile(str(fade_path), fps=VIDEO_FPS, codec='libx264')
                self.transitions.append(str(fade_path))

            # Create a slide transition
            slide_path = TRANSITIONS_DIR / "slide.mp4"
            if not slide_path.exists():
                # Create a 1-second slide transition
                slide_clip = ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=(255, 255, 255), duration=1)
                slide_clip = slide_clip.fx(vfx.slide_out, 1.0, 'right')
                slide_clip.write_videofile(str(slide_path), fps=VIDEO_FPS, codec='libx264')
                self.transitions.append(str(slide_path))

            logger.info("Created default transitions")

        except Exception as e:
            logger.error(f"Error creating default transitions: {str(e)}")

    def authenticate_youtube(self):
        """Authenticate with YouTube API using OAuth2."""
        credentials = None

        # Load saved credentials if they exist
        if os.path.exists(TOKEN_FILE):
            credentials = Credentials.from_authorized_user_info(
                json.load(open(TOKEN_FILE))
            )

        # If credentials are invalid or don't exist, get new ones
        if not credentials or not credentials.valid:
            if credentials and credentials.expired and credentials.refresh_token:
                credentials.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    CREDENTIALS_FILE, SCOPES
                )
                credentials = flow.run_local_server(port=0)

            # Save credentials for future use
            with open(TOKEN_FILE, 'w') as token:
                token.write(credentials.to_json())

        # Build the YouTube API client
        self.youtube = googleapiclient.discovery.build(
            "youtube", "v3", credentials=credentials
        )
        logger.info("Successfully authenticated with YouTube API")

    def get_trending_keywords(self) -> List[str]:
        """Fetch trending keywords from Google Trends for specified categories."""
        trending_keywords = []

        try:
            for category in CATEGORIES:
                # Get daily trending searches
                trending_searches = self.pytrends.trending_searches(pn='united_states')
                # Filter by category using realtime_trending_searches
                self.pytrends.build_payload(kw_list=[category])

                # Get top 5 keywords for each category
                for keyword in trending_searches.iloc[:5, 0].tolist():
                    if keyword not in trending_keywords:
                        trending_keywords.append(keyword)

            logger.info(f"Found {len(trending_keywords)} trending keywords")
            return trending_keywords[:5]  # Limit to top 5 overall

        except Exception as e:
            logger.error(f"Error fetching trending keywords: {str(e)}")
            return ["history facts", "economic trends", "current events"]  # Fallback keywords

    def generate_script(self, keyword: str) -> Dict[str, Any]:
        """Generate a professional video script using Ollama.

        Args:
            keyword: The trending keyword to create a script about

        Returns:
            A dictionary containing the structured script
        """
        # Research the keyword first to get more context
        research = self.research_topic(keyword)

        prompt = f"""
        Create a professional 3-minute video script about "{keyword}" with the following structure:

        1. Title: Create an engaging, click-worthy title that will attract viewers (max 60 characters)

        2. Hook: A powerful 10-15 second opening hook that immediately grabs attention

        3. Introduction: Brief introduction to the topic that establishes credibility (20-30 seconds)

        4. Main Content: Three key points about the topic with supporting details (2 minutes total):
           - Key Point 1: First important aspect with specific facts/examples
           - Key Point 2: Second important aspect with specific facts/examples
           - Key Point 3: Third important aspect with specific facts/examples

        5. Conclusion: A concise conclusion with a clear takeaway (20-30 seconds)

        6. Call to Action: Encourage viewers to like, subscribe, and comment

        Additional context about the topic:
        {research}

        Format the response as a JSON with the following keys:
        "title", "hook", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion", "call_to_action"

        Make the script conversational, engaging, and include specific facts and statistics where appropriate.
        Use short sentences and simple language. Aim for a script that would take exactly 3 minutes to narrate at a normal pace.
        """

        try:
            # Get response from Ollama
            response = ollama.chat(model=OLLAMA_MODEL, messages=[
                {'role': 'user', 'content': prompt}
            ])

            # Extract JSON from response
            content = response['message']['content']

            # Find JSON content (might be wrapped in markdown code blocks)
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_str = content.split("```")[1].strip()
            else:
                json_str = content

            # Parse JSON and add metadata
            script = json.loads(json_str)

            # Add metadata
            script["keyword"] = keyword
            script["language"] = self.language
            script["style"] = self.style
            script["created_at"] = datetime.datetime.now().isoformat()

            # Calculate estimated duration for each section
            word_counts = {
                "hook": len(script.get("hook", "").split()),
                "introduction": len(script.get("introduction", "").split()),
                "key_point_1": len(script.get("key_point_1", "").split()),
                "key_point_2": len(script.get("key_point_2", "").split()),
                "key_point_3": len(script.get("key_point_3", "").split()),
                "conclusion": len(script.get("conclusion", "").split()),
                "call_to_action": len(script.get("call_to_action", "").split())
            }

            # Estimate 150 words per minute for speech
            durations = {k: v / 150 * 60 for k, v in word_counts.items()}
            script["estimated_durations"] = durations
            script["total_duration"] = sum(durations.values())

            # Save script to file
            script_path = OUTPUT_DIR / "scripts" / f"{keyword.replace(' ', '_')}.json"
            with open(script_path, 'w', encoding='utf-8') as f:
                json.dump(script, f, ensure_ascii=False, indent=4)

            logger.info(f"Generated professional script for '{keyword}' with {sum(word_counts.values())} words")
            return script

        except Exception as e:
            logger.error(f"Error generating script for '{keyword}': {str(e)}")
            # Return a basic script as fallback
            return {
                "title": f"Understanding {keyword.title()} - A Complete Guide",
                "hook": f"Did you know that {keyword} is one of the most searched topics today? Let's explore why.",
                "introduction": f"Welcome to this video about {keyword}. Today we'll explore the key aspects that make this topic so important.",
                "key_point_1": f"First, let's look at the history and background of {keyword}.",
                "key_point_2": f"Next, we'll examine the current trends and developments related to {keyword}.",
                "key_point_3": f"Finally, we'll discuss the future implications and why {keyword} matters to you.",
                "conclusion": f"To summarize, {keyword} is a fascinating topic with important implications for our daily lives.",
                "call_to_action": "If you found this information valuable, please like this video and subscribe to our channel for more content like this. Share your thoughts in the comments below!",
                "keyword": keyword,
                "language": self.language,
                "style": self.style,
                "created_at": datetime.datetime.now().isoformat(),
                "estimated_durations": {
                    "hook": 10,
                    "introduction": 20,
                    "key_point_1": 40,
                    "key_point_2": 40,
                    "key_point_3": 40,
                    "conclusion": 20,
                    "call_to_action": 10
                },
                "total_duration": 180
            }

    def research_topic(self, keyword: str) -> str:
        """Research a topic to provide context for script generation.

        Args:
            keyword: The topic to research

        Returns:
            A string containing research information
        """
        try:
            # Use pytrends to get related queries
            self.pytrends.build_payload([keyword], timeframe='today 12-m')
            related_queries = self.pytrends.related_queries()

            # Extract top related queries
            top_queries = []
            if keyword in related_queries and 'top' in related_queries[keyword]:
                top_df = related_queries[keyword]['top']
                if not top_df.empty:
                    top_queries = top_df['query'].tolist()[:5]

            # Get interest over time
            interest_over_time = self.pytrends.interest_over_time()

            # Determine if topic is trending up or down
            trend_direction = "stable"
            if not interest_over_time.empty and keyword in interest_over_time.columns:
                values = interest_over_time[keyword].values
                if len(values) > 2:
                    first_half = values[:len(values)//2]
                    second_half = values[len(values)//2:]
                    first_avg = sum(first_half) / len(first_half)
                    second_avg = sum(second_half) / len(second_half)

                    if second_avg > first_avg * 1.2:
                        trend_direction = "trending up"
                    elif second_avg < first_avg * 0.8:
                        trend_direction = "trending down"

            # Format research results
            research = f"Topic: {keyword}\n\n"
            research += f"Current trend: This topic is {trend_direction}.\n\n"

            if top_queries:
                research += "Related queries:\n"
                for query in top_queries:
                    research += f"- {query}\n"

            # Try to get some additional context from Ollama
            try:
                context_prompt = f"Provide 3-5 key facts about '{keyword}' that would be useful for creating an educational video. Include specific statistics or data points if relevant."
                context_response = ollama.chat(model=OLLAMA_MODEL, messages=[
                    {'role': 'user', 'content': context_prompt}
                ])
                research += f"\nAdditional context:\n{context_response['message']['content']}"
            except:
                pass

            return research

        except Exception as e:
            logger.warning(f"Error researching topic '{keyword}': {str(e)}")
            return f"Topic: {keyword}\n\nNo additional research data available."

    def text_to_speech(self, script: Dict[str, Any], keyword: str) -> Dict[str, str]:
        """Convert script to professional speech using gTTS and enhance audio quality.

        Args:
            script: The script dictionary with all sections
            keyword: The keyword for the video

        Returns:
            Dictionary with paths to audio files for each section and the combined audio
        """
        try:
            # Create a directory for individual audio segments
            segments_dir = OUTPUT_DIR / "audio" / "segments" / f"{keyword.replace(' ', '_')}"
            segments_dir.mkdir(exist_ok=True, parents=True)

            # Define script sections to convert to speech
            sections = ["hook", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion", "call_to_action"]

            # Generate audio for each section
            audio_segments = {}
            for section in sections:
                if section in script and script[section]:
                    section_text = script[section]
                    section_path = segments_dir / f"{section}.mp3"

                    # Convert text to speech
                    tts = gTTS(text=section_text, lang=self.language, slow=False)
                    tts.save(str(section_path))

                    # Store the path
                    audio_segments[section] = str(section_path)

                    logger.info(f"Generated audio for section '{section}'")

            # Combine all audio segments with appropriate pauses
            combined_audio = self.combine_audio_segments(audio_segments, script, keyword)

            # Enhance audio quality
            enhanced_audio = self.enhance_audio_quality(combined_audio, keyword)

            # Return paths to all audio files
            result = {
                "segments": audio_segments,
                "combined": combined_audio,
                "enhanced": enhanced_audio
            }

            logger.info(f"Completed audio generation for '{keyword}'")
            return result

        except Exception as e:
            logger.error(f"Error generating audio for '{keyword}': {str(e)}")
            # Create a fallback audio file
            try:
                fallback_text = f"This is a video about {keyword}. Due to technical issues, the full narration could not be generated."
                fallback_path = OUTPUT_DIR / "audio" / f"{keyword.replace(' ', '_')}_fallback.mp3"
                tts = gTTS(text=fallback_text, lang=self.language, slow=False)
                tts.save(str(fallback_path))
                return {"enhanced": str(fallback_path)}
            except:
                return {"enhanced": None}

    def combine_audio_segments(self, segments: Dict[str, str], script: Dict[str, Any], keyword: str) -> str:
        """Combine audio segments with appropriate pauses.

        Args:
            segments: Dictionary of audio segment paths
            script: The script dictionary
            keyword: The keyword for the video

        Returns:
            Path to the combined audio file
        """
        try:
            # Create output path
            combined_path = OUTPUT_DIR / "audio" / f"{keyword.replace(' ', '_')}_combined.mp3"

            # Define the order of segments
            segment_order = ["hook", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion", "call_to_action"]

            # Create a silent segment for pauses (500ms)
            silence = AudioSegment.silent(duration=500)

            # Initialize combined audio
            combined = AudioSegment.empty()

            # Add each segment with pauses
            for section in segment_order:
                if section in segments:
                    # Load the audio segment
                    segment_audio = AudioSegment.from_mp3(segments[section])

                    # Add a pause before key points
                    if section.startswith("key_point"):
                        combined += silence

                    # Add the segment
                    combined += segment_audio

                    # Add a pause after each segment
                    combined += silence

            # Export the combined audio
            combined.export(str(combined_path), format="mp3", bitrate=AUDIO_BITRATE)

            logger.info(f"Combined audio segments for '{keyword}'")
            return str(combined_path)

        except Exception as e:
            logger.error(f"Error combining audio segments: {str(e)}")
            # If combining fails, return the first available segment
            for segment in segments.values():
                return segment
            return None

    def enhance_audio_quality(self, audio_path: str, keyword: str) -> str:
        """Enhance audio quality for professional sound.

        Args:
            audio_path: Path to the audio file to enhance
            keyword: The keyword for the video

        Returns:
            Path to the enhanced audio file
        """
        if not audio_path:
            return None

        try:
            # Create output path
            enhanced_path = OUTPUT_DIR / "audio" / f"{keyword.replace(' ', '_')}_enhanced.mp3"

            # Load the audio
            audio = AudioSegment.from_mp3(audio_path)

            # Apply audio enhancements
            # 1. Normalize volume
            normalized = audio.normalize()

            # 2. Apply compression (reduce dynamic range)
            # This is a simple implementation - professional audio would use more sophisticated compression
            def compress_dynamic_range(audio, threshold=-20.0, ratio=4.0):
                """Apply compression to reduce dynamic range."""
                compressed = AudioSegment.empty()
                chunk_size = 100  # ms
                for i in range(0, len(audio), chunk_size):
                    chunk = audio[i:i+chunk_size]
                    if chunk.dBFS < threshold:
                        # Apply compression ratio
                        gain = (threshold - chunk.dBFS) * (1 - 1/ratio)
                        chunk = chunk + gain
                    compressed += chunk
                return compressed

            compressed = compress_dynamic_range(normalized)

            # 3. Apply slight EQ boost for voice clarity
            # This would typically be done with a proper audio processing library
            # Here we're using a simple approximation by boosting mid-range frequencies

            # 4. Add subtle reverb for professional sound
            # This would typically be done with a proper audio processing library

            # Export the enhanced audio
            compressed.export(str(enhanced_path), format="mp3", bitrate=AUDIO_BITRATE)

            logger.info(f"Enhanced audio quality for '{keyword}'")
            return str(enhanced_path)

        except Exception as e:
            logger.error(f"Error enhancing audio: {str(e)}")
            return audio_path  # Return original if enhancement fails

    def create_video(self, script: Dict[str, Any], audio_paths: Dict[str, str], keyword: str) -> str:
        """Create a professional video with dynamic visuals and animations.

        Args:
            script: The script dictionary with all sections
            audio_paths: Dictionary with paths to audio files
            keyword: The keyword for the video

        Returns:
            Path to the created video file
        """
        try:
            # Use the enhanced audio if available
            audio_path = audio_paths.get("enhanced") or audio_paths.get("combined")
            if not audio_path:
                logger.error(f"No audio file available for '{keyword}'")
                return None

            # Load audio file
            audio_clip = AudioFileClip(audio_path)
            duration = audio_clip.duration

            # Get color scheme based on style
            colors = self.colors

            # Create a more professional video with sections
            video_clips = []

            # 1. Create intro sequence
            intro_clip = self.create_intro_sequence(script, keyword, duration * 0.15)  # 15% of total duration
            video_clips.append(intro_clip)

            # 2. Create main content
            main_clip = self.create_main_content(script, keyword, duration * 0.75)  # 75% of total duration
            video_clips.append(main_clip)

            # 3. Create outro sequence
            outro_clip = self.create_outro_sequence(script, keyword, duration * 0.1)  # 10% of total duration
            video_clips.append(outro_clip)

            # Concatenate all clips
            final_video = concatenate_videoclips(video_clips)

            # Add audio
            final_video = final_video.set_audio(audio_clip)

            # Add background music
            final_video = self.add_background_music(final_video, duration)

            # Apply final effects (color grading, etc.)
            final_video = self.apply_video_effects(final_video)

            # Save the video
            video_path = OUTPUT_DIR / "videos" / f"{keyword.replace(' ', '_')}.mp4"
            final_video.write_videofile(
                str(video_path),
                fps=VIDEO_FPS,
                codec='libx264',
                audio_codec='aac',
                bitrate=VIDEO_BITRATE,
                threads=4
            )

            logger.info(f"Created professional video for '{keyword}'")
            return str(video_path)

        except Exception as e:
            logger.error(f"Error creating video for '{keyword}': {str(e)}")
            return None

    def create_intro_sequence(self, script: Dict[str, Any], keyword: str, duration: float) -> VideoClip:
        """Create an engaging intro sequence.

        Args:
            script: The script dictionary
            keyword: The keyword for the video
            duration: Duration of the intro in seconds

        Returns:
            VideoClip object for the intro
        """
        try:
            colors = self.colors
            bg_color = colors["background"]

            # Create background
            background = ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=bg_color, duration=duration)

            # Create title with animation
            title_text = script.get('title', f"Understanding {keyword}")
            title_font = self.get_font("bold", 80)

            title_clip = TextClip(
                title_text,
                fontsize=80,
                color=colors["primary"],
                font=title_font,
                size=(VIDEO_WIDTH - 200, None),
                method='caption',
                align='center'
            )

            # Animate title (fade in and slight zoom)
            animated_title = title_clip.set_position(('center', 'center'))
            animated_title = animated_title.set_duration(duration)
            animated_title = animated_title.fx(vfx.fadein, duration * 0.3)

            # Add hook text below title
            hook_text = script.get('hook', f"Discover the fascinating world of {keyword}")
            hook_font = self.get_font("regular", 40)

            hook_clip = TextClip(
                hook_text,
                fontsize=40,
                color=colors["secondary"],
                font=hook_font,
                size=(VIDEO_WIDTH - 300, None),
                method='caption',
                align='center'
            )

            # Position hook below title with animation
            hook_y_position = int(VIDEO_HEIGHT * 0.6)
            animated_hook = hook_clip.set_position(('center', hook_y_position))
            animated_hook = animated_hook.set_duration(duration)
            animated_hook = animated_hook.fx(vfx.fadein, duration * 0.5)  # Fade in slower than title

            # Combine clips
            intro_clip = CompositeVideoClip([background, animated_title, animated_hook])

            return intro_clip

        except Exception as e:
            logger.error(f"Error creating intro sequence: {str(e)}")
            # Return a simple fallback clip
            return ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=(0, 0, 0), duration=duration)

    def create_main_content(self, script: Dict[str, Any], keyword: str, duration: float) -> VideoClip:
        """Create the main content section with key points.

        Args:
            script: The script dictionary
            keyword: The keyword for the video
            duration: Duration of the main content in seconds

        Returns:
            VideoClip object for the main content
        """
        try:
            colors = self.colors
            bg_color = colors["background"]

            # Create background with subtle pattern or gradient
            background = self.create_background_with_pattern(duration)

            # Calculate durations for each section
            intro_duration = duration * 0.15
            key_points_duration = duration * 0.7
            conclusion_duration = duration * 0.15

            # Create clips list
            clips = [background]

            # Introduction section
            intro_text = script.get('introduction', f"Let's explore {keyword} in detail.")
            intro_font = self.get_font("regular", 36)

            intro_clip = TextClip(
                intro_text,
                fontsize=36,
                color=colors["text"],
                font=intro_font,
                size=(VIDEO_WIDTH - 400, None),
                method='caption',
                align='center'
            ).set_position(('center', 'center'))

            intro_clip = intro_clip.set_start(0).set_duration(intro_duration)
            intro_clip = intro_clip.fx(vfx.fadein, 0.5).fx(vfx.fadeout, 0.5)
            clips.append(intro_clip)

            # Key points section - create individual clips for each key point
            key_points = [
                script.get('key_point_1', f"First important aspect of {keyword}."),
                script.get('key_point_2', f"Second important aspect of {keyword}."),
                script.get('key_point_3', f"Third important aspect of {keyword}.")
            ]

            point_duration = key_points_duration / 3
            key_point_font = self.get_font("bold", 40)

            for i, point_text in enumerate(key_points):
                # Create title for the key point
                point_title = f"Key Point {i+1}"
                title_clip = TextClip(
                    point_title,
                    fontsize=50,
                    color=colors["primary"],
                    font=key_point_font,
                    size=(VIDEO_WIDTH - 400, None),
                    method='caption',
                    align='center'
                ).set_position(('center', VIDEO_HEIGHT * 0.3))

                # Create content for the key point
                content_clip = TextClip(
                    point_text,
                    fontsize=36,
                    color=colors["text"],
                    font=self.get_font("regular", 36),
                    size=(VIDEO_WIDTH - 500, None),
                    method='caption',
                    align='center'
                ).set_position(('center', VIDEO_HEIGHT * 0.5))

                # Set timing for this key point
                start_time = intro_duration + (i * point_duration)

                # Add animations
                animated_title = title_clip.set_start(start_time).set_duration(point_duration)
                animated_title = animated_title.fx(vfx.fadein, 0.5).fx(vfx.fadeout, 0.5)

                animated_content = content_clip.set_start(start_time).set_duration(point_duration)
                animated_content = animated_content.fx(vfx.fadein, 0.7).fx(vfx.fadeout, 0.7)

                clips.append(animated_title)
                clips.append(animated_content)

            # Conclusion section
            conclusion_text = script.get('conclusion', f"In conclusion, {keyword} is an important topic worth understanding.")
            conclusion_font = self.get_font("italic", 38)

            conclusion_clip = TextClip(
                conclusion_text,
                fontsize=38,
                color=colors["text"],
                font=conclusion_font,
                size=(VIDEO_WIDTH - 400, None),
                method='caption',
                align='center'
            ).set_position(('center', 'center'))

            conclusion_start = intro_duration + key_points_duration
            conclusion_clip = conclusion_clip.set_start(conclusion_start).set_duration(conclusion_duration)
            conclusion_clip = conclusion_clip.fx(vfx.fadein, 0.5).fx(vfx.fadeout, 0.5)
            clips.append(conclusion_clip)

            # Combine all clips
            main_content = CompositeVideoClip(clips, size=(VIDEO_WIDTH, VIDEO_HEIGHT))

            return main_content

        except Exception as e:
            logger.error(f"Error creating main content: {str(e)}")
            # Return a simple fallback clip
            return ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=(240, 240, 240), duration=duration)

    def create_outro_sequence(self, script: Dict[str, Any], keyword: str, duration: float) -> VideoClip:
        """Create a professional outro sequence with call to action.

        Args:
            script: The script dictionary
            keyword: The keyword for the video
            duration: Duration of the outro in seconds

        Returns:
            VideoClip object for the outro
        """
        try:
            colors = self.colors
            bg_color = colors["background"]

            # Create background
            background = ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=bg_color, duration=duration)

            # Add call to action text
            cta_text = script.get('call_to_action', "Like, subscribe, and share your thoughts in the comments below!")

            # Create call to action clip
            cta_clip = TextClip(
                cta_text,
                fontsize=42,
                color=colors["accent"],
                size=(VIDEO_WIDTH - 400, None),
                method='caption',
                align='center'
            ).set_position(('center', 'center'))

            # Add animation
            animated_cta = cta_clip.set_duration(duration)
            animated_cta = animated_cta.fx(vfx.fadein, 0.3)

            # Combine clips
            outro_clip = CompositeVideoClip([background, animated_cta])

            return outro_clip

        except Exception as e:
            logger.error(f"Error creating outro sequence: {str(e)}")
            # Return a simple fallback clip
            return ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=(0, 0, 0), duration=duration)

    def get_font(self, style: str, size: int) -> str:
        """Get a font path based on style and size.

        Args:
            style: Font style ("regular", "bold", "italic")
            size: Font size

        Returns:
            Path to the font file or None
        """
        # Map style names to likely font file names
        style_map = {
            "regular": ["Regular", "Normal", "Medium"],
            "bold": ["Bold", "Heavy", "Black"],
            "italic": ["Italic", "Oblique"],
            "bold_italic": ["BoldItalic", "BlackOblique"]
        }

        # Try to find a matching font
        if style in style_map and self.fonts:
            # Look for fonts containing the style name
            for font_name, font_path in self.fonts.items():
                for style_term in style_map[style]:
                    if style_term.lower() in font_name.lower():
                        return font_path

            # If no specific match, return the first font
            return next(iter(self.fonts.values()))

        # Return None if no fonts available
        return None

    def create_background_with_pattern(self, duration: float) -> VideoClip:
        """Create a professional background with subtle pattern or gradient.

        Args:
            duration: Duration of the background clip

        Returns:
            VideoClip with the background
        """
        try:
            colors = self.colors
            bg_color = colors["background"]

            # Create a base background
            background = ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=bg_color, duration=duration)

            # Try to add a subtle pattern or gradient
            # This is a simple implementation - professional videos would use more complex patterns

            # Create a simple gradient overlay
            def make_gradient_frame(t):
                """Create a frame with a gradient."""
                # Create a gradient image
                img = np.zeros((VIDEO_HEIGHT, VIDEO_WIDTH, 3), dtype=np.uint8)

                # Fill with background color
                img[:, :] = bg_color

                # Add a subtle gradient
                for y in range(VIDEO_HEIGHT):
                    # Calculate gradient intensity (0-20)
                    intensity = int(20 * y / VIDEO_HEIGHT)

                    # Apply gradient to each row
                    if isinstance(bg_color, tuple) and len(bg_color) == 3:
                        r, g, b = bg_color
                        img[y, :] = (
                            min(r + intensity, 255),
                            min(g + intensity, 255),
                            min(b + intensity, 255)
                        )

                return img

            # Create a gradient clip
            gradient_clip = VideoClip(make_frame=make_gradient_frame, duration=duration)

            # Combine with base background
            final_bg = CompositeVideoClip([background, gradient_clip.set_opacity(0.3)])

            return final_bg

        except Exception as e:
            logger.error(f"Error creating background pattern: {str(e)}")
            # Return a simple background as fallback
            return ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT), color=bg_color, duration=duration)

    def add_background_music(self, video: VideoClip, duration: float) -> VideoClip:
        """Add background music to the video.

        Args:
            video: The video clip to add music to
            duration: Duration of the video

        Returns:
            Video clip with background music
        """
        try:
            # Check if we have music files
            if not self.music_files:
                return video

            # Select a random music file
            music_path = random.choice(self.music_files)

            # Load the music
            music = AudioFileClip(music_path)

            # Loop the music if it's shorter than the video
            if music.duration < duration:
                # Calculate how many loops we need
                loops = math.ceil(duration / music.duration)
                # Concatenate the music with itself
                looped_music = concatenate_videoclips([music] * loops)
                # Trim to the exact duration
                music = looped_music.subclip(0, duration)
            else:
                # Trim the music to match the video duration
                music = music.subclip(0, duration)

            # Lower the volume of the music
            music = music.volumex(0.15)  # 15% of original volume

            # Get the original audio
            original_audio = video.audio

            # Combine the audio tracks
            if original_audio:
                final_audio = CompositeAudioClip([original_audio, music])
                return video.set_audio(final_audio)
            else:
                return video.set_audio(music)

        except Exception as e:
            logger.error(f"Error adding background music: {str(e)}")
            return video  # Return original video if music addition fails

    def apply_video_effects(self, video: VideoClip) -> VideoClip:
        """Apply professional effects to the video.

        Args:
            video: The video clip to enhance

        Returns:
            Enhanced video clip
        """
        try:
            # Apply subtle effects to make the video look more professional

            # 1. Add a slight fade in and fade out
            video = video.fx(vfx.fadein, 1.0)
            video = video.fx(vfx.fadeout, 1.0)

            # 2. Add a subtle vignette effect (darkened corners)
            # This would typically be done with more sophisticated effects

            # 3. Apply color grading
            # This would typically be done with more sophisticated effects

            return video

        except Exception as e:
            logger.error(f"Error applying video effects: {str(e)}")
            return video  # Return original video if effects fail

    def create_thumbnail(self, script: Dict[str, Any], keyword: str) -> str:
        """Generate a professional eye-catching thumbnail with the title.

        Args:
            script: The script dictionary
            keyword: The keyword for the video

        Returns:
            Path to the created thumbnail file
        """
        try:
            # Create a blank image with dimensions 1280x720 (YouTube thumbnail size)
            width, height = 1280, 720

            # Use color scheme from the selected style
            colors = self.colors
            primary_color = colors["primary"]
            accent_color = colors["accent"]
            text_color = colors["text_light"]

            # Try to create a more visually appealing background
            try:
                # Method 1: Try to find a relevant image online (simplified implementation)
                # In a real implementation, you would use a proper image search API
                background_image = None

                # Method 2: Create a gradient background if no image is available
                if not background_image:
                    # Create gradient background
                    background = Image.new('RGB', (width, height), colors["background"])

                    # Create a gradient overlay
                    gradient = Image.new('L', (width, 1), 0)
                    for x in range(width):
                        # Create a gradient from left to right
                        gradient.putpixel((x, 0), int(255 * x / width))

                    # Resize the gradient to full height
                    gradient = gradient.resize((width, height))

                    # Create a colored gradient using the primary and accent colors
                    r1, g1, b1 = primary_color
                    r2, g2, b2 = accent_color

                    r_grad = Image.new('L', (width, height), 0)
                    g_grad = Image.new('L', (width, height), 0)
                    b_grad = Image.new('L', (width, height), 0)

                    # Fill the gradient channels
                    for y in range(height):
                        for x in range(width):
                            grad_val = gradient.getpixel((x, y))
                            factor = grad_val / 255.0
                            r_grad.putpixel((x, y), int(r1 + (r2 - r1) * factor))
                            g_grad.putpixel((x, y), int(g1 + (g2 - g1) * factor))
                            b_grad.putpixel((x, y), int(b1 + (b1 - b1) * factor))

                    # Merge the channels
                    background = Image.merge('RGB', (r_grad, g_grad, b_grad))

                    # Add some noise for texture
                    for y in range(height):
                        for x in range(width):
                            if random.random() < 0.05:  # 5% of pixels
                                noise = random.randint(-20, 20)
                                r, g, b = background.getpixel((x, y))
                                background.putpixel((x, y), (
                                    max(0, min(255, r + noise)),
                                    max(0, min(255, g + noise)),
                                    max(0, min(255, b + noise))
                                ))
            except Exception as e:
                logger.warning(f"Error creating fancy background: {str(e)}")
                # Fallback to simple background
                background = Image.new('RGB', (width, height), colors["background"])

            # Add a semi-transparent overlay to ensure text readability
            overlay = Image.new('RGBA', (width, height), (0, 0, 0, 128))  # 50% transparent black
            background = Image.alpha_composite(background.convert('RGBA'), overlay)
            background = background.convert('RGB')

            # Create drawing context
            draw = ImageDraw.Draw(background)

            # Try to load fonts
            title_font = self.get_font("bold", 80) or None
            subtitle_font = self.get_font("regular", 40) or None

            # Fall back to default fonts if needed
            if not title_font or not subtitle_font:
                try:
                    title_font = ImageFont.truetype("Arial Bold.ttf", 80)
                    subtitle_font = ImageFont.truetype("Arial.ttf", 40)
                except IOError:
                    title_font = ImageFont.load_default()
                    subtitle_font = title_font

            # Add title text
            title = script.get('title', f"Understanding {keyword.title()}")

            # Limit title length
            if len(title) > 40:
                title = title[:37] + "..."

            # Calculate text position to center it
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            title_height = title_bbox[3] - title_bbox[1]

            title_position = ((width - title_width) // 2, (height - title_height) // 2 - 50)

            # Draw title with shadow and glow effects
            # Shadow
            shadow_offset = 4
            shadow_color = (0, 0, 0)
            for offset in range(1, shadow_offset + 1):
                draw.text((title_position[0] + offset, title_position[1] + offset),
                         title, font=title_font, fill=shadow_color)

            # Main text
            draw.text(title_position, title, font=title_font, fill=text_color)

            # Add subtitle or hook
            subtitle = script.get('hook', f"Discover the fascinating world of {keyword}")
            if len(subtitle) > 60:
                subtitle = subtitle[:57] + "..."

            subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
            subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]

            subtitle_position = ((width - subtitle_width) // 2, title_position[1] + title_height + 40)

            # Draw subtitle with shadow
            draw.text((subtitle_position[0] + 2, subtitle_position[1] + 2),
                     subtitle, font=subtitle_font, fill=(0, 0, 0))
            draw.text(subtitle_position, subtitle, font=subtitle_font, fill=accent_color)

            # Add visual elements

            # 1. Add a border with the accent color
            border_width = 12
            for i in range(border_width):
                draw.rectangle(
                    [(i, i), (width - i - 1, height - i - 1)],
                    outline=accent_color
                )

            # 2. Add corner accents
            corner_size = 80
            corner_width = 8

            # Top-left corner
            for i in range(corner_width):
                draw.line([(i, i), (i, corner_size)], fill=primary_color, width=1)
                draw.line([(i, i), (corner_size, i)], fill=primary_color, width=1)

            # Top-right corner
            for i in range(corner_width):
                draw.line([(width-1-i, i), (width-1-i, corner_size)], fill=primary_color, width=1)
                draw.line([(width-1-i, i), (width-1-corner_size, i)], fill=primary_color, width=1)

            # Bottom-left corner
            for i in range(corner_width):
                draw.line([(i, height-1-i), (i, height-1-corner_size)], fill=primary_color, width=1)
                draw.line([(i, height-1-i), (corner_size, height-1-i)], fill=primary_color, width=1)

            # Bottom-right corner
            for i in range(corner_width):
                draw.line([(width-1-i, height-1-i), (width-1-i, height-1-corner_size)], fill=primary_color, width=1)
                draw.line([(width-1-i, height-1-i), (width-1-corner_size, height-1-i)], fill=primary_color, width=1)

            # 3. Add a play button icon to suggest video content
            play_button_size = 60
            play_button_center = (width - 100, height - 100)

            # Draw play button circle
            draw.ellipse(
                [
                    (play_button_center[0] - play_button_size, play_button_center[1] - play_button_size),
                    (play_button_center[0] + play_button_size, play_button_center[1] + play_button_size)
                ],
                outline=accent_color,
                fill=(0, 0, 0, 128)
            )

            # Draw play triangle
            play_triangle = [
                (play_button_center[0] - play_button_size//3, play_button_center[1] - play_button_size//2),
                (play_button_center[0] + play_button_size//2, play_button_center[1]),
                (play_button_center[0] - play_button_size//3, play_button_center[1] + play_button_size//2)
            ]
            draw.polygon(play_triangle, fill=accent_color)

            # Apply final image enhancements
            background = ImageEnhance.Contrast(background).enhance(1.2)  # Increase contrast
            background = ImageEnhance.Brightness(background).enhance(1.1)  # Slightly increase brightness

            # Save the thumbnail
            thumbnail_path = OUTPUT_DIR / "thumbnails" / f"{keyword.replace(' ', '_')}.jpg"
            background.save(str(thumbnail_path), quality=95)

            logger.info(f"Created professional thumbnail for '{keyword}'")
            return str(thumbnail_path)

        except Exception as e:
            logger.error(f"Error creating thumbnail for '{keyword}': {str(e)}")
            return None

    def upload_to_youtube(self, video_path: str, thumbnail_path: str,
                         script: Dict[str, str], keyword: str) -> str:
        """Upload video to YouTube and schedule it for publication."""
        if not self.youtube:
            self.authenticate_youtube()

        try:
            # Prepare video metadata
            title = f"{script['title']} - Quick Explanation"
            if len(title) > 100:  # YouTube title limit
                title = title[:97] + "..."

            description = (
                f"{script['introduction']}\n\n"
                f"Key Points:\n"
                f"1. {script['key_point_1']}\n"
                f"2. {script['key_point_2']}\n"
                f"3. {script['key_point_3']}\n\n"
                f"{script['conclusion']}\n\n"
                f"#Trending #{keyword.replace(' ', '')} #Explanation"
            )

            # Generate tags
            tags = [keyword] + keyword.split() + ["explanation", "quick", "trending"]
            tags = list(set([tag.lower() for tag in tags if len(tag) > 2]))[:15]  # Limit to 15 unique tags

            # Set scheduled publication time (18:00 today or tomorrow)
            now = datetime.datetime.now()
            publish_time = now.replace(hour=PUBLICATION_HOUR, minute=0, second=0, microsecond=0)

            # If it's already past publication time, schedule for tomorrow
            if now > publish_time:
                publish_time = publish_time + datetime.timedelta(days=1)

            # Convert to RFC3339 format
            publish_time_rfc3339 = publish_time.isoformat() + "Z"

            # Prepare the request body
            body = {
                "snippet": {
                    "title": title,
                    "description": description,
                    "tags": tags,
                    "categoryId": "27"  # Education category
                },
                "status": {
                    "privacyStatus": "private",  # Start as private
                    "publishAt": publish_time_rfc3339,
                    "selfDeclaredMadeForKids": False
                }
            }

            # Upload the video
            logger.info(f"Starting upload for '{keyword}'")

            # Create upload request
            request = self.youtube.videos().insert(
                part=",".join(body.keys()),
                body=body,
                media_body=googleapiclient.http.MediaFileUpload(
                    video_path,
                    resumable=True
                )
            )

            # Execute the upload
            response = None
            while response is None:
                status, response = request.next_chunk()
                if status:
                    logger.info(f"Uploaded {int(status.progress() * 100)}%")

            video_id = response["id"]
            logger.info(f"Video upload complete. Video ID: {video_id}")

            # Upload thumbnail
            if thumbnail_path:
                self.youtube.thumbnails().set(
                    videoId=video_id,
                    media_body=googleapiclient.http.MediaFileUpload(thumbnail_path)
                ).execute()
                logger.info(f"Thumbnail uploaded for video ID: {video_id}")

            # Return the video URL
            return f"https://www.youtube.com/watch?v={video_id}"

        except googleapiclient.errors.HttpError as e:
            error_content = json.loads(e.content)
            logger.error(f"YouTube API error: {error_content['error']['message']}")
            return None
        except Exception as e:
            logger.error(f"Error uploading video for '{keyword}': {str(e)}")
            return None

    def optimize_metadata(self, video_id: str, keyword: str) -> bool:
        """Analyze competitors and optimize video metadata."""
        if not self.youtube:
            self.authenticate_youtube()

        try:
            # Search for top videos related to the keyword
            search_response = self.youtube.search().list(
                q=keyword,
                part="id,snippet",
                maxResults=10,
                type="video",
                relevanceLanguage="en",
                safeSearch="none",
                order="viewCount"  # Sort by view count to get popular videos
            ).execute()

            # Extract video IDs
            video_ids = [item["id"]["videoId"] for item in search_response["items"]]

            if not video_ids:
                logger.warning(f"No competitor videos found for '{keyword}'")
                return False

            # Get detailed video information
            videos_response = self.youtube.videos().list(
                id=",".join(video_ids),
                part="snippet,statistics"
            ).execute()

            # Extract tags and descriptions from successful videos
            all_tags = []
            descriptions = []

            for item in videos_response["items"]:
                # Only consider videos with significant views
                view_count = int(item["statistics"].get("viewCount", 0))
                if view_count > 1000:
                    snippet = item["snippet"]
                    if "tags" in snippet:
                        all_tags.extend(snippet["tags"])
                    descriptions.append(snippet.get("description", ""))

            # Find most common tags
            tag_counter = {}
            for tag in all_tags:
                tag_counter[tag] = tag_counter.get(tag, 0) + 1

            # Sort tags by frequency
            sorted_tags = sorted(tag_counter.items(), key=lambda x: x[1], reverse=True)
            optimized_tags = [tag for tag, _ in sorted_tags[:15]]

            # Add the original keyword if not present
            if keyword not in optimized_tags:
                optimized_tags.insert(0, keyword)

            # Extract common phrases from descriptions
            common_phrases = []
            for desc in descriptions:
                # Simple extraction of phrases (could be improved with NLP)
                lines = desc.split("\n")
                for line in lines:
                    if len(line) > 20 and len(line) < 100:  # Reasonable phrase length
                        common_phrases.append(line)

            # Get current video metadata
            current_video = self.youtube.videos().list(
                id=video_id,
                part="snippet"
            ).execute()

            if not current_video["items"]:
                logger.error(f"Could not retrieve metadata for video ID: {video_id}")
                return False

            current_snippet = current_video["items"][0]["snippet"]

            # Update video with optimized metadata
            update_body = {
                "id": video_id,
                "snippet": {
                    "title": current_snippet["title"],
                    "categoryId": current_snippet["categoryId"],
                    "description": current_snippet["description"],
                    "tags": optimized_tags
                }
            }

            # Add a few common phrases to the description if available
            if common_phrases:
                additional_desc = "\n\nRelated Information:\n"
                for i, phrase in enumerate(random.sample(common_phrases, min(3, len(common_phrases)))):
                    additional_desc += f"- {phrase}\n"

                update_body["snippet"]["description"] += additional_desc

            # Update the video
            self.youtube.videos().update(
                part="snippet",
                body=update_body
            ).execute()

            logger.info(f"Metadata optimized for video ID: {video_id}")
            return True

        except googleapiclient.errors.HttpError as e:
            error_content = json.loads(e.content)
            logger.error(f"YouTube API error during optimization: {error_content['error']['message']}")
            return False
        except Exception as e:
            logger.error(f"Error optimizing metadata for '{keyword}': {str(e)}")
            return False

    def process_keyword(self, keyword: str) -> bool:
        """Process a single keyword through the entire professional video pipeline."""
        try:
            logger.info(f"Processing keyword: '{keyword}'")

            # 1. Generate professional script with research
            logger.info(f"Generating script for '{keyword}'...")
            script = self.generate_script(keyword)
            if not script:
                logger.error(f"Failed to generate script for '{keyword}'")
                return False

            # 2. Convert script to professional speech with enhanced audio
            logger.info(f"Converting script to speech for '{keyword}'...")
            audio_paths = self.text_to_speech(script, keyword)
            if not audio_paths or "enhanced" not in audio_paths:
                logger.error(f"Failed to generate audio for '{keyword}'")
                return False

            # 3. Create professional video with animations and effects
            logger.info(f"Creating professional video for '{keyword}'...")
            video_path = self.create_video(script, audio_paths, keyword)
            if not video_path:
                logger.error(f"Failed to create video for '{keyword}'")
                return False

            # 4. Create eye-catching thumbnail
            logger.info(f"Creating thumbnail for '{keyword}'...")
            thumbnail_path = self.create_thumbnail(script, keyword)
            if not thumbnail_path:
                logger.warning(f"Failed to create thumbnail for '{keyword}', proceeding without it")

            # 5. Upload to YouTube with optimized metadata
            logger.info(f"Uploading video for '{keyword}' to YouTube...")
            video_url = self.upload_to_youtube(video_path, thumbnail_path, script, keyword)
            if not video_url:
                logger.error(f"Failed to upload video for '{keyword}'")
                return False

            # 6. Extract video ID and optimize metadata
            video_id = video_url.split("=")[-1]
            logger.info(f"Optimizing metadata for video ID: {video_id}")
            self.optimize_metadata(video_id, keyword)

            # 7. Log success and return video URL
            logger.info(f"Successfully processed '{keyword}'")
            logger.info(f"Video URL: {video_url}")

            return True

        except Exception as e:
            logger.error(f"Error processing keyword '{keyword}': {str(e)}")
            return False

def main():
    """Main function to run the YouTube automation process."""
    try:
        # Initialize the AutoYouTube class
        auto_yt = AutoYouTube()

        # Run continuously
        while True:
            try:
                # Get trending keywords
                keywords = auto_yt.get_trending_keywords()
                logger.info(f"Found {len(keywords)} trending keywords: {keywords}")

                # Process each keyword
                for keyword in keywords:
                    success = auto_yt.process_keyword(keyword)
                    if success:
                        logger.info(f"Successfully processed '{keyword}'")
                    else:
                        logger.warning(f"Failed to process '{keyword}'")

                    # Add a delay between processing keywords to avoid rate limits
                    time.sleep(60)  # 1 minute delay

                # Wait for 24 hours before the next batch
                logger.info("Completed processing all keywords. Waiting for 24 hours...")
                time.sleep(24 * 60 * 60)  # 24 hours in seconds

            except Exception as e:
                logger.error(f"Error in main loop: {str(e)}")
                logger.info("Waiting for 1 hour before retrying...")
                time.sleep(60 * 60)  # 1 hour in seconds

    except KeyboardInterrupt:
        logger.info("Process interrupted by user. Exiting...")

if __name__ == "__main__":
    main()