#!/usr/bin/env python3
"""
openai_tts.py - OpenAI Text-to-Speech integration for high-quality voices
"""

import os
import logging
from pathlib import Path
from typing import Dict, Optional
import tempfile

from config import get_config

logger = logging.getLogger(__name__)

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI library not available. Install with: pip install openai")

class OpenAITTS:
    """OpenAI Text-to-Speech manager for high-quality voice generation."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize OpenAI TTS client."""
        self.config = get_config()
        
        if not OPENAI_AVAILABLE:
            self.available = False
            return
        
        # API credentials
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        
        if not self.api_key:
            logger.warning("OpenAI API key not provided")
            self.available = False
            return
        
        # Initialize OpenAI client
        try:
            self.client = OpenAI(api_key=self.api_key)
            self.available = True
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            self.available = False
            return
        
        # Available voices
        self.available_voices = {
            "alloy": "alloy",      # Balanced, neutral
            "echo": "echo",        # Male, clear
            "fable": "fable",      # British accent, storytelling
            "onyx": "onyx",        # Deep male, authoritative
            "nova": "nova",        # Young female, energetic
            "shimmer": "shimmer"   # Soft female, gentle
        }
        
        # Models available
        self.models = {
            "tts-1": "tts-1",      # Standard quality, faster
            "tts-1-hd": "tts-1-hd" # High definition, slower
        }
        
        if self.available:
            logger.info("OpenAI TTS initialized successfully")
    
    def generate_speech(self, text: str, voice: str = "alloy", 
                       model: str = "tts-1-hd") -> Optional[bytes]:
        """Generate speech from text using OpenAI TTS."""
        if not self.available:
            logger.error("OpenAI TTS not available")
            return None
        
        try:
            logger.info(f"Generating speech with OpenAI TTS (voice: {voice}, model: {model})")
            
            response = self.client.audio.speech.create(
                model=model,
                voice=voice,
                input=text,
                response_format="mp3"
            )
            
            logger.info("Successfully generated speech with OpenAI TTS")
            return response.content
            
        except Exception as e:
            logger.error(f"Error generating speech with OpenAI: {e}")
            return None
    
    def set_voice_for_keyword(self, keyword: str) -> str:
        """Select appropriate voice based on keyword."""
        keyword_lower = keyword.lower()
        
        # Voice selection logic based on content type
        if any(word in keyword_lower for word in ['business', 'finance', 'professional']):
            return "onyx"  # Deep, authoritative
        elif any(word in keyword_lower for word in ['technology', 'ai', 'science']):
            return "echo"  # Clear, technical
        elif any(word in keyword_lower for word in ['lifestyle', 'health', 'wellness']):
            return "shimmer"  # Soft, gentle
        elif any(word in keyword_lower for word in ['entertainment', 'gaming', 'fun']):
            return "nova"  # Energetic, young
        elif any(word in keyword_lower for word in ['education', 'tutorial', 'story']):
            return "fable"  # Storytelling, clear
        else:
            return "alloy"  # Balanced, neutral
    
    def generate_enhanced_speech(self, script: Dict[str, str], keyword: str, 
                                voice: Optional[str] = None) -> Optional[str]:
        """Generate enhanced speech from script using OpenAI TTS."""
        try:
            # Create speech text from script
            speech_parts = []
            
            # Helper function to extract text
            def extract_text(value):
                if isinstance(value, str):
                    return value
                elif isinstance(value, dict):
                    return value.get("text", str(value))
                else:
                    return str(value)
            
            # Build speech with natural pauses
            intro = extract_text(script.get("introduction", ""))
            if intro:
                speech_parts.append(f"{intro}")
            
            for i in range(1, 4):
                key = f"key_point_{i}"
                point = script.get(key, "")
                if point:
                    point_text = extract_text(point)
                    speech_parts.append(f"{point_text}")
            
            conclusion = extract_text(script.get("conclusion", ""))
            if conclusion:
                speech_parts.append(conclusion)
            
            # Join with natural pauses
            full_text = " ... ".join(speech_parts)
            
            # OpenAI TTS has a 4096 character limit
            if len(full_text) > 4000:
                full_text = full_text[:4000] + "..."
                logger.warning("Text truncated to fit OpenAI character limit")
            
            # Select voice if not provided
            if not voice:
                voice = self.set_voice_for_keyword(keyword)
            
            # Generate speech with HD model
            audio_data = self.generate_speech(full_text, voice, "tts-1-hd")
            
            if not audio_data:
                logger.error("Failed to generate speech with OpenAI")
                return None
            
            # Save audio file
            output_path = self.config["directories"]["audio"] / f"{keyword.replace(' ', '_')}.mp3"
            
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            logger.info(f"Enhanced speech generated with OpenAI: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error generating enhanced speech: {e}")
            return None

# Example usage and testing
if __name__ == "__main__":
    # Test OpenAI TTS
    logging.basicConfig(level=logging.INFO)
    
    tts = OpenAITTS()
    
    if tts.available:
        print("✅ OpenAI TTS is available")
        print(f"Available voices: {list(tts.available_voices.keys())}")
        print(f"Available models: {list(tts.models.keys())}")
        
        # Test speech generation
        test_script = {
            "title": "OpenAI TTS Test Video",
            "introduction": "Welcome to this test of OpenAI's advanced text-to-speech technology.",
            "key_point_1": "This is the first point demonstrating high-quality voice synthesis.",
            "key_point_2": "Here's the second point showing natural speech patterns.",
            "key_point_3": "Finally, the third point highlighting clarity and expression.",
            "conclusion": "Thanks for listening to this OpenAI TTS demonstration!"
        }
        
        audio_path = tts.generate_enhanced_speech(test_script, "test_keyword")
        if audio_path:
            print(f"✅ Test audio generated: {audio_path}")
        else:
            print("❌ Test audio generation failed")
    else:
        print("❌ OpenAI TTS not available")
        if not OPENAI_AVAILABLE:
            print("Install OpenAI library: pip install openai")
        else:
            print("Set OPENAI_API_KEY environment variable")
            print("Get your API key at: https://platform.openai.com/")
