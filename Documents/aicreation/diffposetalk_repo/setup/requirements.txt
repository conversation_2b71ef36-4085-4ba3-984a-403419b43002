# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
_libgcc_mutex=0.1=conda_forge
_openmp_mutex=4.5=2_gnu
audioread=3.0.1=pypi_0
boost=1.85.0=hb563948_4
bzip2=1.0.8=h4bc722e_7
c-ares=1.32.3=h4bc722e_0
ca-certificates=2024.7.4=hbcca054_0
certifi=2024.7.4=pypi_0
cffi=1.17.0=pypi_0
charset-normalizer=3.3.2=pypi_0
chumpy=0.70=pypi_0
cmake=3.30.2=hf8c4bd3_0
contourpy=1.1.1=pypi_0
cycler=0.12.1=pypi_0
decorator=5.1.1=pypi_0
eigen=3.4.0=h00ab1b0_0
filelock=3.15.4=pypi_0
fonttools=4.53.1=pypi_0
freetype-py=2.4.0=pypi_0
fsspec=2024.6.1=pypi_0
huggingface-hub=0.24.5=pypi_0
icu=75.1=he02047a_0
idna=3.7=pypi_0
imageio=2.34.2=pypi_0
importlib-metadata=8.2.0=pypi_0
importlib-resources=6.4.0=pypi_0
jinja2=3.1.4=pypi_0
joblib=1.4.2=pypi_0
keyutils=1.6.1=h166bdaf_0
kiwisolver=1.4.5=pypi_0
krb5=1.21.3=h659f571_0
lazy-loader=0.4=pypi_0
ld_impl_linux-64=2.38=h1181459_1
libblas=3.9.0=23_linux64_openblas
libboost=1.85.0=h0ccab89_4
libboost-devel=1.85.0=h00ab1b0_4
libboost-headers=1.85.0=ha770c72_4
libboost-python=1.85.0=py38haa4b4a7_4
libboost-python-devel=1.85.0=py38hb563948_4
libcblas=3.9.0=23_linux64_openblas
libcurl=8.9.1=hdb1bdb2_0
libedit=3.1.20191231=he28a2e2_2
libev=4.33=hd590300_2
libexpat=2.6.2=h59595ed_0
libffi=3.4.4=h6a678d5_1
libgcc-ng=14.1.0=h77fa898_0
libgfortran-ng=14.1.0=h69a702a_0
libgfortran5=14.1.0=hc5f4f2c_0
libgomp=14.1.0=h77fa898_0
liblapack=3.9.0=23_linux64_openblas
libnghttp2=1.58.0=h47da74e_1
libnsl=2.0.1=hd590300_0
libopenblas=0.3.27=pthreads_hac2b453_1
librosa=0.10.2.post1=pypi_0
libsqlite=3.46.0=hde9e2c9_0
libssh2=1.11.0=h0841786_0
libstdcxx-ng=14.1.0=hc0a3c3a_0
libuuid=2.38.1=h0b41bf4_0
libuv=1.48.0=hd590300_0
libxcrypt=4.4.36=hd590300_1
libzlib=1.3.1=h4ab18f5_1
llvmlite=0.41.1=pypi_0
markupsafe=2.1.5=pypi_0
matplotlib=3.7.5=pypi_0
mesh=1.0.0a1=pypi_0
mpmath=1.3.0=pypi_0
msgpack=1.0.8=pypi_0
ncurses=6.5=h59595ed_0
networkx=3.1=pypi_0
numba=0.58.1=pypi_0
numpy=1.24.4=py38h59b608b_0
nvidia-cublas-cu12=12.1.3.1=pypi_0
nvidia-cuda-cupti-cu12=12.1.105=pypi_0
nvidia-cuda-nvrtc-cu12=12.1.105=pypi_0
nvidia-cuda-runtime-cu12=12.1.105=pypi_0
nvidia-cudnn-cu12=********=pypi_0
nvidia-cufft-cu12=*********=pypi_0
nvidia-curand-cu12=**********=pypi_0
nvidia-cusolver-cu12=**********=pypi_0
nvidia-cusparse-cu12=**********=pypi_0
nvidia-nccl-cu12=2.20.5=pypi_0
nvidia-nvjitlink-cu12=12.6.20=pypi_0
nvidia-nvtx-cu12=12.1.105=pypi_0
opencv-contrib-python=*********=pypi_0
opencv-python=*********=pypi_0
openssl=3.3.1=h4bc722e_2
packaging=24.1=pypi_0
pillow=10.4.0=pypi_0
pip=24.0=py38h06a4308_0
platformdirs=4.2.2=pypi_0
pooch=1.8.2=pypi_0
psbody-mesh=0.4=pypi_0
pycparser=2.22=pypi_0
pyglet=2.0.16=pypi_0
pyopengl=3.1.7=pypi_0
pyparsing=3.1.2=pypi_0
pyrender=0.1.45=pypi_0
python=3.8.19=hd12c33a_0_cpython
python-dateutil=2.9.0.post0=pypi_0
python_abi=3.8=4_cp38
pyyaml=6.0.2=pypi_0
pyzmq=26.1.0=pypi_0
readline=8.2=h5eee18b_0
regex=2024.7.24=pypi_0
requests=2.32.3=pypi_0
rhash=1.4.4=hd590300_0
safetensors=0.4.4=pypi_0
scikit-learn=1.3.2=pypi_0
scipy=1.10.1=pypi_0
setuptools=72.1.0=py38h06a4308_0
six=1.16.0=pypi_0
soundfile=0.12.1=pypi_0
soxr=0.3.7=pypi_0
sqlite=3.46.0=h6d4b2fc_0
sympy=1.13.1=pypi_0
threadpoolctl=3.5.0=pypi_0
tk=8.6.13=noxft_h4845f30_101
tokenizers=0.19.1=pypi_0
torch=2.4.0=pypi_0
tqdm=4.66.5=pypi_0
transformers=4.44.0=pypi_0
trimesh=4.4.3=pypi_0
triton=3.0.0=pypi_0
typing-extensions=4.12.2=pypi_0
urllib3=2.2.2=pypi_0
wheel=0.43.0=py38h06a4308_0
xz=5.4.6=h5eee18b_1
zipp=3.19.2=pypi_0
zlib=1.3.1=h4ab18f5_1
zstd=1.5.6=ha6fb4c9_0
