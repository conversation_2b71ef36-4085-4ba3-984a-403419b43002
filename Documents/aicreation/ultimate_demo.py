#!/usr/bin/env python3
"""
ultimate_demo.py - Démonstration ultime du système complet avec Minimax TTS
"""

import sys
import time
import os
from pathlib import Path
from enhanced_auto_youtube import EnhancedAutoYouTube

def print_ultimate_header():
    """Print ultimate demo header."""
    print("🚀" * 25)
    print("🎬 SYSTÈME ULTIME DE CRÉATION VIDÉOS YOUTUBE 🎬")
    print("🚀" * 25)
    print()
    print("✨ FONCTIONNALITÉS PREMIUM INTÉGRÉES ✨")
    print("🤖 Ollama AI - Scripts optimisés YouTube (vidIQ/TubeBuddy)")
    print("🎤 Minimax TTS - Voix ultra-réalistes avec IA")
    print("🎬 Blender 3D - Vidéos professionnelles")
    print("🖼️  Miniatures - Gradients et effets visuels")
    print("⚙️  Architecture - Modulaire et robuste")
    print()

def check_system_status():
    """Check all system components."""
    print("🔍 VÉRIFICATION DU SYSTÈME:")
    print("-" * 40)
    
    status = {}
    
    # Check Ollama
    try:
        from ollama_manager import OllamaManager
        ollama = OllamaManager()
        status['ollama'] = ollama.available
        print(f"🤖 Ollama AI: {'✅ Disponible' if status['ollama'] else '❌ Non disponible'}")
    except:
        status['ollama'] = False
        print("🤖 Ollama AI: ❌ Erreur")
    
    # Check Minimax TTS
    try:
        from minimax_tts import MinimaxTTS
        api_key = os.getenv('MINIMAX_API_KEY')
        group_id = os.getenv('MINIMAX_GROUP_ID')
        
        if api_key and group_id:
            tts = MinimaxTTS()
            status['minimax'] = tts.available
            print(f"🎤 Minimax TTS: {'✅ Configuré' if status['minimax'] else '⚠️  Credentials invalides'}")
        else:
            status['minimax'] = False
            print("🎤 Minimax TTS: ⚠️  Non configuré (utilise gTTS)")
    except:
        status['minimax'] = False
        print("🎤 Minimax TTS: ❌ Erreur")
    
    # Check Blender
    try:
        import subprocess
        from config import get_config
        config = get_config()
        blender_path = config["blender"]["executable"]
        
        if os.path.exists(blender_path):
            result = subprocess.run([blender_path, "--version"], 
                                  capture_output=True, text=True, timeout=5)
            status['blender'] = result.returncode == 0
            print(f"🎬 Blender 3D: {'✅ Disponible' if status['blender'] else '⚠️  Erreur'}")
        else:
            status['blender'] = False
            print("🎬 Blender 3D: ⚠️  Non trouvé (utilise FFmpeg)")
    except:
        status['blender'] = False
        print("🎬 Blender 3D: ❌ Erreur")
    
    # Check other components
    try:
        from gtts import gTTS
        status['gtts'] = True
        print("🔊 gTTS: ✅ Disponible")
    except:
        status['gtts'] = False
        print("🔊 gTTS: ❌ Non disponible")
    
    try:
        from PIL import Image
        status['pil'] = True
        print("🖼️  PIL (Thumbnails): ✅ Disponible")
    except:
        status['pil'] = False
        print("🖼️  PIL (Thumbnails): ❌ Non disponible")
    
    print()
    return status

def show_quality_levels(status):
    """Show quality levels based on available components."""
    print("📊 NIVEAUX DE QUALITÉ DISPONIBLES:")
    print("-" * 40)
    
    if status.get('minimax') and status.get('blender'):
        print("🌟 NIVEAU PREMIUM MAXIMUM:")
        print("   🎤 Voix ultra-réalistes (Minimax)")
        print("   🎬 Vidéos 3D professionnelles (Blender)")
        print("   🤖 Scripts IA optimisés (Ollama)")
        print("   🖼️  Miniatures premium")
        quality_level = "PREMIUM MAX"
    
    elif status.get('minimax'):
        print("⭐ NIVEAU PREMIUM AUDIO:")
        print("   🎤 Voix ultra-réalistes (Minimax)")
        print("   🎬 Vidéos HD (FFmpeg)")
        print("   🤖 Scripts IA optimisés (Ollama)")
        print("   🖼️  Miniatures premium")
        quality_level = "PREMIUM AUDIO"
    
    elif status.get('blender'):
        print("🎬 NIVEAU PREMIUM VIDÉO:")
        print("   🔊 Voix améliorées (gTTS + pydub)")
        print("   🎬 Vidéos 3D professionnelles (Blender)")
        print("   🤖 Scripts IA optimisés (Ollama)")
        print("   🖼️  Miniatures premium")
        quality_level = "PREMIUM VIDÉO"
    
    else:
        print("✅ NIVEAU STANDARD:")
        print("   🔊 Voix améliorées (gTTS + pydub)")
        print("   🎬 Vidéos HD (FFmpeg)")
        print("   🤖 Scripts IA optimisés (Ollama)")
        print("   🖼️  Miniatures premium")
        quality_level = "STANDARD"
    
    print(f"\n🎯 Votre niveau actuel: {quality_level}")
    print()
    
    return quality_level

def generate_ultimate_video(keyword, status):
    """Generate video with ultimate quality."""
    print(f"🎬 GÉNÉRATION ULTIME POUR: '{keyword}'")
    print("=" * 60)
    
    # Show what will be used
    print("🔧 Configuration utilisée:")
    tts_system = "Minimax TTS (Ultra-réaliste)" if status.get('minimax') else "gTTS + pydub (Amélioré)"
    video_system = "Blender 3D (Professionnel)" if status.get('blender') else "FFmpeg (HD)"
    
    print(f"   🎤 Audio: {tts_system}")
    print(f"   🎬 Vidéo: {video_system}")
    print(f"   🤖 Scripts: Ollama AI (Optimisé YouTube)")
    print(f"   🖼️  Miniatures: Gradients premium")
    print()
    
    # Generate
    auto_youtube = EnhancedAutoYouTube()
    
    print("🚀 Lancement de la génération...")
    start_time = time.time()
    
    success = auto_youtube.process_keyword(keyword)
    
    generation_time = time.time() - start_time
    
    if success:
        print(f"✅ GÉNÉRATION RÉUSSIE en {generation_time:.1f} secondes!")
        
        # Show results
        video_path = Path("output/videos") / f"{keyword.replace(' ', '_')}.mp4"
        audio_path = Path("output/audio") / f"{keyword.replace(' ', '_')}.mp3"
        thumb_path = Path("output/thumbnails") / f"{keyword.replace(' ', '_')}.jpg"
        script_path = Path("output/scripts") / f"{keyword.replace(' ', '_')}.json"
        
        print("\n📁 FICHIERS GÉNÉRÉS:")
        
        if video_path.exists():
            size_mb = video_path.stat().st_size / 1024 / 1024
            print(f"   🎬 Vidéo: {video_path.name} ({size_mb:.1f} MB)")
        
        if audio_path.exists():
            size_mb = audio_path.stat().st_size / 1024 / 1024
            print(f"   🎤 Audio: {audio_path.name} ({size_mb:.1f} MB)")
        
        if thumb_path.exists():
            size_kb = thumb_path.stat().st_size / 1024
            print(f"   🖼️  Miniature: {thumb_path.name} ({size_kb:.0f} KB)")
        
        if script_path.exists():
            print(f"   📝 Script: {script_path.name}")
        
        print(f"\n🌐 Vidéo: file://{video_path.absolute()}")
        print(f"🖼️  Miniature: file://{thumb_path.absolute()}")
        
        return True
    else:
        print(f"❌ GÉNÉRATION ÉCHOUÉE après {generation_time:.1f} secondes")
        return False

def show_upgrade_suggestions(status):
    """Show suggestions to upgrade the system."""
    print("💡 SUGGESTIONS D'AMÉLIORATION:")
    print("-" * 40)
    
    suggestions = []
    
    if not status.get('minimax'):
        suggestions.append("🎤 Configurez Minimax TTS pour des voix ultra-réalistes")
        suggestions.append("   📖 Consultez: MINIMAX_SETUP.md")
        suggestions.append("   🧪 Testez: python test_minimax.py")
    
    if not status.get('blender'):
        suggestions.append("🎬 Installez Blender pour des vidéos 3D professionnelles")
        suggestions.append("   💾 Téléchargez: https://www.blender.org/download/")
    
    if suggestions:
        for suggestion in suggestions:
            print(f"   {suggestion}")
        print()
        print("🚀 Avec ces améliorations, vous obtiendrez une qualité PREMIUM MAXIMUM!")
    else:
        print("   🎉 Votre système est déjà configuré au maximum!")
        print("   🌟 Vous avez accès à toutes les fonctionnalités premium!")
    
    print()

def main():
    """Main ultimate demo function."""
    print_ultimate_header()
    
    # Check system status
    status = check_system_status()
    
    # Show quality levels
    quality_level = show_quality_levels(status)
    
    if len(sys.argv) > 1:
        # Generate with specific keyword
        keyword = " ".join(sys.argv[1:])
        generate_ultimate_video(keyword, status)
    else:
        # Interactive demo
        print("🎮 DÉMONSTRATION ULTIME:")
        print("1. 🚀 Générer une vidéo premium")
        print("2. 🔍 Voir les détails du système")
        print("3. 💡 Suggestions d'amélioration")
        print("4. 🎤 Test comparatif vocal")
        print()
        
        choice = input("Choisissez une option (1-4): ").strip()
        
        if choice == "1":
            keyword = input("🎯 Entrez votre mot-clé: ").strip()
            if keyword:
                generate_ultimate_video(keyword, status)
            else:
                print("❌ Mot-clé vide")
        
        elif choice == "2":
            print("\n📋 DÉTAILS DU SYSTÈME:")
            print("-" * 30)
            for component, available in status.items():
                status_icon = "✅" if available else "❌"
                print(f"   {component}: {status_icon}")
        
        elif choice == "3":
            show_upgrade_suggestions(status)
        
        elif choice == "4":
            print("🎤 Lancement du test comparatif vocal...")
            os.system("python voice_comparison_demo.py")
        
        else:
            print("❌ Choix invalide")
    
    print("\n🎉 DÉMONSTRATION ULTIME TERMINÉE!")
    print()
    print("📚 RESSOURCES:")
    print("   📖 Guide complet: README.md")
    print("   🎤 Setup Minimax: MINIMAX_SETUP.md")
    print("   🧪 Test Minimax: python test_minimax.py")
    print("   🔊 Comparaison: python voice_comparison_demo.py")
    print()
    print("💡 Utilisation:")
    print("   python ultimate_demo.py 'votre mot-clé'")

if __name__ == "__main__":
    main()
