#!/usr/bin/env python3
"""
test_pixabay.py - Test de l'intégration Pixabay pour les vidéos de nature
"""

import os
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def print_header():
    """Print test header."""
    print("🌿" * 25)
    print("🎬 TEST INTÉGRATION VIDÉOS NATURE PIXABAY 🎬")
    print("🌿" * 25)
    print()
    print("Ce script teste l'intégration de vidéos de nature depuis Pixabay")
    print("pour enrichir vos vidéos YouTube avec de magnifiques séquences naturelles.")
    print()

def check_pixabay_setup():
    """Check Pixabay setup."""
    print("🔍 VÉRIFICATION DE LA CONFIGURATION PIXABAY:")
    print("-" * 50)
    
    api_key = os.getenv('PIXABAY_API_KEY')
    
    if api_key:
        print(f"✅ API Key Pixabay: Configurée ({api_key[:8]}...)")
        return True
    else:
        print("❌ API Key Pixabay: Non configurée")
        print()
        print("📋 CONFIGURATION REQUISE:")
        print("1. Créez un compte gratuit sur https://pixabay.com/")
        print("2. Allez dans https://pixabay.com/api/docs/")
        print("3. Obtenez votre API key gratuite")
        print("4. Configurez la variable d'environnement:")
        print("   export PIXABAY_API_KEY='votre_cle_api'")
        print()
        print("💡 L'API Pixabay est gratuite avec 100 requêtes/minute")
        return False

def test_pixabay_manager():
    """Test Pixabay manager."""
    print("🧪 TEST DU GESTIONNAIRE PIXABAY:")
    print("-" * 40)
    
    try:
        from pixabay_videos import PixabayVideoManager
        
        manager = PixabayVideoManager()
        
        if not manager.available:
            print("❌ Gestionnaire Pixabay non disponible")
            return False
        
        print("✅ Gestionnaire Pixabay initialisé")
        
        # Test search
        test_keywords = ["artificial intelligence", "climate change", "space exploration"]
        
        for keyword in test_keywords:
            print(f"\n🔍 Test de recherche pour: '{keyword}'")
            
            videos = manager.search_nature_videos(keyword, 2)
            
            if videos:
                print(f"   ✅ Trouvé {len(videos)} vidéos:")
                for video in videos:
                    duration = video['duration']
                    quality = f"{video['width']}x{video['height']}"
                    size_mb = video['size'] / (1024 * 1024)
                    print(f"      • {video['search_term']}: {duration}s, {quality}, {size_mb:.1f}MB")
            else:
                print("   ⚠️ Aucune vidéo trouvée")
        
        return True
        
    except ImportError:
        print("❌ Module pixabay_videos non trouvé")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_video_download():
    """Test video download."""
    print("\n📥 TEST DE TÉLÉCHARGEMENT:")
    print("-" * 30)
    
    try:
        from pixabay_videos import PixabayVideoManager
        
        manager = PixabayVideoManager()
        
        if not manager.available:
            print("❌ Gestionnaire non disponible")
            return False
        
        # Search for a video
        videos = manager.search_nature_videos("nature", 1)
        
        if not videos:
            print("❌ Aucune vidéo trouvée pour le test")
            return False
        
        video = videos[0]
        print(f"🔄 Téléchargement de: {video['search_term']} ({video['duration']}s)")
        
        # Download
        downloaded_path = manager.download_video(video, "test_nature")
        
        if downloaded_path and Path(downloaded_path).exists():
            file_size = Path(downloaded_path).stat().st_size / (1024 * 1024)
            print(f"✅ Téléchargement réussi: {downloaded_path}")
            print(f"   📊 Taille: {file_size:.1f} MB")
            
            # Cleanup test file
            Path(downloaded_path).unlink(missing_ok=True)
            print("🧹 Fichier de test supprimé")
            
            return True
        else:
            print("❌ Téléchargement échoué")
            return False
            
    except Exception as e:
        print(f"❌ Erreur de téléchargement: {e}")
        return False

def test_video_composer():
    """Test video composer integration."""
    print("\n🎬 TEST DU COMPOSITEUR VIDÉO:")
    print("-" * 35)
    
    try:
        from video_composer import VideoComposer
        
        composer = VideoComposer()
        
        if composer.pixabay_manager.available:
            print("✅ Compositeur vidéo avec intégration Pixabay")
            print(f"   🎨 Styles disponibles: {list(composer.composition_styles.keys())}")
            
            # Show composition styles
            for style, config in composer.composition_styles.items():
                print(f"      • {style}: {config['description']}")
            
            return True
        else:
            print("⚠️ Compositeur vidéo sans Pixabay (fallback)")
            return False
            
    except ImportError:
        print("❌ Module video_composer non trouvé")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_integration():
    """Test complete integration."""
    print("\n🔗 TEST D'INTÉGRATION COMPLÈTE:")
    print("-" * 40)
    
    try:
        from video_manager import VideoManager
        
        video_manager = VideoManager()
        
        if hasattr(video_manager, 'video_composer') and video_manager.video_composer:
            print("✅ VideoManager avec intégration nature")
            
            if video_manager.video_composer.pixabay_manager.available:
                print("✅ Pixabay intégré et fonctionnel")
                print("🌿 Les vidéos seront enrichies avec des séquences nature!")
            else:
                print("⚠️ Pixabay non configuré - utilise méthodes standard")
            
            return True
        else:
            print("⚠️ VideoManager sans compositeur nature")
            return False
            
    except Exception as e:
        print(f"❌ Erreur d'intégration: {e}")
        return False

def show_nature_categories():
    """Show available nature categories."""
    print("\n🌿 CATÉGORIES DE NATURE DISPONIBLES:")
    print("-" * 45)
    
    categories = {
        "🏔️ Paysages": ["mountain", "valley", "horizon", "landscape"],
        "🌊 Eau": ["ocean", "river", "lake", "waterfall", "waves"],
        "🌳 Forêts": ["forest", "trees", "jungle", "leaves"],
        "🌸 Fleurs": ["flowers", "garden", "meadow", "spring"],
        "🌤️ Ciel": ["sky", "clouds", "sunset", "sunrise", "storm"],
        "🦅 Animaux": ["birds flying", "wildlife", "deer", "butterfly"],
        "🌍 Environnement": ["renewable energy", "wind turbines", "solar panels"],
        "❄️ Saisons": ["winter nature", "autumn nature", "spring nature"]
    }
    
    for category, keywords in categories.items():
        print(f"{category}: {', '.join(keywords[:3])}...")

def main():
    """Main test function."""
    print_header()
    
    # Check setup
    pixabay_configured = check_pixabay_setup()
    
    if not pixabay_configured:
        print("\n⚠️ Configuration Pixabay requise pour continuer les tests.")
        print("💡 Configurez votre API key et relancez ce script.")
        return
    
    print()
    
    # Run tests
    tests = [
        ("Gestionnaire Pixabay", test_pixabay_manager),
        ("Téléchargement vidéo", test_video_download),
        ("Compositeur vidéo", test_video_composer),
        ("Intégration complète", test_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results[test_name] = False
    
    # Show nature categories
    show_nature_categories()
    
    # Summary
    print("\n📊 RÉSUMÉ DES TESTS:")
    print("-" * 25)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Résultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("🌿 Votre système peut maintenant créer des vidéos avec de magnifiques séquences nature!")
        print()
        print("🚀 Testez avec:")
        print("   python test_single_keyword.py 'nature test'")
    else:
        print(f"\n⚠️ {total - passed} test(s) échoué(s)")
        print("🔧 Vérifiez la configuration et les dépendances")

if __name__ == "__main__":
    main()
