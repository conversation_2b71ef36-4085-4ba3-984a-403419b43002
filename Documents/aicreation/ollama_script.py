#!/usr/bin/env python3
"""
ollama_script.py — Utilisation d'Ollama via la ligne de commande
"""

import subprocess
import json
import sys
import tempfile
import os

def call_ollama_cli(model_name, prompt):
    """Appelle Ollama via la ligne de commande."""
    try:
        # Créer un fichier temporaire pour stocker le prompt
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp:
            temp.write(prompt)
            temp_path = temp.name

        print(f"Envoi de la requête à Ollama (modèle: {model_name})...")

        # Lire le contenu du fichier
        with open(temp_path, 'r') as f:
            prompt_content = f.read()

        # Appel à la commande ollama
        cmd = ["/Applications/Ollama.app/Contents/Resources/ollama", "run", model_name, prompt_content]
        result = subprocess.run(cmd, capture_output=True, text=True)

        # Supprimer le fichier temporaire
        os.unlink(temp_path)

        if result.returncode != 0:
            print(f"Erreur lors de l'exécution d'Ollama: {result.stderr}", file=sys.stderr)
            return None

        return result.stdout
    except Exception as e:
        print(f"Erreur lors de l'appel à Ollama: {e}", file=sys.stderr)
        return None

def main():
    # Utiliser le modèle spécifié ou phi3 par défaut
    model_name = sys.argv[1] if len(sys.argv) > 1 else "phi3"

    # Message à envoyer
    prompt = """
    Write a 3-minute video script about "history facts" with the following structure:
    1. Title: An engaging title for the video
    2. Introduction: Brief introduction to the topic (30 seconds)
    3. 3 Key Points: Three main points about the topic (2 minutes)
    4. Conclusion: A brief conclusion (30 seconds)

    Format the response as a JSON with the following keys:
    "title", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion"
    """

    # Appel à Ollama
    response = call_ollama_cli(model_name, prompt)

    if response:
        print("\nRéponse d'Ollama:")
        print(response)

        # Essayer de parser le JSON si présent
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].strip()
            else:
                json_str = response

            script = json.loads(json_str)

            print("\nJSON parsé avec succès:")
            print(json.dumps(script, indent=2, ensure_ascii=False))
        except json.JSONDecodeError as e:
            print(f"\nErreur lors du parsing JSON: {e}", file=sys.stderr)
            print("Contenu brut reçu:")
            print(response)

if __name__ == "__main__":
    main()
