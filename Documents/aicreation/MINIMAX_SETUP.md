# 🎤 Minimax TTS Integration Guide

Intégration de Minimax TTS pour des voix ultra-réalistes dans votre système de création automatique de vidéos YouTube.

## 🌟 Pourquoi Minimax TTS ?

### Avantages par rapport à gTTS
- **Qualité vocale ultra-réaliste** : Voix indiscernables de voix humaines
- **Expression émotionnelle** : Détection et reproduction des nuances émotionnelles
- **300+ voix pré-construites** : Multiples langues, accents, genres et âges
- **Clonage vocal** : Créez des voix personnalisées avec 10 secondes d'audio
- **Support multilingue** : 17+ langues avec accents authentiques
- **Contrôle avancé** : Pitch, vitesse, expression personnalisables

### Comparaison des qualités

| Caractéristique | gTTS (Gratuit) | Minimax TTS (Premium) |
|-----------------|----------------|----------------------|
| Réalisme vocal | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Expression émotionnelle | ❌ | ✅ |
| Voix disponibles | 1 par langue | 300+ |
| Clonage vocal | ❌ | ✅ |
| Contrôle avancé | ❌ | ✅ |
| Coût | Gratuit | Payant |

## 🚀 Installation et Configuration

### Étape 1 : Créer un compte Minimax

1. Visitez [https://www.minimaxi.com/](https://www.minimaxi.com/)
2. Créez un compte gratuit
3. Accédez à la plateforme API

### Étape 2 : Obtenir les credentials

1. **Group ID** :
   - Allez dans l'onglet "Account" → "Your Profile"
   - Copiez le GroupID (nombre à 19 chiffres)

2. **API Key** :
   - Dans le menu de gauche, cliquez sur "API Keys"
   - Cliquez sur "Create New Secret Key"
   - Donnez un nom à votre clé
   - **IMPORTANT** : Copiez et sauvegardez la clé immédiatement (elle ne sera plus affichée)

### Étape 3 : Configuration des variables d'environnement

#### Sur macOS/Linux :
```bash
# Ajoutez ces lignes à votre ~/.bashrc ou ~/.zshrc
export MINIMAX_API_KEY="votre_cle_api_ici"
export MINIMAX_GROUP_ID="votre_group_id_ici"

# Rechargez votre terminal
source ~/.bashrc  # ou ~/.zshrc
```

#### Sur Windows :
```cmd
# Dans l'invite de commande
set MINIMAX_API_KEY=votre_cle_api_ici
set MINIMAX_GROUP_ID=votre_group_id_ici

# Ou via les variables d'environnement système
```

### Étape 4 : Test de l'installation

```bash
cd Documents/aicreation
python test_minimax.py
```

Si tout est configuré correctement, vous devriez voir :
```
✅ Minimax TTS is available and ready!
🎉 All tests passed! Minimax TTS is ready to use.
```

## 🎯 Utilisation

### Génération automatique avec Minimax

Une fois configuré, le système utilisera automatiquement Minimax TTS :

```bash
# Test avec un mot-clé
python test_single_keyword.py "intelligence artificielle"

# Démonstration complète
python final_demo.py "future technology"

# Système complet
python enhanced_auto_youtube.py
```

### Sélection automatique des voix

Le système sélectionne automatiquement la voix appropriée selon le contenu :

- **Business/Finance** → Voix masculine professionnelle
- **Technologie/IA** → Voix masculine confiante
- **Lifestyle/Santé** → Voix féminine douce
- **Entertainment/Gaming** → Voix féminine énergique
- **Éducation/Tutoriels** → Voix masculine narrateur
- **Défaut** → Voix féminine chaleureuse

### Voix disponibles

```python
available_voices = {
    "male_confident": "male-qn-qingse",
    "female_warm": "female-shaonv", 
    "male_professional": "male-qn-jingying",
    "female_energetic": "female-yujie",
    "male_narrator": "male-qn-badao",
    "female_gentle": "female-tianmei",
    "english_us_male": "en-US-male-1",
    "english_us_female": "en-US-female-1",
    "english_uk_male": "en-GB-male-1",
    "english_uk_female": "en-GB-female-1"
}
```

## 🔧 Configuration avancée

### Personnalisation des voix

Modifiez `minimax_tts.py` pour ajouter vos propres voix :

```python
def set_voice_for_keyword(self, keyword: str) -> str:
    keyword_lower = keyword.lower()
    
    # Ajoutez vos propres règles
    if "crypto" in keyword_lower:
        return self.available_voices["male_confident"]
    elif "meditation" in keyword_lower:
        return self.available_voices["female_gentle"]
    
    # ... logique existante
```

### Clonage de voix personnalisé

```python
from minimax_tts import MinimaxTTS

tts = MinimaxTTS()

# Cloner une voix à partir d'un fichier audio
success = tts.clone_voice_from_file(
    audio_file_path="ma_voix.mp3",
    voice_id="ma_voix_custom"
)

if success:
    # Utiliser la voix clonée
    audio_data = tts.generate_speech(
        text="Bonjour, ceci est ma voix clonée !",
        voice_id="ma_voix_custom"
    )
```

## 💰 Coûts et limites

### Tarification Minimax
- **Modèle speech-01-turbo** : ~$0.002 par 1000 caractères
- **Limite de caractères** : 5000 caractères par requête
- **Clonage vocal** : Frais unique lors de la première utilisation

### Optimisation des coûts
- Le système limite automatiquement le texte à 4500 caractères
- Fallback automatique vers gTTS si Minimax échoue
- Pas de coûts supplémentaires si les credentials ne sont pas configurés

## 🔍 Dépannage

### Problèmes courants

#### "Minimax TTS not available"
```bash
# Vérifiez vos variables d'environnement
echo $MINIMAX_API_KEY
echo $MINIMAX_GROUP_ID

# Testez la connexion
python test_minimax.py
```

#### "API request failed"
- Vérifiez que votre compte Minimax a des crédits
- Vérifiez que l'API key est valide
- Vérifiez votre connexion internet

#### "Text too long"
- Le système tronque automatiquement à 4500 caractères
- Réduisez la longueur de vos scripts si nécessaire

### Logs détaillés

Activez les logs détaillés pour diagnostiquer :

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 Comparaison des résultats

### Avant (gTTS)
- Voix robotique standard
- Pas d'expression émotionnelle
- Qualité basique mais fonctionnelle

### Après (Minimax)
- Voix ultra-réaliste
- Expression émotionnelle naturelle
- Qualité professionnelle
- Engagement accru des viewers

## 🎉 Résultats attendus

Avec Minimax TTS, vos vidéos YouTube auront :
- **Voix professionnelles** indiscernables de voix humaines
- **Engagement accru** grâce à l'expression émotionnelle
- **Différenciation** par rapport à la concurrence
- **Qualité premium** pour vos contenus

## 🔗 Ressources

- [Site officiel Minimax](https://www.minimaxi.com/)
- [Documentation API](https://platform.minimax.chat/docs)
- [Playground vocal](https://www.hailuo.ai/audio)
- [Support Minimax](https://discord.com/invite/hvvt8hAye6)

## 📝 Notes importantes

1. **Sauvegardez vos credentials** : L'API key ne sera affichée qu'une fois
2. **Surveillez vos coûts** : Configurez des alertes de facturation
3. **Testez régulièrement** : Utilisez `test_minimax.py` pour vérifier le fonctionnement
4. **Fallback automatique** : Le système utilise gTTS si Minimax n'est pas disponible

---

🎤 **Avec Minimax TTS, transformez vos vidéos YouTube avec des voix d'une qualité exceptionnelle !**
