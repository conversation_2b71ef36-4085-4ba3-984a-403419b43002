#!/usr/bin/env python3
"""
Gestionnaire de formats vidéo multi-plateforme.
Support YouTube (16:9), TikTok (9:16), Instagram (1:1) avec optimisations spécifiques.
"""

from dataclasses import dataclass
from typing import Dict, Tu<PERSON>, List, Optional
from enum import Enum
import moviepy.editor as mpy
from loguru import logger

class Platform(Enum):
    """Plateformes supportées"""
    YOUTUBE = "youtube"
    TIKTOK = "tiktok"
    INSTAGRAM = "instagram"
    YOUTUBE_SHORT = "youtube_short"
    FACEBOOK = "facebook"
    TWITTER = "twitter"

class Quality(Enum):
    """Niveaux de qualité"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ULTRA = "ultra"

@dataclass
class FormatSpec:
    """Spécifications d'un format vidéo"""
    width: int
    height: int
    fps: int
    aspect_ratio: str
    max_duration: int  # secondes
    min_duration: int  # secondes
    recommended_bitrate: str
    audio_bitrate: str
    codec: str
    preset: str
    
    # Optimisations spécifiques
    use_square_pixels: bool = True
    color_space: str = "bt709"
    profile: str = "high"
    level: str = "4.0"
    
    # Contraintes de contenu
    max_file_size_mb: int = 100
    supports_subtitles: bool = True
    supports_chapters: bool = False

@dataclass
class ContentGuidelines:
    """Directives de contenu pour chaque plateforme"""
    optimal_hook_duration: float  # secondes
    attention_span: float  # secondes moyennes
    text_size_factor: float  # multiplicateur pour la taille du texte
    subtitle_position: str  # top, center, bottom
    safe_area_margin: float  # pourcentage des bords à éviter
    
    # Style recommandé
    pacing: str  # fast, medium, slow
    transition_style: str  # quick, smooth, dramatic
    color_scheme: str  # bright, natural, dark
    
    # Engagement
    cta_timing: float  # quand placer le CTA (en % de la vidéo)
    hashtag_count: int  # nombre optimal de hashtags

class FormatManager:
    """Gestionnaire des formats vidéo multi-plateforme"""
    
    def __init__(self):
        self.formats = self._initialize_formats()
        self.guidelines = self._initialize_guidelines()
    
    def _initialize_formats(self) -> Dict[Platform, Dict[Quality, FormatSpec]]:
        """Initialise les spécifications de formats"""
        formats = {}
        
        # YouTube (16:9)
        formats[Platform.YOUTUBE] = {
            Quality.LOW: FormatSpec(
                width=1280, height=720, fps=30, aspect_ratio="16:9",
                max_duration=3600, min_duration=60,
                recommended_bitrate="2500k", audio_bitrate="128k",
                codec="libx264", preset="fast"
            ),
            Quality.MEDIUM: FormatSpec(
                width=1920, height=1080, fps=30, aspect_ratio="16:9",
                max_duration=3600, min_duration=60,
                recommended_bitrate="5000k", audio_bitrate="192k",
                codec="libx264", preset="medium"
            ),
            Quality.HIGH: FormatSpec(
                width=1920, height=1080, fps=60, aspect_ratio="16:9",
                max_duration=3600, min_duration=60,
                recommended_bitrate="8000k", audio_bitrate="256k",
                codec="libx264", preset="slow"
            ),
            Quality.ULTRA: FormatSpec(
                width=3840, height=2160, fps=60, aspect_ratio="16:9",
                max_duration=3600, min_duration=60,
                recommended_bitrate="20000k", audio_bitrate="320k",
                codec="libx265", preset="medium"
            )
        }
        
        # TikTok (9:16)
        formats[Platform.TIKTOK] = {
            Quality.LOW: FormatSpec(
                width=720, height=1280, fps=30, aspect_ratio="9:16",
                max_duration=60, min_duration=15,
                recommended_bitrate="1500k", audio_bitrate="128k",
                codec="libx264", preset="fast",
                max_file_size_mb=50
            ),
            Quality.MEDIUM: FormatSpec(
                width=1080, height=1920, fps=30, aspect_ratio="9:16",
                max_duration=60, min_duration=15,
                recommended_bitrate="3000k", audio_bitrate="192k",
                codec="libx264", preset="medium",
                max_file_size_mb=75
            ),
            Quality.HIGH: FormatSpec(
                width=1080, height=1920, fps=60, aspect_ratio="9:16",
                max_duration=60, min_duration=15,
                recommended_bitrate="5000k", audio_bitrate="256k",
                codec="libx264", preset="slow",
                max_file_size_mb=100
            )
        }
        
        # Instagram (1:1 et 9:16)
        formats[Platform.INSTAGRAM] = {
            Quality.MEDIUM: FormatSpec(
                width=1080, height=1080, fps=30, aspect_ratio="1:1",
                max_duration=60, min_duration=15,
                recommended_bitrate="2500k", audio_bitrate="192k",
                codec="libx264", preset="medium",
                max_file_size_mb=50
            ),
            Quality.HIGH: FormatSpec(
                width=1080, height=1920, fps=30, aspect_ratio="9:16",
                max_duration=60, min_duration=15,
                recommended_bitrate="3500k", audio_bitrate="256k",
                codec="libx264", preset="medium",
                max_file_size_mb=75
            )
        }
        
        # YouTube Shorts (9:16)
        formats[Platform.YOUTUBE_SHORT] = {
            Quality.MEDIUM: FormatSpec(
                width=1080, height=1920, fps=30, aspect_ratio="9:16",
                max_duration=60, min_duration=15,
                recommended_bitrate="3000k", audio_bitrate="192k",
                codec="libx264", preset="medium",
                max_file_size_mb=100
            ),
            Quality.HIGH: FormatSpec(
                width=1080, height=1920, fps=60, aspect_ratio="9:16",
                max_duration=60, min_duration=15,
                recommended_bitrate="5000k", audio_bitrate="256k",
                codec="libx264", preset="slow",
                max_file_size_mb=150
            )
        }
        
        return formats
    
    def _initialize_guidelines(self) -> Dict[Platform, ContentGuidelines]:
        """Initialise les directives de contenu"""
        return {
            Platform.YOUTUBE: ContentGuidelines(
                optimal_hook_duration=15.0,
                attention_span=120.0,
                text_size_factor=1.0,
                subtitle_position="bottom",
                safe_area_margin=0.05,
                pacing="medium",
                transition_style="smooth",
                color_scheme="natural",
                cta_timing=0.85,
                hashtag_count=5
            ),
            Platform.TIKTOK: ContentGuidelines(
                optimal_hook_duration=3.0,
                attention_span=15.0,
                text_size_factor=1.3,
                subtitle_position="center",
                safe_area_margin=0.1,
                pacing="fast",
                transition_style="quick",
                color_scheme="bright",
                cta_timing=0.9,
                hashtag_count=8
            ),
            Platform.INSTAGRAM: ContentGuidelines(
                optimal_hook_duration=5.0,
                attention_span=30.0,
                text_size_factor=1.1,
                subtitle_position="bottom",
                safe_area_margin=0.08,
                pacing="medium",
                transition_style="smooth",
                color_scheme="bright",
                cta_timing=0.8,
                hashtag_count=10
            ),
            Platform.YOUTUBE_SHORT: ContentGuidelines(
                optimal_hook_duration=3.0,
                attention_span=20.0,
                text_size_factor=1.2,
                subtitle_position="center",
                safe_area_margin=0.1,
                pacing="fast",
                transition_style="quick",
                color_scheme="bright",
                cta_timing=0.9,
                hashtag_count=6
            )
        }
    
    def get_format_spec(self, platform: Platform, quality: Quality = Quality.MEDIUM) -> FormatSpec:
        """Récupère les spécifications d'un format"""
        if platform not in self.formats:
            raise ValueError(f"Plateforme non supportée: {platform}")
        
        if quality not in self.formats[platform]:
            # Fallback vers la qualité medium
            available_qualities = list(self.formats[platform].keys())
            if Quality.MEDIUM in available_qualities:
                quality = Quality.MEDIUM
            else:
                quality = available_qualities[0]
            logger.warning(f"Qualité {quality} non disponible pour {platform}, utilisation de {quality}")
        
        return self.formats[platform][quality]
    
    def get_content_guidelines(self, platform: Platform) -> ContentGuidelines:
        """Récupère les directives de contenu"""
        return self.guidelines.get(platform, self.guidelines[Platform.YOUTUBE])
    
    def get_optimal_dimensions(self, platform: Platform, quality: Quality = Quality.MEDIUM) -> Tuple[int, int]:
        """Récupère les dimensions optimales"""
        spec = self.get_format_spec(platform, quality)
        return (spec.width, spec.height)
    
    def validate_duration(self, platform: Platform, duration: float) -> bool:
        """Valide la durée pour une plateforme"""
        spec = self.get_format_spec(platform)
        return spec.min_duration <= duration <= spec.max_duration
    
    def suggest_optimal_duration(self, platform: Platform, content_type: str = "general") -> float:
        """Suggère une durée optimale selon la plateforme et le type de contenu"""
        guidelines = self.get_content_guidelines(platform)
        spec = self.get_format_spec(platform)
        
        # Durées optimales selon le type de contenu
        content_durations = {
            "educational": {
                Platform.YOUTUBE: 180.0,
                Platform.TIKTOK: 45.0,
                Platform.INSTAGRAM: 30.0,
                Platform.YOUTUBE_SHORT: 45.0
            },
            "entertainment": {
                Platform.YOUTUBE: 120.0,
                Platform.TIKTOK: 30.0,
                Platform.INSTAGRAM: 25.0,
                Platform.YOUTUBE_SHORT: 30.0
            },
            "promotional": {
                Platform.YOUTUBE: 90.0,
                Platform.TIKTOK: 20.0,
                Platform.INSTAGRAM: 20.0,
                Platform.YOUTUBE_SHORT: 25.0
            }
        }
        
        if content_type in content_durations and platform in content_durations[content_type]:
            suggested = content_durations[content_type][platform]
        else:
            # Durée par défaut basée sur l'attention span
            suggested = min(guidelines.attention_span, spec.max_duration)
        
        # Respect des contraintes de la plateforme
        return max(spec.min_duration, min(suggested, spec.max_duration))
    
    def get_subtitle_config(self, platform: Platform, video_width: int, video_height: int) -> Dict:
        """Configuration des sous-titres selon la plateforme"""
        guidelines = self.get_content_guidelines(platform)
        
        # Taille de police adaptée
        base_font_size = 24
        font_size = int(base_font_size * guidelines.text_size_factor * (video_width / 1920))
        
        # Position des sous-titres
        if guidelines.subtitle_position == "bottom":
            position = ('center', video_height * (1 - guidelines.safe_area_margin - 0.1))
        elif guidelines.subtitle_position == "center":
            position = ('center', video_height * 0.5)
        else:  # top
            position = ('center', video_height * (guidelines.safe_area_margin + 0.1))
        
        return {
            "fontsize": font_size,
            "position": position,
            "color": "white",
            "stroke_color": "black",
            "stroke_width": max(1, font_size // 12),
            "method": "caption",
            "size": (video_width * (1 - 2 * guidelines.safe_area_margin), None)
        }
    
    def optimize_for_platform(self, video_clip: mpy.VideoClip, platform: Platform, quality: Quality = Quality.MEDIUM) -> mpy.VideoClip:
        """Optimise un clip vidéo pour une plateforme spécifique"""
        spec = self.get_format_spec(platform, quality)
        guidelines = self.get_content_guidelines(platform)
        
        # Redimensionnement
        video_clip = video_clip.resize((spec.width, spec.height))
        
        # Ajustement de la durée si nécessaire
        if video_clip.duration > spec.max_duration:
            video_clip = video_clip.subclip(0, spec.max_duration)
            logger.warning(f"Vidéo tronquée à {spec.max_duration}s pour {platform.value}")
        
        # Ajustement du FPS
        if hasattr(video_clip, 'fps') and video_clip.fps != spec.fps:
            video_clip = video_clip.set_fps(spec.fps)
        
        return video_clip
    
    def get_export_settings(self, platform: Platform, quality: Quality = Quality.MEDIUM) -> Dict:
        """Paramètres d'export optimisés pour une plateforme"""
        spec = self.get_format_spec(platform, quality)
        
        return {
            "codec": spec.codec,
            "fps": spec.fps,
            "preset": spec.preset,
            "bitrate": spec.recommended_bitrate,
            "audio_codec": "aac",
            "audio_bitrate": spec.audio_bitrate,
            "temp_audiofile": "temp-audio.m4a",
            "remove_temp": True,
            "verbose": False,
            "logger": None
        }
    
    def get_supported_platforms(self) -> List[Platform]:
        """Liste des plateformes supportées"""
        return list(self.formats.keys())
    
    def get_platform_info(self, platform: Platform) -> Dict:
        """Informations complètes sur une plateforme"""
        if platform not in self.formats:
            return {}
        
        qualities = list(self.formats[platform].keys())
        default_spec = self.get_format_spec(platform)
        guidelines = self.get_content_guidelines(platform)
        
        return {
            "platform": platform.value,
            "supported_qualities": [q.value for q in qualities],
            "aspect_ratio": default_spec.aspect_ratio,
            "max_duration": default_spec.max_duration,
            "min_duration": default_spec.min_duration,
            "optimal_hook_duration": guidelines.optimal_hook_duration,
            "attention_span": guidelines.attention_span,
            "recommended_hashtags": guidelines.hashtag_count
        }

# Instance globale du gestionnaire de formats
format_manager = FormatManager()

# Fonctions utilitaires
def get_platform_dimensions(platform_name: str, quality: str = "medium") -> Tuple[int, int]:
    """Fonction utilitaire pour récupérer les dimensions"""
    platform = Platform(platform_name.lower())
    quality_enum = Quality(quality.lower())
    return format_manager.get_optimal_dimensions(platform, quality_enum)

def validate_video_for_platform(platform_name: str, duration: float, width: int, height: int) -> Dict[str, bool]:
    """Valide une vidéo pour une plateforme"""
    platform = Platform(platform_name.lower())
    spec = format_manager.get_format_spec(platform)
    
    return {
        "duration_valid": spec.min_duration <= duration <= spec.max_duration,
        "dimensions_valid": (width, height) == (spec.width, spec.height),
        "aspect_ratio_valid": abs((width/height) - (spec.width/spec.height)) < 0.01
    }

# Exemple d'utilisation
if __name__ == "__main__":
    manager = FormatManager()
    
    # Test des différentes plateformes
    for platform in manager.get_supported_platforms():
        info = manager.get_platform_info(platform)
        print(f"\n{platform.value.upper()}:")
        print(f"  Ratio: {info['aspect_ratio']}")
        print(f"  Durée: {info['min_duration']}-{info['max_duration']}s")
        print(f"  Hook optimal: {info['optimal_hook_duration']}s")
        print(f"  Hashtags: {info['recommended_hashtags']}")
    
    # Test de validation
    result = validate_video_for_platform("tiktok", 30, 1080, 1920)
    print(f"\nValidation TikTok: {result}")
