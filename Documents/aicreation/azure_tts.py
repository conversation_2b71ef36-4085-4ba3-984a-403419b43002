#!/usr/bin/env python3
"""
azure_tts.py - Azure Cognitive Services Text-to-Speech integration
"""

import os
import logging
from pathlib import Path
from typing import Dict, Optional
import xml.etree.ElementTree as ET

from config import get_config

logger = logging.getLogger(__name__)

try:
    import azure.cognitiveservices.speech as speechsdk
    AZURE_AVAILABLE = True
except ImportError:
    AZURE_AVAILABLE = False
    logger.warning("Azure Speech SDK not available. Install with: pip install azure-cognitiveservices-speech")

class AzureTTS:
    """Azure Cognitive Services Text-to-Speech manager."""
    
    def __init__(self, subscription_key: Optional[str] = None, region: Optional[str] = None):
        """Initialize Azure TTS client."""
        self.config = get_config()
        
        if not AZURE_AVAILABLE:
            self.available = False
            return
        
        # API credentials
        self.subscription_key = subscription_key or os.getenv('AZURE_SPEECH_KEY')
        self.region = region or os.getenv('AZURE_SPEECH_REGION', 'eastus')
        
        if not self.subscription_key:
            logger.warning("Azure Speech subscription key not provided")
            self.available = False
            return
        
        try:
            # Initialize Azure Speech Config
            self.speech_config = speechsdk.SpeechConfig(
                subscription=self.subscription_key, 
                region=self.region
            )
            self.available = True
        except Exception as e:
            logger.error(f"Failed to initialize Azure Speech: {e}")
            self.available = False
            return
        
        # Available voices (high-quality neural voices)
        self.available_voices = {
            # English US
            "aria_us": "en-US-AriaNeural",           # Female, news anchor
            "davis_us": "en-US-DavisNeural",         # Male, professional
            "jenny_us": "en-US-JennyNeural",         # Female, friendly
            "guy_us": "en-US-GuyNeural",             # Male, casual
            "jane_us": "en-US-JaneNeural",           # Female, young adult
            "jason_us": "en-US-JasonNeural",         # Male, energetic
            "sara_us": "en-US-SaraNeural",           # Female, soft
            "tony_us": "en-US-TonyNeural",           # Male, mature
            
            # English UK
            "libby_uk": "en-GB-LibbyNeural",         # Female, young
            "maisie_uk": "en-GB-MaisieNeural",       # Female, child
            "ryan_uk": "en-GB-RyanNeural",           # Male, young
            "sonia_uk": "en-GB-SoniaNeural",         # Female, mature
            
            # Multilingual
            "emma_multilingual": "en-US-EmmaMultilingualNeural",  # Female, multilingual
            "brian_multilingual": "en-US-BrianMultilingualNeural" # Male, multilingual
        }
        
        if self.available:
            logger.info("Azure TTS initialized successfully")
    
    def generate_speech(self, text: str, voice_name: str = "en-US-AriaNeural") -> Optional[bytes]:
        """Generate speech from text using Azure TTS."""
        if not self.available:
            logger.error("Azure TTS not available")
            return None
        
        try:
            # Set voice
            self.speech_config.speech_synthesis_voice_name = voice_name
            
            # Create synthesizer
            synthesizer = speechsdk.SpeechSynthesizer(speech_config=self.speech_config)
            
            logger.info(f"Generating speech with Azure TTS (voice: {voice_name})")
            
            # Generate speech
            result = synthesizer.speak_text_async(text).get()
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                logger.info("Successfully generated speech with Azure TTS")
                return result.audio_data
            elif result.reason == speechsdk.ResultReason.Canceled:
                cancellation_details = result.cancellation_details
                logger.error(f"Speech synthesis canceled: {cancellation_details.reason}")
                if cancellation_details.error_details:
                    logger.error(f"Error details: {cancellation_details.error_details}")
                return None
            
        except Exception as e:
            logger.error(f"Error generating speech with Azure: {e}")
            return None
    
    def generate_speech_with_ssml(self, ssml: str) -> Optional[bytes]:
        """Generate speech using SSML for advanced control."""
        if not self.available:
            logger.error("Azure TTS not available")
            return None
        
        try:
            synthesizer = speechsdk.SpeechSynthesizer(speech_config=self.speech_config)
            
            logger.info("Generating speech with Azure TTS using SSML")
            
            result = synthesizer.speak_ssml_async(ssml).get()
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                logger.info("Successfully generated speech with Azure TTS (SSML)")
                return result.audio_data
            else:
                logger.error(f"SSML synthesis failed: {result.reason}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating SSML speech: {e}")
            return None
    
    def create_ssml(self, text: str, voice_name: str, style: str = "neutral", 
                   rate: str = "medium", pitch: str = "medium") -> str:
        """Create SSML for advanced speech control."""
        ssml = f"""
        <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
            <voice name="{voice_name}">
                <mstts:express-as style="{style}">
                    <prosody rate="{rate}" pitch="{pitch}">
                        {text}
                    </prosody>
                </mstts:express-as>
            </voice>
        </speak>
        """
        return ssml.strip()
    
    def set_voice_for_keyword(self, keyword: str) -> str:
        """Select appropriate voice based on keyword."""
        keyword_lower = keyword.lower()
        
        # Voice selection logic
        if any(word in keyword_lower for word in ['business', 'finance', 'professional']):
            return self.available_voices["davis_us"]  # Professional male
        elif any(word in keyword_lower for word in ['technology', 'ai', 'science']):
            return self.available_voices["guy_us"]    # Technical male
        elif any(word in keyword_lower for word in ['lifestyle', 'health', 'wellness']):
            return self.available_voices["sara_us"]   # Soft female
        elif any(word in keyword_lower for word in ['entertainment', 'gaming', 'fun']):
            return self.available_voices["jason_us"]  # Energetic male
        elif any(word in keyword_lower for word in ['news', 'breaking', 'update']):
            return self.available_voices["aria_us"]   # News anchor female
        elif any(word in keyword_lower for word in ['education', 'tutorial', 'learning']):
            return self.available_voices["jenny_us"]  # Friendly female
        else:
            return self.available_voices["aria_us"]   # Default professional
    
    def generate_enhanced_speech(self, script: Dict[str, str], keyword: str, 
                                voice_name: Optional[str] = None) -> Optional[str]:
        """Generate enhanced speech from script using Azure TTS."""
        try:
            # Create speech text from script
            speech_parts = []
            
            # Helper function to extract text
            def extract_text(value):
                if isinstance(value, str):
                    return value
                elif isinstance(value, dict):
                    return value.get("text", str(value))
                else:
                    return str(value)
            
            # Build speech with natural pauses
            intro = extract_text(script.get("introduction", ""))
            if intro:
                speech_parts.append(f"{intro}")
            
            for i in range(1, 4):
                key = f"key_point_{i}"
                point = script.get(key, "")
                if point:
                    point_text = extract_text(point)
                    speech_parts.append(f"{point_text}")
            
            conclusion = extract_text(script.get("conclusion", ""))
            if conclusion:
                speech_parts.append(conclusion)
            
            # Join with natural pauses
            full_text = " ... ".join(speech_parts)
            
            # Select voice if not provided
            if not voice_name:
                voice_name = self.set_voice_for_keyword(keyword)
            
            # Create SSML for better control
            ssml = self.create_ssml(
                text=full_text,
                voice_name=voice_name,
                style="friendly",
                rate="medium",
                pitch="medium"
            )
            
            # Generate speech
            audio_data = self.generate_speech_with_ssml(ssml)
            
            if not audio_data:
                logger.error("Failed to generate speech with Azure")
                return None
            
            # Save audio file
            output_path = self.config["directories"]["audio"] / f"{keyword.replace(' ', '_')}.wav"
            
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            logger.info(f"Enhanced speech generated with Azure: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error generating enhanced speech: {e}")
            return None

# Example usage and testing
if __name__ == "__main__":
    # Test Azure TTS
    logging.basicConfig(level=logging.INFO)
    
    tts = AzureTTS()
    
    if tts.available:
        print("✅ Azure TTS is available")
        print(f"Available voices: {len(tts.available_voices)}")
        for name, voice_id in list(tts.available_voices.items())[:5]:
            print(f"  • {name}: {voice_id}")
        
        # Test speech generation
        test_script = {
            "title": "Azure TTS Test Video",
            "introduction": "Welcome to this test of Azure Cognitive Services text-to-speech.",
            "key_point_1": "This demonstrates high-quality neural voice synthesis.",
            "key_point_2": "Azure provides professional-grade speech generation.",
            "key_point_3": "The voices are natural and expressive.",
            "conclusion": "Thanks for listening to this Azure TTS demonstration!"
        }
        
        audio_path = tts.generate_enhanced_speech(test_script, "test_keyword")
        if audio_path:
            print(f"✅ Test audio generated: {audio_path}")
        else:
            print("❌ Test audio generation failed")
    else:
        print("❌ Azure TTS not available")
        if not AZURE_AVAILABLE:
            print("Install Azure Speech SDK: pip install azure-cognitiveservices-speech")
        else:
            print("Set AZURE_SPEECH_KEY and AZURE_SPEECH_REGION environment variables")
            print("Get your credentials at: https://portal.azure.com/")
