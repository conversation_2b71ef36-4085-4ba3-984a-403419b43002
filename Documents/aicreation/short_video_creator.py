#!/usr/bin/env python3
"""
Générateur de vidéos courtes au format vertical (9:16)
pour les plateformes sociales comme TikTok, Instagram et YouTube Shorts.
"""

import os
from pathlib import Path
import random
from typing import Optional, List, Union
import numpy as np
from moviepy.editor import (
    ImageClip, AudioFileClip, TextClip, CompositeVideoClip, VideoClip
)
from PIL import Image, ImageDraw, ImageFont
from loguru import logger
import sys
import torch

# Ajout du dossier parent au PYTHONPATH pour importer skyreels_manager
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from skyreels_manager import SkyReelsManager

class ShortVideoConfig:
    """Configuration pour la génération de vidéos courtes"""
    def __init__(self):
        # Format vertical 9:16
        self.width = 1080
        self.height = 1920
        self.fps = 30
        self.duration = 8  # Durée fixe de 8 secondes
        self.font_size = 72  # Police plus grande pour le format vertical
        self.font = "Arial"
        self.background_color = (20, 20, 20)
        self.text_color = (255, 255, 255)
        self.music_volume = 0.4
        self.output_dir = Path("output/shorts")
        self.output_dir.mkdir(parents=True, exist_ok=True)

class ShortVideoCreator:
    """Crée des vidéos courtes avec effets modernes"""
    
    def __init__(self, config: ShortVideoConfig):
        """
        Initialise le créateur de vidéos courtes.
        
        Args:
            config: Configuration pour la génération des vidéos
        """
        self.config = config
        self.sky_reels = SkyReelsManager()
    
    def create_short(
        self,
        prompt: str,
        text: str,
        music_path: Optional[str] = None
    ) -> str:
        """
        Crée une vidéo courte avec une image générée et du texte animé.
        
        Args:
            prompt: Description pour la génération d'image
            text: Texte à afficher sur la vidéo
            music_path: Chemin vers la musique de fond (optionnel)
            
        Returns:
            Chemin vers la vidéo générée
        """
        try:
            # Génère l'image de fond avec SkyReels
            background_img = self.sky_reels.generate_image(
                prompt=prompt,
                width=self.config.width,
                height=self.config.height
            )
            
            # Convertit l'image numpy en clip vidéo
            background = ImageClip(background_img).set_duration(self.config.duration)
            
            # Crée les clips de texte avec animation
            text_clips = self._create_animated_text(text)
            
            # Compose la vidéo
            video = CompositeVideoClip(
                [background] + text_clips,
                size=(self.config.width, self.config.height)
            )
            
            # Ajoute la musique si fournie
            if music_path and os.path.exists(music_path):
                audio = AudioFileClip(music_path)
                # Ajuste la durée de la musique
                audio = audio.subclip(0, self.config.duration)
                audio = audio.volumex(self.config.music_volume)
                video = video.set_audio(audio)
            
            # Exporte la vidéo
            output_path = str(self.config.output_dir / f"short_{random.randint(1000, 9999)}.mp4")
            video.write_videofile(
                output_path,
                fps=self.config.fps,
                codec='libx264',
                audio_codec='aac',
                preset='ultrafast'  # Pour un rendu rapide
            )
            
            return output_path
            
        except Exception as e:
            logger.error(f"Erreur création vidéo courte: {e}")
            return ""
            
    def _create_animated_text(self, text: str) -> List[VideoClip]:
        """
        Crée des clips de texte avec animation moderne.
        
        Args:
            text: Texte à animer
            
        Returns:
            Liste des clips de texte animés
        """
        clips = []
        words = text.split()
        duration_per_word = self.config.duration / (len(words) + 1)
        
        for i, word in enumerate(words):
            # Crée le clip de texte
            txt_clip = TextClip(
                word,
                font=self.config.font,
                fontsize=self.config.font_size,
                color=self.config.text_color,
                stroke_color='black',
                stroke_width=2
            )
            
            # Position verticale basée sur le nombre de mots
            y_pos = (self.config.height * 0.4) + (i * self.config.font_size * 1.5)
            
            # Animation d'apparition
            txt_clip = (txt_clip
                       .set_position(("center", y_pos))
                       .set_start(i * duration_per_word)
                       .set_duration(self.config.duration - (i * duration_per_word))
                       .crossfadein(0.5)
                       .crossfadeout(0.5))
            
            clips.append(txt_clip)
        
        return clips

def main():
    """Fonction de test pour la génération de vidéo courte"""
    try:
        # Configuration de base
        config = ShortVideoConfig()
        creator = ShortVideoCreator(config)
        
        # Paramètres de test
        prompt = "Un paysage naturel époustouflant avec montagnes majestueuses au coucher du soleil"
        text = "Découvrez la beauté de la nature"
        
        # Recherche une musique de fond
        music_dir = Path("assets/music")
        music_path = None
        if music_dir.exists():
            music_files = list(music_dir.glob("*.mp3"))
            if music_files:
                music_path = str(music_files[0])
                logger.info(f"Musique trouvée: {music_path}")
                
        # Génère la vidéo
        logger.info("Création de la vidéo courte...")
        video_path = creator.create_short(prompt, text, music_path)
        
        if video_path:
            file_size = Path(video_path).stat().st_size / (1024 * 1024)  # Taille en MB
            logger.info(f"✅ Vidéo créée: {video_path} ({file_size:.1f} MB)")
        else:
            logger.error("❌ Échec de la création de la vidéo")
            
    except Exception as e:
        logger.error(f"Erreur dans le programme principal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Configuration du logger
    logger.remove()
    logger.add(
        sys.stderr,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>"
    )
    
    main()
