bitsandbytes-0.42.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bitsandbytes-0.42.0.dist-info/LICENSE,sha256=UkEte8fOQVfqYou6rLiCngqcs8WPV_mRdhJryM8r_IU,1086
bitsandbytes-0.42.0.dist-info/METADATA,sha256=_BM_JbhFlZOsncZtQyNXpBPMb07O3rm6zZpX_krRkFk,9858
bitsandbytes-0.42.0.dist-info/NOTICE.md,sha256=_4zDL2L8BqUwtmvoznR_wqhQmsP2QwdXHrAHnBMzAl8,265
bitsandbytes-0.42.0.dist-info/RECORD,,
bitsandbytes-0.42.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes-0.42.0.dist-info/WHEEL,sha256=oiQVh_5PnQM0E3gPdiz09WCNmwiHDMaGer_elqB3coM,92
bitsandbytes-0.42.0.dist-info/top_level.txt,sha256=RttH1rYsSQjh-S6_y8rqF6hmKVVQ-cigSmKp5lBdKk4,19
bitsandbytes/__init__.py,sha256=wkhTf3lz9w-LMJoUF4cMb2z_3XGJU3ZmDuEzMhM5p4U,694
bitsandbytes/__main__.py,sha256=rWjs6LsifG_Vglj3WM4brY2IOCjwKpAjuBP3OIzYFPU,4014
bitsandbytes/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/__pycache__/__main__.cpython-39.pyc,,
bitsandbytes/__pycache__/cextension.cpython-39.pyc,,
bitsandbytes/__pycache__/functional.cpython-39.pyc,,
bitsandbytes/__pycache__/utils.cpython-39.pyc,,
bitsandbytes/autograd/__init__.py,sha256=Ltb59FJrcWYVsTfGW6SscEZtiDhHZe7EFrYnIhnASug,67
bitsandbytes/autograd/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/autograd/__pycache__/_functions.cpython-39.pyc,,
bitsandbytes/autograd/_functions.py,sha256=ueNhElKDD9Q0eSjLeHRUyctCfb3gfyaGu0MTr_PmfRM,22315
bitsandbytes/cextension.py,sha256=klJwL-8ZPylUOETDTW-fvUbZ_Bt_rdB6wRDND1fB_wk,1635
bitsandbytes/cuda_setup/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/cuda_setup/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/cuda_setup/__pycache__/env_vars.cpython-39.pyc,,
bitsandbytes/cuda_setup/__pycache__/main.cpython-39.pyc,,
bitsandbytes/cuda_setup/env_vars.py,sha256=HEqjxDnbYEJsK0tVgRhoDdt2rQqQGKadw1WhBxxpO7Y,1824
bitsandbytes/cuda_setup/main.py,sha256=7furkRUUyemUhWty930pHMJviB5Q1gzOGEBofX9XQdw,18201
bitsandbytes/functional.py,sha256=6_1SVPnuFBB-1hNQZpMxATLQIUvXf9B1iRgikwZvMY0,85527
bitsandbytes/libbitsandbytes_cpu.so,sha256=aRCVMaA17voYycY1vJoerrV5PcvO7kWznjsfPUNXgiQ,41608
bitsandbytes/libbitsandbytes_cuda110.so,sha256=k-7X6J86RV-qwtFG4-yc3a0QqmKkcFyxjA9gD2lpA2g,5938880
bitsandbytes/libbitsandbytes_cuda110_nocublaslt.so,sha256=rtsxLaJlH3G61dFV2soHZjWmWuqd4Gw_V6uhfAapOmM,11110760
bitsandbytes/libbitsandbytes_cuda111.so,sha256=BgwxHku6aiHgXP8x6NNg13gLa0zhz9cBzR9chXoeW54,8974016
bitsandbytes/libbitsandbytes_cuda111_nocublaslt.so,sha256=lqk3it3pMGA5xIHggDCLsidd_IhLzx1tGADvR-XGJco,20248936
bitsandbytes/libbitsandbytes_cuda114.so,sha256=DO3vbXx-xXBICjv2yMHoPZTxbDh-Wuxx1jtaXWnrXhU,9317984
bitsandbytes/libbitsandbytes_cuda114_nocublaslt.so,sha256=DhDZK_RLxc_VAvFvF54QDziagYtXsBi7n2vimQvoGiU,20973832
bitsandbytes/libbitsandbytes_cuda115.so,sha256=dgDPm3H21h4uU8_xNDcWqUfQp7IlinQ1LtMzofwfcGw,9310128
bitsandbytes/libbitsandbytes_cuda115_nocublaslt.so,sha256=chw_uE08Yv1qaiJkwOp_ef0jLBUUOTAi3PzySVonopQ,20929112
bitsandbytes/libbitsandbytes_cuda117.so,sha256=v2lvToUKz7DP3J7iBtpqvRj2DRqdAoarSYgx0qqDSWA,9117920
bitsandbytes/libbitsandbytes_cuda117_nocublaslt.so,sha256=RPQsUTf7Of1LFwQZNYwdWlI78atTojawI4l1zTS4lmY,20741008
bitsandbytes/libbitsandbytes_cuda118.so,sha256=kL1bK2FND66eED2rzb2vU3sxQoh9wB_lPV56BA9b5oo,14922256
bitsandbytes/libbitsandbytes_cuda118_nocublaslt.so,sha256=uR8PX9n4mKt4CfBkqyVn2x8gc07L7jbWEfzHGxpGc_g,26520768
bitsandbytes/libbitsandbytes_cuda120.so,sha256=tBNcz8Cj4BXYp3UIOwYEcal-7VgTfRP_XpL8l7ZXMVI,14508384
bitsandbytes/libbitsandbytes_cuda120_nocublaslt.so,sha256=_vYm4xzF_DBKSUpaueBdLU4kBkvdDjkqvqeix99_b-U,25713680
bitsandbytes/libbitsandbytes_cuda121.so,sha256=PssbuGkrByG6KRw8vqoiR_2K5jrDpAwYLb-cHyhCzPU,14516576
bitsandbytes/libbitsandbytes_cuda121_nocublaslt.so,sha256=KEqjTIqERYogtBb4nXx2tIUPLq1viLv7gXroxmf4DvA,25730064
bitsandbytes/libbitsandbytes_cuda122.so,sha256=ATrG8AJXzu3Djr9VguYNYM_A4sMZZVWPg9AMeiVLxdk,14561032
bitsandbytes/libbitsandbytes_cuda122_nocublaslt.so,sha256=A85DBXmY4x20wIvfKph2eTE-ByL36xpqEn_5P8F0W3A,25807368
bitsandbytes/libbitsandbytes_cuda123.so,sha256=9bLUk1IxtAzbGYIpGDGQFd6CR5CkjGUuv8hKYiLktsI,14581608
bitsandbytes/libbitsandbytes_cuda123_nocublaslt.so,sha256=t5HATyr1uk3xW9haLfILwyvWn7X_slIwLgOLVgZTlSw,25836136
bitsandbytes/nn/__init__.py,sha256=T7Domd9C_vbGVqHV6uZchCjRKrviESj1IKdCCGt4MYE,457
bitsandbytes/nn/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/nn/__pycache__/modules.cpython-39.pyc,,
bitsandbytes/nn/__pycache__/triton_based_modules.cpython-39.pyc,,
bitsandbytes/nn/modules.py,sha256=KA6xt1lDBLACJoLETxEqXcroLuxXJgUO4yjWZew8GbI,20951
bitsandbytes/nn/triton_based_modules.py,sha256=rBcYQ3a5fgZ2eO93ERxDBOs_VeR4FpR2rbc7DW46HWg,9843
bitsandbytes/optim/__init__.py,sha256=TSl80yMFkwGBl8N0FBFcfBLt2vt4cZn-hbkuwHGuCUE,794
bitsandbytes/optim/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/adagrad.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/adam.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/adamw.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/lamb.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/lars.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/lion.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/optimizer.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/rmsprop.cpython-39.pyc,,
bitsandbytes/optim/__pycache__/sgd.cpython-39.pyc,,
bitsandbytes/optim/adagrad.py,sha256=E4KsNJKOB2VfgkyKEoeYwFFXnedsxHZItdfzwc5_cdE,3719
bitsandbytes/optim/adam.py,sha256=nHHvXoeiAuosn4a9VWI3Z7_XmvYC6bOHb8en6mxiwkA,12776
bitsandbytes/optim/adamw.py,sha256=byibv4xoBM7FUK8FScRTx2KbI4-2Mi0yB8WJCb2x3wE,2699
bitsandbytes/optim/lamb.py,sha256=hfH4H9eVAHcbjL04DAI_lcPD1OPAmcY4_myow-o21aw,2313
bitsandbytes/optim/lars.py,sha256=PeUB8RlfaRtHEa-ZZZkrKDdmkHa7XEEfU81irU-mKsY,5653
bitsandbytes/optim/lion.py,sha256=jANwqVZSAxNZnoqi_OQ9XG8hKa6e84mkwJ9CchtpLHs,2304
bitsandbytes/optim/optimizer.py,sha256=219zPzx9dpeY0VndzlXt6jn2yV9sEiSXkrxe26wXjIo,25167
bitsandbytes/optim/rmsprop.py,sha256=1zGT9JIZh214fbBZ-CTirVKk1rQxSZe-BRJzhRtYL2U,2785
bitsandbytes/optim/sgd.py,sha256=YHVUeEkwxgYx_0GhH0Et6fCpk7rfhboDR2F06jRWz4E,2340
bitsandbytes/research/__init__.py,sha256=_MilJdwSRWObRfzzy14WD6HsJa6okT4d5YxH4aB9zg4,119
bitsandbytes/research/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/research/autograd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/research/autograd/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/research/autograd/__pycache__/_functions.cpython-39.pyc,,
bitsandbytes/research/autograd/_functions.py,sha256=k72rcf4hT3M5GOpGoijWkpTAqjRNoecGlOHmTTn3n80,15874
bitsandbytes/research/nn/__init__.py,sha256=j5XA_2ZA6efMtcbuUCyegfCLkDDQuL3ix5xS4yKZayY,53
bitsandbytes/research/nn/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/research/nn/__pycache__/modules.cpython-39.pyc,,
bitsandbytes/research/nn/modules.py,sha256=EnI2qVTosAMkH4G1fQleA0zvm8dZR9G-GJ4pFDo8V9M,2357
bitsandbytes/triton/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/triton/__pycache__/__init__.cpython-39.pyc,,
bitsandbytes/triton/__pycache__/dequantize_rowwise.cpython-39.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_mixed_dequantize.cpython-39.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_rowwise_dequantize.cpython-39.pyc,,
bitsandbytes/triton/__pycache__/quantize_columnwise_and_transpose.cpython-39.pyc,,
bitsandbytes/triton/__pycache__/quantize_global.cpython-39.pyc,,
bitsandbytes/triton/__pycache__/quantize_rowwise.cpython-39.pyc,,
bitsandbytes/triton/__pycache__/triton_utils.cpython-39.pyc,,
bitsandbytes/triton/dequantize_rowwise.py,sha256=qdh3f4O53faM6SFT_aYvrytWF_FQW3q2bhBll6Uwfc4,2193
bitsandbytes/triton/int8_matmul_mixed_dequantize.py,sha256=fl-iSK5FvJ9ATusXG6SCsDCoEvfXwhfBZJcfdm8yARQ,8256
bitsandbytes/triton/int8_matmul_rowwise_dequantize.py,sha256=EMiY3nfx0LIvYEGUqtzcfUonQxwoDcppYli9Qd6kViw,8240
bitsandbytes/triton/quantize_columnwise_and_transpose.py,sha256=K2fFegPtSsi2tgKxb5goO8YpUmQ6wgTvsXabgTRAFNI,2749
bitsandbytes/triton/quantize_global.py,sha256=5in9Plx1Kgf6Nx5B1RBXCiJnb0G4qwraGADNiq1LtVc,3957
bitsandbytes/triton/quantize_rowwise.py,sha256=sraX6TMubZQGiG9Gyh0UFzK823e_TkXZk9R1BILJdPU,2331
bitsandbytes/triton/triton_utils.py,sha256=f7CP_3lvUoTQJ-xSp4wAfiU8uX_trtGdUsoLzlcsHQY,103
bitsandbytes/utils.py,sha256=NomhCXFSFwHDfdPcjhFu63lUh5mLXaZfy6mOWcOJ2Ng,6589
tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tests/__pycache__/__init__.cpython-39.pyc,,
tests/__pycache__/test_autograd.cpython-39.pyc,,
tests/__pycache__/test_cuda_setup_evaluator.cpython-39.pyc,,
tests/__pycache__/test_functional.cpython-39.pyc,,
tests/__pycache__/test_generation.cpython-39.pyc,,
tests/__pycache__/test_linear4bit.cpython-39.pyc,,
tests/__pycache__/test_linear8bitlt.cpython-39.pyc,,
tests/__pycache__/test_modules.cpython-39.pyc,,
tests/__pycache__/test_optim.cpython-39.pyc,,
tests/__pycache__/test_triton.cpython-39.pyc,,
tests/test_autograd.py,sha256=k-Y4fhcWf1TrwzAnkFVUJV5_W89SE0tZuN3aWW20sYQ,23412
tests/test_cuda_setup_evaluator.py,sha256=LkeMbRi9_F3wWkLZkxGB3LCGEMLxF-96H6WccTGk4-U,665
tests/test_functional.py,sha256=KSnXQ6TcAa2h3NmfsYrNZ_HLCvN9Unqx04mB6DS20rA,87721
tests/test_generation.py,sha256=3NWgcJbSX7O2tYThYO-VxOUNgTzlloCezDUCxSRbwxc,4401
tests/test_linear4bit.py,sha256=Yvgqwo7D5ES7pRjKTMwVwwJVJKnnrqQCa9jcgn5jU24,3866
tests/test_linear8bitlt.py,sha256=3-KFQ2zfMczzrEV9d6nN8_aPuEAWwjU26YQhZo58BAs,5415
tests/test_modules.py,sha256=rncnmC-TcRAWxMx_CAqvtovtqxasaqaotomcezpbUCw,23188
tests/test_optim.py,sha256=vdiA5D59BMMc5sJogT3jSfpsmZp_DaLTbaMFHLMywhU,21112
tests/test_triton.py,sha256=Zekyy8LHj_sTmBAEjMKXzDPmzmLaMEpUQTz7aa5cH4g,2531
