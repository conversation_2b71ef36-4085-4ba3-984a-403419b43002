#!/usr/bin/env python3
"""
diagnose_piper.py - Diagnostic complet de Piper TTS
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def check_piper_files():
    """Check Piper installation files."""
    print("🔍 DIAGNOSTIC FICHIERS PIPER")
    print("=" * 40)
    
    piper_dir = Path("./piper")
    
    if not piper_dir.exists():
        print("❌ Dossier ./piper non trouvé")
        return False
    
    print(f"✅ Dossier Piper: {piper_dir}")
    
    # Check all files in piper directory
    files = list(piper_dir.iterdir())
    print(f"📁 Fichiers dans ./piper: {len(files)}")
    
    for file in files:
        size = file.stat().st_size if file.is_file() else "DIR"
        perms = oct(file.stat().st_mode)[-3:] if file.is_file() else "---"
        print(f"   {file.name}: {size} bytes, perms: {perms}")
    
    # Check executable specifically
    piper_exe = piper_dir / "piper"
    if piper_exe.exists():
        print(f"✅ Exécutable trouvé: {piper_exe}")
        print(f"   Taille: {piper_exe.stat().st_size} bytes")
        print(f"   Permissions: {oct(piper_exe.stat().st_mode)[-3:]}")
        print(f"   Exécutable: {os.access(piper_exe, os.X_OK)}")
        return True
    else:
        print("❌ Exécutable piper non trouvé")
        return False

def check_dependencies():
    """Check system dependencies."""
    print("\n🔧 DIAGNOSTIC DÉPENDANCES")
    print("-" * 30)
    
    # Check espeak-ng
    espeak_paths = [
        "/opt/homebrew/bin/espeak-ng",
        "/usr/local/bin/espeak-ng",
        "/usr/bin/espeak-ng"
    ]
    
    espeak_found = False
    for path in espeak_paths:
        if Path(path).exists():
            print(f"✅ espeak-ng trouvé: {path}")
            espeak_found = True
            break
    
    if not espeak_found:
        print("❌ espeak-ng non trouvé")
        print("💡 Installez avec: brew install espeak-ng")
    
    # Check libraries
    lib_paths = [
        "/opt/homebrew/lib/libespeak-ng.dylib",
        "/usr/local/lib/libespeak-ng.dylib"
    ]
    
    lib_found = False
    for path in lib_paths:
        if Path(path).exists():
            print(f"✅ Bibliothèque trouvée: {path}")
            lib_found = True
            break
    
    if not lib_found:
        print("❌ Bibliothèque libespeak-ng non trouvée")
    
    return espeak_found and lib_found

def test_piper_direct():
    """Test Piper executable directly."""
    print("\n🧪 TEST DIRECT PIPER")
    print("-" * 25)
    
    piper_exe = "./piper/piper"
    
    if not Path(piper_exe).exists():
        print("❌ Exécutable non trouvé")
        return False
    
    # Set up environment
    env = os.environ.copy()
    env['DYLD_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    env['DYLD_FALLBACK_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    env['ESPEAK_DATA_PATH'] = "./piper/espeak-ng-data"
    
    print("🔄 Test avec --version...")
    
    try:
        # Use Popen for better control
        process = subprocess.Popen(
            [piper_exe, "--version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env,
            text=True
        )
        
        # Wait with timeout
        try:
            stdout, stderr = process.communicate(timeout=10)
            
            if process.returncode == 0:
                print(f"✅ Succès! Version: {stdout.strip()}")
                return True
            else:
                print(f"❌ Échec (code {process.returncode})")
                print(f"Stderr: {stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            process.kill()
            print("⏰ Timeout - processus tué")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def fix_piper_permissions():
    """Fix Piper permissions and quarantine."""
    print("\n🔧 CORRECTION PERMISSIONS")
    print("-" * 30)
    
    piper_exe = Path("./piper/piper")
    
    if not piper_exe.exists():
        print("❌ Exécutable non trouvé")
        return False
    
    try:
        # Remove quarantine
        print("🔄 Suppression quarantaine...")
        result = subprocess.run(
            ["xattr", "-d", "com.apple.quarantine", str(piper_exe)],
            capture_output=True, text=True
        )
        
        if result.returncode == 0:
            print("✅ Quarantaine supprimée")
        else:
            print("⚠️ Pas de quarantaine ou déjà supprimée")
        
        # Set executable permissions
        print("🔄 Permissions d'exécution...")
        piper_exe.chmod(0o755)
        print("✅ Permissions définies")
        
        # Check if it's executable now
        if os.access(piper_exe, os.X_OK):
            print("✅ Fichier exécutable")
            return True
        else:
            print("❌ Toujours pas exécutable")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def download_test_model():
    """Download a test model for Piper."""
    print("\n📥 TÉLÉCHARGEMENT MODÈLE TEST")
    print("-" * 35)
    
    models_dir = Path.home() / ".local" / "share" / "piper" / "models"
    models_dir.mkdir(parents=True, exist_ok=True)
    
    model_file = models_dir / "fr_FR-gilles-low.onnx"
    config_file = models_dir / "fr_FR-gilles-low.onnx.json"
    
    if model_file.exists():
        print(f"✅ Modèle déjà présent: {model_file.name}")
        return True
    
    print(f"📁 Téléchargement vers: {models_dir}")
    
    # URLs
    model_url = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/gilles/low/fr_FR-gilles-low.onnx"
    config_url = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/gilles/low/fr_FR-gilles-low.onnx.json"
    
    try:
        print("🔄 Téléchargement modèle...")
        subprocess.run(["curl", "-L", model_url, "-o", str(model_file)], 
                      check=True, timeout=60)
        
        print("🔄 Téléchargement config...")
        subprocess.run(["curl", "-L", config_url, "-o", str(config_file)], 
                      check=True, timeout=30)
        
        if model_file.exists() and config_file.exists():
            model_size = model_file.stat().st_size / (1024 * 1024)
            print(f"✅ Téléchargement réussi! ({model_size:.1f} MB)")
            return True
        else:
            print("❌ Fichiers non créés")
            return False
            
    except Exception as e:
        print(f"❌ Erreur téléchargement: {e}")
        return False

def test_piper_synthesis():
    """Test actual speech synthesis."""
    print("\n🎵 TEST SYNTHÈSE VOCALE")
    print("-" * 30)
    
    piper_exe = "./piper/piper"
    models_dir = Path.home() / ".local" / "share" / "piper" / "models"
    model_file = models_dir / "fr_FR-gilles-low.onnx"
    
    if not Path(piper_exe).exists():
        print("❌ Exécutable Piper non trouvé")
        return False
    
    if not model_file.exists():
        print("❌ Modèle non trouvé")
        return False
    
    # Test text
    test_text = "Bonjour, ceci est un test de Piper TTS."
    output_file = "test_piper_synthesis.wav"
    
    print(f"📝 Texte: {test_text}")
    print(f"🎵 Modèle: {model_file.name}")
    
    # Set environment
    env = os.environ.copy()
    env['DYLD_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    env['DYLD_FALLBACK_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    env['ESPEAK_DATA_PATH'] = "./piper/espeak-ng-data"
    
    try:
        print("🔄 Synthèse en cours...")
        
        # Run Piper
        process = subprocess.Popen(
            [piper_exe, "--model", str(model_file), "--output_file", output_file],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env,
            text=True
        )
        
        stdout, stderr = process.communicate(input=test_text, timeout=30)
        
        if process.returncode == 0:
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"✅ Synthèse réussie!")
                print(f"📁 Fichier: {output_file} ({file_size} bytes)")
                
                # Cleanup
                Path(output_file).unlink(missing_ok=True)
                return True
            else:
                print("❌ Fichier de sortie non créé")
        else:
            print(f"❌ Échec (code {process.returncode})")
            print(f"Stderr: {stderr}")
        
        return False
        
    except subprocess.TimeoutExpired:
        process.kill()
        print("⏰ Timeout lors de la synthèse")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Main diagnostic function."""
    print("🎤 DIAGNOSTIC COMPLET PIPER TTS")
    print("=" * 50)
    
    # Step 1: Check files
    print("ÉTAPE 1: Vérification des fichiers")
    files_ok = check_piper_files()
    
    if not files_ok:
        print("\n❌ ARRÊT: Fichiers Piper manquants")
        return
    
    # Step 2: Check dependencies
    print("\nÉTAPE 2: Vérification des dépendances")
    deps_ok = check_dependencies()
    
    if not deps_ok:
        print("\n⚠️ Dépendances manquantes - installation...")
        print("🔄 Installation espeak-ng...")
        try:
            subprocess.run(["brew", "install", "espeak-ng"], check=True)
            print("✅ espeak-ng installé")
        except:
            print("❌ Échec installation espeak-ng")
            return
    
    # Step 3: Fix permissions
    print("\nÉTAPE 3: Correction des permissions")
    perms_ok = fix_piper_permissions()
    
    if not perms_ok:
        print("\n❌ ARRÊT: Impossible de corriger les permissions")
        return
    
    # Step 4: Test executable
    print("\nÉTAPE 4: Test de l'exécutable")
    exe_ok = test_piper_direct()
    
    if not exe_ok:
        print("\n❌ ARRÊT: Exécutable ne fonctionne pas")
        return
    
    # Step 5: Download model
    print("\nÉTAPE 5: Téléchargement du modèle")
    model_ok = download_test_model()
    
    if not model_ok:
        print("\n⚠️ Pas de modèle - synthèse impossible")
        return
    
    # Step 6: Test synthesis
    print("\nÉTAPE 6: Test de synthèse")
    synthesis_ok = test_piper_synthesis()
    
    # Final result
    print(f"\n{'='*50}")
    print("🎯 RÉSULTAT FINAL:")
    
    if synthesis_ok:
        print("🎉 PIPER TTS FONCTIONNE PARFAITEMENT!")
        print("✅ Prêt pour l'intégration dans le système")
    else:
        print("❌ Piper TTS ne fonctionne pas complètement")
        print("🔧 Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
