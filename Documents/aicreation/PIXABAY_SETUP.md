# 🌿 Guide d'Installation Pixabay pour Vidéos Nature

Intégrez de magnifiques vidéos de nature dans vos créations YouTube automatiques !

## 🎯 Pourquoi Pixabay ?

### Avantages
- **✅ Gratuit** : API gratuite avec 100 requêtes/minute
- **🌿 Contenu nature** : Millions de vidéos de nature HD
- **📜 Licence libre** : Utilisation commerciale autorisée
- **🎬 Qualité HD** : Vidéos jusqu'à 4K disponibles
- **🔍 Recherche intelligente** : Sélection automatique selon votre contenu

### Types de vidéos disponibles
- **🏔️ Paysages** : Montagnes, vallées, horizons
- **🌊 Éléments aquatiques** : Océans, rivières, cascades
- **🌳 Forêts** : Arbres, jungle, feuillages
- **🌸 Flore** : Fleurs, jardins, prairies
- **🌤️ Ciel** : Nuages, couchers de soleil, orages
- **🦅 Faune** : Oiseaux, animaux sauvages
- **🌍 Environnement** : Énergies renouvelables, écologie

## 🚀 Installation en 3 étapes

### Étape 1 : Créer un compte Pixabay

1. **Visitez** [https://pixabay.com/](https://pixabay.com/)
2. **Cliquez** sur "Join" (Rejoindre)
3. **Créez** votre compte gratuit
4. **Confirmez** votre email

### Étape 2 : Obtenir votre API Key

1. **Connectez-vous** à votre compte Pixabay
2. **Allez** sur [https://pixabay.com/api/docs/](https://pixabay.com/api/docs/)
3. **Cliquez** sur "Get API Key" ou "Obtenir la clé API"
4. **Copiez** votre clé API (format: `12345678-abcdef123456789`)

### Étape 3 : Configuration

#### Sur macOS/Linux :
```bash
# Ajoutez à votre ~/.bashrc ou ~/.zshrc
export PIXABAY_API_KEY="votre_cle_api_ici"

# Rechargez votre terminal
source ~/.bashrc  # ou ~/.zshrc
```

#### Sur Windows :
```cmd
# Dans l'invite de commande
set PIXABAY_API_KEY=votre_cle_api_ici

# Ou via les variables d'environnement système
```

## 🧪 Test de l'installation

```bash
cd Documents/aicreation
python test_pixabay.py
```

Si tout est configuré correctement, vous devriez voir :
```
✅ API Key Pixabay: Configurée
✅ Gestionnaire Pixabay initialisé
✅ Téléchargement réussi
🎉 TOUS LES TESTS RÉUSSIS!
```

## 🎬 Utilisation

### Génération automatique avec nature

Une fois configuré, vos vidéos incluront automatiquement des séquences nature :

```bash
# Test avec intégration nature
python test_single_keyword.py "intelligence artificielle"

# Le système sélectionnera automatiquement des vidéos nature appropriées
```

### Styles de composition disponibles

Le système propose plusieurs styles d'intégration :

#### 1. **Overlay** (Par défaut)
- Vidéo nature en arrière-plan
- Texte superposé par-dessus
- Opacité ajustable

#### 2. **Transitions**
- Alternance entre contenu et nature
- Transitions fluides
- Rythme dynamique

#### 3. **Picture-in-Picture**
- Petite fenêtre nature dans un coin
- Contenu principal au centre
- Discret mais efficace

## 🎯 Sélection Automatique des Vidéos

Le système choisit automatiquement les vidéos nature selon votre contenu :

### Technologie → Nature moderne
- **IA/Tech** : Éoliennes, panneaux solaires, paysages futuristes
- **Innovation** : Énergies renouvelables, technologie verte

### Environnement → Nature environnementale
- **Climat** : Glaciers, tempêtes, sécheresse
- **Écologie** : Forêts pristines, eau claire, nature préservée

### Santé/Bien-être → Nature apaisante
- **Santé** : Paysages paisibles, eau calme, jardins zen
- **Méditation** : Lacs tranquilles, forêts sereines

### Business → Nature professionnelle
- **Finance** : Paysages de croissance, nature prospère
- **Entreprise** : Environnements corporatifs naturels

### Éducation → Nature éducative
- **Science** : Nature scientifique, recherche environnementale
- **Apprentissage** : Exploration, découverte naturelle

## ⚙️ Configuration Avancée

### Personnaliser les paramètres

Modifiez `config.py` pour ajuster l'intégration :

```python
PIXABAY_CONFIG = {
    "enabled": True,
    "videos_per_keyword": 3,        # Nombre de vidéos à télécharger
    "max_video_duration": 60,       # Durée max en secondes
    "min_video_quality": "HD",      # HD ou FHD
    "composition_style": "overlay", # Style de composition
    "nature_opacity": 0.7,          # Opacité de la nature (0.0-1.0)
    "download_timeout": 60          # Timeout de téléchargement
}
```

### Mots-clés personnalisés

Ajoutez vos propres associations dans `pixabay_videos.py` :

```python
self.keyword_to_nature = {
    "votre_sujet": ["mot-clé nature 1", "mot-clé nature 2"],
    # ... autres associations
}
```

## 💰 Limites et Coûts

### API Gratuite Pixabay
- **100 requêtes/minute** : Largement suffisant pour usage normal
- **5000 requêtes/heure** : Pour usage intensif
- **Pas de limite mensuelle** : Utilisation illimitée
- **Contenu commercial** : Autorisé sans frais supplémentaires

### Optimisation des requêtes
- Le système met en cache les résultats
- Téléchargement intelligent (évite les doublons)
- Sélection automatique des meilleures vidéos

## 🔍 Dépannage

### Problèmes courants

#### "API Key not configured"
```bash
# Vérifiez votre variable d'environnement
echo $PIXABAY_API_KEY

# Reconfigurez si nécessaire
export PIXABAY_API_KEY="votre_cle"
```

#### "No videos found"
- Vérifiez votre connexion internet
- Essayez avec des mots-clés plus généraux
- Vérifiez que votre API key est valide

#### "Download failed"
- Vérifiez l'espace disque disponible
- Vérifiez les permissions du dossier `output/temp/`
- Essayez avec une connexion plus stable

### Logs détaillés

Activez les logs pour diagnostiquer :

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 Exemples de Résultats

### Avant (sans nature)
- Vidéo avec arrière-plan uni
- Contenu textuel uniquement
- Aspect basique

### Après (avec nature)
- **Arrière-plan dynamique** avec paysages magnifiques
- **Ambiance visuelle** adaptée au contenu
- **Engagement accru** grâce aux visuels naturels
- **Qualité professionnelle** comparable aux chaînes premium

## 🎉 Résultats Attendus

Avec l'intégration Pixabay, vos vidéos auront :

- **🌿 Visuels naturels** adaptés au contenu
- **📈 Engagement amélioré** grâce aux images attrayantes
- **🎬 Qualité professionnelle** comparable aux grandes chaînes
- **🌍 Diversité visuelle** avec des paysages du monde entier
- **⚡ Génération automatique** sans intervention manuelle

## 🔗 Ressources

- [Site officiel Pixabay](https://pixabay.com/)
- [Documentation API](https://pixabay.com/api/docs/)
- [Conditions d'utilisation](https://pixabay.com/service/terms/)
- [Licence Pixabay](https://pixabay.com/service/license/)

## 📝 Notes importantes

1. **Respectez les conditions** : Lisez les conditions d'utilisation Pixabay
2. **Crédits optionnels** : Bien que non obligatoires, les crédits sont appréciés
3. **Sauvegardez votre API key** : Gardez-la en sécurité
4. **Surveillez l'usage** : Respectez les limites de l'API gratuite
5. **Testez régulièrement** : Utilisez `test_pixabay.py` pour vérifier le fonctionnement

---

🌿 **Transformez vos vidéos YouTube avec de magnifiques séquences nature gratuites !**
