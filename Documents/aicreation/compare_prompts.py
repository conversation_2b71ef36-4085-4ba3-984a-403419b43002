#!/usr/bin/env python3
"""
compare_prompts.py - Compare old vs new prompt quality
"""

import json
import time
from pathlib import Path
from ollama_manager import OllamaManager

def analyze_script_quality(script_data, keyword):
    """Analyze the quality of a generated script."""
    analysis = {
        "keyword": keyword,
        "title_length": len(script_data.get("title", "")),
        "title_has_numbers": any(char.isdigit() for char in script_data.get("title", "")),
        "title_has_punctuation": any(char in "!?:" for char in script_data.get("title", "")),
        "title_has_power_words": any(word.lower() in script_data.get("title", "").lower() 
                                   for word in ["secret", "shocking", "revealed", "truth", "exposed", "crash", "now", "everything"]),
        "hook_strength": "shocking" in script_data.get("introduction", "").lower() or 
                        "imagine" in script_data.get("introduction", "").lower() or
                        any(char.isdigit() for char in script_data.get("introduction", "")),
        "has_statistics": any(any(char.isdigit() for char in script_data.get(key, "")) 
                            for key in ["key_point_1", "key_point_2", "key_point_3"]),
        "conclusion_strength": "question" in script_data.get("conclusion", "").lower() or
                             "?" in script_data.get("conclusion", ""),
        "total_word_count": sum(len(script_data.get(key, "").split()) 
                              for key in ["introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion"])
    }
    
    # Calculate quality score
    score = 0
    if analysis["title_length"] <= 60: score += 1
    if analysis["title_has_numbers"]: score += 1
    if analysis["title_has_punctuation"]: score += 1
    if analysis["title_has_power_words"]: score += 2
    if analysis["hook_strength"]: score += 2
    if analysis["has_statistics"]: score += 2
    if analysis["conclusion_strength"]: score += 1
    if 150 <= analysis["total_word_count"] <= 250: score += 1
    
    analysis["quality_score"] = score
    analysis["max_score"] = 11
    analysis["quality_percentage"] = (score / 11) * 100
    
    return analysis

def test_keyword_quality(keyword):
    """Test script generation quality for a keyword."""
    print(f"\n🎯 Testing keyword: '{keyword}'")
    print("=" * 50)
    
    ollama_manager = OllamaManager()
    
    if not ollama_manager.available:
        print("❌ Ollama not available")
        return None
    
    # Generate script with enhanced prompt
    print("🚀 Generating script with enhanced YouTube-optimized prompt...")
    start_time = time.time()
    
    script = ollama_manager.generate_script(keyword)
    
    generation_time = time.time() - start_time
    
    if not script:
        print("❌ Failed to generate script")
        return None
    
    # Analyze quality
    analysis = analyze_script_quality(script, keyword)
    
    print(f"⏱️  Generation time: {generation_time:.1f} seconds")
    print(f"📊 Quality score: {analysis['quality_score']}/{analysis['max_score']} ({analysis['quality_percentage']:.1f}%)")
    print()
    
    # Display results
    print("📝 GENERATED SCRIPT:")
    print(f"Title: {script['title']}")
    print(f"  • Length: {analysis['title_length']} chars")
    print(f"  • Has numbers: {'✅' if analysis['title_has_numbers'] else '❌'}")
    print(f"  • Has punctuation: {'✅' if analysis['title_has_punctuation'] else '❌'}")
    print(f"  • Has power words: {'✅' if analysis['title_has_power_words'] else '❌'}")
    print()
    
    print("🎣 Hook Analysis:")
    print(f"Introduction: {script['introduction'][:100]}...")
    print(f"  • Hook strength: {'✅' if analysis['hook_strength'] else '❌'}")
    print()
    
    print("📈 Content Analysis:")
    print(f"  • Has statistics: {'✅' if analysis['has_statistics'] else '❌'}")
    print(f"  • Total words: {analysis['total_word_count']}")
    print(f"  • Conclusion strength: {'✅' if analysis['conclusion_strength'] else '❌'}")
    print()
    
    return analysis

def main():
    """Main comparison function."""
    print("🔬 YOUTUBE SCRIPT QUALITY ANALYZER")
    print("=" * 50)
    print("Testing the enhanced YouTube-optimized prompt system")
    print()
    
    # Test keywords
    test_keywords = [
        "artificial intelligence",
        "cryptocurrency investing", 
        "climate change solutions",
        "space exploration",
        "quantum computing"
    ]
    
    results = []
    
    for keyword in test_keywords:
        analysis = test_keyword_quality(keyword)
        if analysis:
            results.append(analysis)
        
        # Small delay between tests
        time.sleep(2)
    
    # Summary
    if results:
        print("\n📊 QUALITY SUMMARY:")
        print("=" * 50)
        
        avg_score = sum(r["quality_score"] for r in results) / len(results)
        avg_percentage = sum(r["quality_percentage"] for r in results) / len(results)
        
        print(f"Average Quality Score: {avg_score:.1f}/{results[0]['max_score']} ({avg_percentage:.1f}%)")
        print()
        
        print("Individual Results:")
        for result in results:
            print(f"  • {result['keyword']}: {result['quality_score']}/{result['max_score']} ({result['quality_percentage']:.1f}%)")
        
        print()
        print("🎉 Enhanced prompt system analysis complete!")
        print("💡 Higher scores indicate better YouTube optimization")

if __name__ == "__main__":
    main()
