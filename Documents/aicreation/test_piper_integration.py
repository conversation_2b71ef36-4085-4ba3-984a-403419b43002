#!/usr/bin/env python3
"""
test_piper_integration.py - Test de l'intégration Piper dans le système
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_piper_module():
    """Test the Piper TTS module."""
    print("🎤 TEST DU MODULE PIPER TTS")
    print("=" * 40)
    
    try:
        from piper_tts import PiperTTS
        
        piper = PiperTTS()
        
        print(f"📍 Exécutable: {piper.piper_executable}")
        print(f"📁 Dossier modèles: {piper.models_dir}")
        print(f"✅ Disponible: {piper.available}")
        
        if piper.available:
            print("✅ Piper TTS module fonctionne!")
            
            # Test voice selection
            test_text = "Bonjour, ceci est un test de Piper TTS intégré."
            voice_config = piper.get_voice_for_content(test_text, "nature")
            print(f"🎵 Voix sélectionnée: {voice_config}")
            
            # List available voices
            voices = piper.list_available_voices()
            print(f"🎭 Voix disponibles: {voices}")
            
            return True
        else:
            print("❌ Piper TTS non disponible")
            return False
            
    except ImportError:
        print("❌ Module piper_tts non trouvé")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_audio_manager_integration():
    """Test Piper integration in AudioManager."""
    print("\n🎵 TEST INTÉGRATION AUDIO MANAGER")
    print("-" * 40)
    
    try:
        from audio_manager import AudioManager
        
        audio_manager = AudioManager()
        
        print(f"🎤 Systèmes TTS disponibles: {list(audio_manager.tts_systems.keys())}")
        
        if "piper" in audio_manager.tts_systems:
            print("✅ Piper TTS intégré dans AudioManager!")
            
            piper_system = audio_manager.tts_systems["piper"]
            print(f"📍 Piper disponible: {piper_system.available}")
            
            return True
        else:
            print("⚠️ Piper TTS non intégré dans AudioManager")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_video_creation_with_piper():
    """Test video creation with Piper TTS."""
    print("\n🎬 TEST CRÉATION VIDÉO AVEC PIPER")
    print("-" * 40)
    
    try:
        from enhanced_auto_youtube import EnhancedAutoYouTube
        
        # Initialize system
        auto_youtube = EnhancedAutoYouTube()
        
        # Check if Piper is available in the audio manager
        if hasattr(auto_youtube, 'audio_manager') and auto_youtube.audio_manager:
            if "piper" in auto_youtube.audio_manager.tts_systems:
                print("✅ Piper TTS disponible dans le système complet")
                
                # Test with a simple keyword
                test_keyword = "piper test"
                print(f"🧪 Test avec le mot-clé: '{test_keyword}'")
                
                # This would create a full video, but we'll just test the audio part
                script = {
                    "title": "Test Piper TTS",
                    "introduction": "Bonjour et bienvenue dans ce test de Piper TTS.",
                    "key_point_1": "Piper TTS est un système de synthèse vocale local de haute qualité.",
                    "key_point_2": "Il produit des voix très naturelles sans nécessiter de connexion internet.",
                    "key_point_3": "L'intégration dans notre système permet d'utiliser Piper automatiquement.",
                    "conclusion": "Merci d'avoir écouté ce test de Piper TTS. La qualité vocale est excellente!"
                }
                
                print("🔄 Génération de l'audio avec Piper...")
                audio_path = auto_youtube.audio_manager.generate_audio(script, test_keyword)
                
                if audio_path and Path(audio_path).exists():
                    file_size = Path(audio_path).stat().st_size / (1024 * 1024)
                    print(f"✅ Audio généré avec succès!")
                    print(f"📁 Fichier: {audio_path}")
                    print(f"📊 Taille: {file_size:.1f} MB")
                    return True
                else:
                    print("❌ Échec de génération audio")
                    return False
            else:
                print("⚠️ Piper TTS non disponible dans le système")
                return False
        else:
            print("❌ AudioManager non disponible")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_piper_nature_video():
    """Create a nature video specifically with Piper TTS."""
    print("\n🌿 CRÉATION VIDÉO NATURE AVEC PIPER")
    print("-" * 40)
    
    try:
        from simple_nature_composer import SimpleNatureComposer
        from audio_manager import AudioManager
        
        # Create audio with Piper
        audio_manager = AudioManager()
        
        if "piper" not in audio_manager.tts_systems:
            print("❌ Piper TTS non disponible")
            return False
        
        # Force use of Piper by temporarily removing other systems
        original_systems = audio_manager.tts_systems.copy()
        audio_manager.tts_systems = {"piper": original_systems["piper"]}
        
        # Create nature script
        nature_script = {
            "title": "La Beauté de la Nature avec Piper TTS",
            "introduction": "Découvrez la magnificence de la nature à travers cette vidéo générée avec Piper TTS.",
            "key_point_1": "La nature nous offre des paysages à couper le souffle, des forêts verdoyantes aux océans infinis.",
            "key_point_2": "Chaque élément naturel contribue à l'équilibre de notre écosystème fragile et précieux.",
            "key_point_3": "Préserver la nature est essentiel pour les générations futures et notre bien-être.",
            "conclusion": "Merci d'avoir regardé cette vidéo sur la nature, générée avec la voix naturelle de Piper TTS."
        }
        
        print("🔄 Génération audio avec Piper TTS...")
        audio_path = audio_manager.generate_audio(nature_script, "nature_piper")
        
        if not audio_path:
            print("❌ Échec génération audio")
            return False
        
        print(f"✅ Audio Piper généré: {audio_path}")
        
        # Create video with nature background
        composer = SimpleNatureComposer()
        
        print("🎬 Création vidéo avec arrière-plan nature...")
        video_path = composer.create_nature_video(audio_path, "nature_piper", add_music=True)
        
        if video_path and Path(video_path).exists():
            video_size = Path(video_path).stat().st_size / (1024 * 1024)
            print(f"✅ Vidéo nature avec Piper créée!")
            print(f"📁 Fichier: {video_path}")
            print(f"📊 Taille: {video_size:.1f} MB")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
            return True
        else:
            print("❌ Échec création vidéo")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Main test function."""
    print("🎤 TEST COMPLET INTÉGRATION PIPER TTS")
    print("=" * 50)
    
    tests = [
        ("Module Piper TTS", test_piper_module),
        ("Intégration AudioManager", test_audio_manager_integration),
        ("Système complet", test_video_creation_with_piper),
        ("Vidéo nature avec Piper", create_piper_nature_video)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DES TESTS:")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 PIPER TTS PARFAITEMENT INTÉGRÉ!")
        print("🌟 Votre système utilise maintenant Piper TTS en priorité")
        print("🎵 Qualité vocale locale et gratuite")
    elif passed > 0:
        print(f"\n⚠️ Intégration partielle ({passed}/{total})")
        print("🔧 Vérifiez la configuration Piper")
    else:
        print("\n❌ Intégration échouée")
        print("💡 Vérifiez l'installation et la configuration Piper")

if __name__ == "__main__":
    main()
