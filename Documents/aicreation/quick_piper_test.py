#!/usr/bin/env python3
"""
quick_piper_test.py - Test rapide de Piper
"""

import os
import subprocess
from pathlib import Path

def quick_test():
    """Quick test of Piper."""
    print("🎤 TEST RAPIDE PIPER")
    print("=" * 30)
    
    # Check if piper executable exists
    piper_path = "./piper/piper"
    
    if not Path(piper_path).exists():
        print(f"❌ Piper non trouvé: {piper_path}")
        return False
    
    print(f"✅ Piper trouvé: {piper_path}")
    
    # Set environment
    env = os.environ.copy()
    env['DYLD_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    
    # Try simple command
    try:
        print("🧪 Test commande --version...")
        result = subprocess.run([piper_path, "--version"], 
                              capture_output=True, text=True, 
                              timeout=5, env=env)
        
        if result.returncode == 0:
            print(f"✅ Version: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Erreur: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    if quick_test():
        print("🎉 Piper fonctionne!")
    else:
        print("❌ Piper ne fonctionne pas")
        print("\n💡 Solutions:")
        print("1. chmod +x ./piper/piper")
        print("2. xattr -d com.apple.quarantine ./piper/piper")
        print("3. brew install espeak-ng")
