#!/usr/bin/env python3
"""
Test simple pour SkyReels-A1 - Génération d'images IA
Test optimisé pour macOS avec fallback CPU
"""

import os
import sys
import time
import traceback
from pathlib import Path
import numpy as np
from PIL import Image

# Ajouter le répertoire courant au path
sys.path.insert(0, os.getcwd())

try:
    from skyreels_manager import SkyReelsManager
    print("✅ Import SkyReelsManager réussi")
except ImportError as e:
    print(f"❌ Erreur import SkyReelsManager: {e}")
    sys.exit(1)

def test_skyreels_functionality():
    """Test complet de SkyReels avec gestion d'erreurs"""
    print("🚀 Test SkyReels-A1 Image Generation")
    print("=" * 50)
    
    # Vérification des modèles
    models_path = Path("pretrained_models/SkyReels-A1-5B")
    print(f"📁 Vérification des modèles: {models_path}")
    
    if not models_path.exists():
        print(f"❌ Répertoire des modèles non trouvé: {models_path}")
        return False
    
    # Vérification des composants
    required_components = [
        "siglip-so400m-patch14-384",
        "text_encoder", 
        "transformer",
        "vae",
        "pose_guider"
    ]
    
    missing_components = []
    for component in required_components:
        component_path = models_path / component
        if component_path.exists():
            print(f"  ✅ {component}")
        else:
            print(f"  ❌ {component} - MANQUANT")
            missing_components.append(component)
    
    if missing_components:
        print(f"\n❌ Composants manquants: {missing_components}")
        print("Veuillez télécharger les modèles SkyReels-A1-5B complets")
        return False
    
    print("\n🔧 Initialisation SkyReelsManager...")
    
    try:
        # Configuration optimisée pour macOS
        config_path = "skyreels_config.yaml"
        manager = SkyReelsManager(config_path)
        print("✅ SkyReelsManager initialisé")
        
        # Test de génération d'image simple
        print("\n🎨 Test génération d'image...")
        test_prompt = "beautiful landscape, high quality, cinematic"
        print(f"📝 Prompt: {test_prompt}")
        
        start_time = time.time()
        
        # Génération avec paramètres réduits pour test
        image = manager.generate_image(
            prompt=test_prompt,
            width=512,
            height=512,
            num_inference_steps=20,  # Réduit pour accélérer
            guidance_scale=7.5
        )
        
        generation_time = time.time() - start_time
        print(f"⏱️ Temps de génération: {generation_time:.2f}s")
        
        if image is not None:
            print("✅ Image générée avec succès!")
            print(f"📐 Dimensions: {image.shape}")
            print(f"🎨 Type: {image.dtype}")
            
            # Sauvegarde de l'image de test
            output_dir = Path("output/test_images")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Conversion en PIL et sauvegarde
            if isinstance(image, np.ndarray):
                # Normalisation si nécessaire
                if image.max() <= 1.0:
                    image = (image * 255).astype(np.uint8)
                
                pil_image = Image.fromarray(image)
                output_path = output_dir / f"skyreels_test_{int(time.time())}.png"
                pil_image.save(output_path)
                print(f"💾 Image sauvegardée: {output_path}")
                print(f"📦 Taille fichier: {output_path.stat().st_size / 1024:.1f} KB")
                
                # Affichage des statistiques
                print(f"\n📊 Statistiques de l'image:")
                print(f"   Min pixel: {image.min()}")
                print(f"   Max pixel: {image.max()}")
                print(f"   Moyenne: {image.mean():.2f}")
                
                return True
            else:
                print(f"❌ Format d'image inattendu: {type(image)}")
                return False
        else:
            print("❌ Échec de la génération d'image")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        print(f"📋 Traceback complet:")
        traceback.print_exc()
        return False
    
    finally:
        # Nettoyage
        try:
            if 'manager' in locals():
                manager.cleanup()
                print("🧹 Nettoyage effectué")
        except:
            pass

def test_system_requirements():
    """Vérifie les prérequis système"""
    print("\n🔍 Vérification des prérequis système")
    print("-" * 40)
    
    # Python version
    python_version = sys.version_info
    print(f"🐍 Python: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Modules requis
    required_modules = [
        "torch", "torchvision", "transformers", 
        "diffusers", "PIL", "numpy", "yaml"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module} - MANQUANT")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Modules manquants: {missing_modules}")
        print("Installez avec: pip install torch torchvision transformers diffusers pillow numpy pyyaml")
        return False
    
    # Vérification de PyTorch
    try:
        import torch
        print(f"🔥 PyTorch: {torch.__version__}")
        print(f"💻 CUDA disponible: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
        else:
            print("🖥️ Mode CPU (recommandé pour macOS)")
    except Exception as e:
        print(f"❌ Erreur PyTorch: {e}")
        return False
    
    return True

def main():
    """Fonction principale de test"""
    print("🎬 SkyReels-A1 Test Suite")
    print("=" * 60)
    
    # Test des prérequis
    if not test_system_requirements():
        print("\n❌ Prérequis non satisfaits")
        return
    
    # Test de fonctionnalité
    success = test_skyreels_functionality()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ SkyReels-A1 fonctionne correctement")
        print("🎨 Génération d'images IA opérationnelle")
    else:
        print("❌ ÉCHEC DES TESTS")
        print("🔧 Vérifiez la configuration et les modèles")
    
    print("\n📋 Prochaines étapes:")
    print("   1. Intégrer SkyReels dans le créateur de vidéos")
    print("   2. Tester avec différents prompts")
    print("   3. Optimiser les performances")

if __name__ == "__main__":
    main()
