#!/usr/bin/env python3
"""
audio_manager.py - Enhanced audio generation and processing
"""

import logging
import os
import subprocess
from pathlib import Path
from typing import Dict, Optional, List
import time

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    logging.warning("gTTS not available. Audio generation will be limited.")

try:
    from pydub import AudioSegment
    from pydub.effects import normalize, compress_dynamic_range
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    logging.warning("pydub not available. Audio processing will be limited.")

from config import get_config

logger = logging.getLogger(__name__)

class AudioManager:
    """Enhanced audio manager for generating and processing speech."""
    
    def __init__(self):
        self.config = get_config()
        self.audio_config = self.config["audio"]
        self.directories = self.config["directories"]
        
    def _create_speech_text(self, script: Dict[str, str]) -> str:
        """Create optimized speech text from script."""
        # Add natural pauses and emphasis
        speech_parts = []

        # Helper function to extract text from various formats
        def extract_text(value):
            if isinstance(value, str):
                return value
            elif isinstance(value, dict):
                return value.get("text", str(value))
            else:
                return str(value)

        # Introduction with pause
        intro = script.get("introduction", "")
        if intro:
            intro_text = extract_text(intro)
            speech_parts.append(f"{intro_text}... ")

        # Key points with pauses between them
        for i in range(1, 4):
            key = f"key_point_{i}"
            point = script.get(key, "")
            if point:
                point_text = extract_text(point)
                speech_parts.append(f"{point_text}... ")

        # Conclusion
        conclusion = script.get("conclusion", "")
        if conclusion:
            conclusion_text = extract_text(conclusion)
            speech_parts.append(conclusion_text)

        return " ".join(speech_parts)
    
    def _generate_basic_audio(self, text: str, output_path: Path) -> bool:
        """Generate basic audio using gTTS."""
        if not GTTS_AVAILABLE:
            logger.error("gTTS not available for audio generation")
            return False
        
        try:
            # Split text into chunks if it's too long
            max_chunk_length = 500  # gTTS has limitations
            chunks = []
            
            if len(text) <= max_chunk_length:
                chunks = [text]
            else:
                # Split by sentences
                sentences = text.split('. ')
                current_chunk = ""
                
                for sentence in sentences:
                    if len(current_chunk + sentence) <= max_chunk_length:
                        current_chunk += sentence + ". "
                    else:
                        if current_chunk:
                            chunks.append(current_chunk.strip())
                        current_chunk = sentence + ". "
                
                if current_chunk:
                    chunks.append(current_chunk.strip())
            
            # Generate audio for each chunk
            audio_segments = []
            temp_files = []
            
            for i, chunk in enumerate(chunks):
                temp_path = output_path.parent / f"temp_chunk_{i}.mp3"
                temp_files.append(temp_path)
                
                tts = gTTS(
                    text=chunk,
                    lang=self.audio_config["language"],
                    slow=self.audio_config["slow"]
                )
                tts.save(str(temp_path))
                
                if PYDUB_AVAILABLE:
                    segment = AudioSegment.from_mp3(str(temp_path))
                    audio_segments.append(segment)
            
            # Combine chunks if multiple
            if PYDUB_AVAILABLE and len(audio_segments) > 1:
                combined = AudioSegment.empty()
                for segment in audio_segments:
                    combined += segment
                    combined += AudioSegment.silent(duration=500)  # 0.5 second pause
                
                combined.export(str(output_path), format="mp3")
            elif len(chunks) == 1:
                # Just rename the single file
                temp_files[0].rename(output_path)
            else:
                # Fallback: use the first chunk
                temp_files[0].rename(output_path)
            
            # Clean up temp files
            for temp_file in temp_files:
                if temp_file.exists() and temp_file != output_path:
                    temp_file.unlink()
            
            logger.info(f"Generated basic audio: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating basic audio: {e}")
            return False
    
    def _enhance_audio(self, input_path: Path, output_path: Path) -> bool:
        """Enhance audio quality using pydub."""
        if not PYDUB_AVAILABLE:
            logger.warning("pydub not available. Skipping audio enhancement.")
            if input_path != output_path:
                input_path.rename(output_path)
            return True
        
        try:
            # Load audio
            audio = AudioSegment.from_mp3(str(input_path))
            
            # Normalize audio levels
            audio = normalize(audio)
            
            # Apply dynamic range compression
            audio = compress_dynamic_range(audio)
            
            # Adjust volume to optimal level
            target_dBFS = -20.0
            change_in_dBFS = target_dBFS - audio.dBFS
            audio = audio.apply_gain(change_in_dBFS)
            
            # Add fade in/out
            audio = audio.fade_in(1000).fade_out(1000)  # 1 second fade
            
            # Export enhanced audio
            audio.export(str(output_path), format="mp3", bitrate="192k")
            
            logger.info(f"Enhanced audio: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error enhancing audio: {e}")
            # Fallback: copy original file
            if input_path != output_path:
                input_path.rename(output_path)
            return False
    
    def _add_background_music(self, speech_path: Path, output_path: Path) -> bool:
        """Add subtle background music to speech."""
        if not PYDUB_AVAILABLE:
            logger.warning("pydub not available. Skipping background music.")
            if speech_path != output_path:
                speech_path.rename(output_path)
            return True
        
        try:
            # Load speech audio
            speech = AudioSegment.from_mp3(str(speech_path))
            
            # Create a simple background tone (optional)
            # This is a placeholder - you could add actual background music files
            background_freq = 220  # A3 note
            background = AudioSegment.sine(background_freq, duration=len(speech))
            
            # Make background very quiet
            background = background - 40  # Reduce volume by 40dB
            
            # Mix speech with background
            mixed = speech.overlay(background)
            
            # Export mixed audio
            mixed.export(str(output_path), format="mp3", bitrate="192k")
            
            logger.info(f"Added background music: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding background music: {e}")
            # Fallback: copy original file
            if speech_path != output_path:
                speech_path.rename(output_path)
            return False
    
    def generate_audio(self, script: Dict[str, str], keyword: str) -> Optional[str]:
        """Generate enhanced audio from script."""
        try:
            # Create speech text
            speech_text = self._create_speech_text(script)
            
            if not speech_text.strip():
                logger.error("No speech text generated from script")
                return None
            
            # Create output paths
            base_filename = keyword.replace(' ', '_')
            temp_path = self.directories["temp"] / f"{base_filename}_temp.mp3"
            enhanced_path = self.directories["temp"] / f"{base_filename}_enhanced.mp3"
            final_path = self.directories["audio"] / f"{base_filename}.mp3"
            
            # Generate basic audio
            if not self._generate_basic_audio(speech_text, temp_path):
                logger.error("Failed to generate basic audio")
                return None
            
            # Enhance audio quality
            if not self._enhance_audio(temp_path, enhanced_path):
                logger.warning("Audio enhancement failed, using basic audio")
                enhanced_path = temp_path
            
            # Add background music (optional)
            if self.audio_config.get("add_background_music", False):
                self._add_background_music(enhanced_path, final_path)
            else:
                enhanced_path.rename(final_path)
            
            # Clean up temp files
            for temp_file in [temp_path, enhanced_path]:
                if temp_file.exists() and temp_file != final_path:
                    temp_file.unlink(missing_ok=True)
            
            # Verify final file exists
            if final_path.exists():
                file_size = final_path.stat().st_size
                logger.info(f"Generated audio file: {final_path} ({file_size} bytes)")
                return str(final_path)
            else:
                logger.error("Final audio file was not created")
                return None
                
        except Exception as e:
            logger.error(f"Error generating audio for '{keyword}': {e}")
            return None
    
    def get_audio_duration(self, audio_path: str) -> Optional[float]:
        """Get audio duration in seconds."""
        try:
            if PYDUB_AVAILABLE:
                audio = AudioSegment.from_mp3(audio_path)
                return len(audio) / 1000.0  # Convert to seconds
            else:
                # Fallback using ffprobe
                result = subprocess.run([
                    "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                    "-of", "csv=p=0", audio_path
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    return float(result.stdout.strip())
                
        except Exception as e:
            logger.error(f"Error getting audio duration: {e}")
        
        return None
    
    def validate_audio(self, audio_path: str) -> bool:
        """Validate that audio file is properly formatted."""
        try:
            path = Path(audio_path)
            if not path.exists():
                return False
            
            # Check file size
            if path.stat().st_size < 1000:  # Less than 1KB
                logger.warning(f"Audio file seems too small: {path.stat().st_size} bytes")
                return False
            
            # Check duration
            duration = self.get_audio_duration(audio_path)
            if duration and duration < 10:  # Less than 10 seconds
                logger.warning(f"Audio duration seems too short: {duration} seconds")
                return False
            
            logger.info(f"Audio validation passed: {audio_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error validating audio: {e}")
            return False
