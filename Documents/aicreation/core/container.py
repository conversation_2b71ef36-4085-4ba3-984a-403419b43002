"""
Dependency Injection Container.

This module provides a lightweight dependency injection container that manages
the lifecycle and dependencies of application components.
"""

import inspect
from typing import Any, Dict, Type, TypeVar, Callable, Optional, List
from functools import wraps
from enum import Enum

from .logging import get_logger
from .exceptions import ConfigurationError


T = TypeVar('T')


class Lifetime(Enum):
    """Service lifetime options."""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class ServiceDescriptor:
    """Describes how a service should be created and managed."""
    
    def __init__(
        self,
        service_type: Type,
        implementation: Optional[Type] = None,
        factory: Optional[Callable] = None,
        instance: Optional[Any] = None,
        lifetime: Lifetime = Lifetime.TRANSIENT
    ):
        self.service_type = service_type
        self.implementation = implementation or service_type
        self.factory = factory
        self.instance = instance
        self.lifetime = lifetime
        
        if not any([implementation, factory, instance]):
            raise ValueError("Must provide implementation, factory, or instance")


class DependencyContainer:
    """Lightweight dependency injection container."""
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._singletons: Dict[Type, Any] = {}
        self._scoped_instances: Dict[Type, Any] = {}
        self.logger = get_logger("container")
    
    def register_singleton(
        self, 
        service_type: Type[T], 
        implementation: Optional[Type[T]] = None,
        factory: Optional[Callable[[], T]] = None,
        instance: Optional[T] = None
    ) -> 'DependencyContainer':
        """Register a singleton service."""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation=implementation,
            factory=factory,
            instance=instance,
            lifetime=Lifetime.SINGLETON
        )
        self._services[service_type] = descriptor
        
        if instance is not None:
            self._singletons[service_type] = instance
        
        self.logger.debug(f"Registered singleton: {service_type.__name__}")
        return self
    
    def register_transient(
        self, 
        service_type: Type[T], 
        implementation: Optional[Type[T]] = None,
        factory: Optional[Callable[[], T]] = None
    ) -> 'DependencyContainer':
        """Register a transient service."""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation=implementation,
            factory=factory,
            lifetime=Lifetime.TRANSIENT
        )
        self._services[service_type] = descriptor
        
        self.logger.debug(f"Registered transient: {service_type.__name__}")
        return self
    
    def register_scoped(
        self, 
        service_type: Type[T], 
        implementation: Optional[Type[T]] = None,
        factory: Optional[Callable[[], T]] = None
    ) -> 'DependencyContainer':
        """Register a scoped service."""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation=implementation,
            factory=factory,
            lifetime=Lifetime.SCOPED
        )
        self._services[service_type] = descriptor
        
        self.logger.debug(f"Registered scoped: {service_type.__name__}")
        return self
    
    def resolve(self, service_type: Type[T]) -> T:
        """Resolve a service instance."""
        if service_type not in self._services:
            raise ConfigurationError(f"Service {service_type.__name__} not registered")
        
        descriptor = self._services[service_type]
        
        # Handle singleton
        if descriptor.lifetime == Lifetime.SINGLETON:
            if service_type in self._singletons:
                return self._singletons[service_type]
            
            instance = self._create_instance(descriptor)
            self._singletons[service_type] = instance
            return instance
        
        # Handle scoped
        elif descriptor.lifetime == Lifetime.SCOPED:
            if service_type in self._scoped_instances:
                return self._scoped_instances[service_type]
            
            instance = self._create_instance(descriptor)
            self._scoped_instances[service_type] = instance
            return instance
        
        # Handle transient
        else:
            return self._create_instance(descriptor)
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """Create an instance from a service descriptor."""
        # Use existing instance
        if descriptor.instance is not None:
            return descriptor.instance
        
        # Use factory
        if descriptor.factory is not None:
            return descriptor.factory()
        
        # Use implementation class
        return self._create_from_class(descriptor.implementation)
    
    def _create_from_class(self, cls: Type) -> Any:
        """Create instance from class with dependency injection."""
        # Get constructor signature
        signature = inspect.signature(cls.__init__)
        parameters = list(signature.parameters.values())[1:]  # Skip 'self'
        
        # Resolve dependencies
        kwargs = {}
        for param in parameters:
            if param.annotation != inspect.Parameter.empty:
                try:
                    kwargs[param.name] = self.resolve(param.annotation)
                except ConfigurationError:
                    if param.default != inspect.Parameter.empty:
                        kwargs[param.name] = param.default
                    else:
                        raise ConfigurationError(
                            f"Cannot resolve dependency {param.annotation.__name__} "
                            f"for {cls.__name__}.{param.name}"
                        )
        
        return cls(**kwargs)
    
    def clear_scoped(self):
        """Clear scoped instances."""
        self._scoped_instances.clear()
        self.logger.debug("Cleared scoped instances")
    
    def is_registered(self, service_type: Type) -> bool:
        """Check if a service type is registered."""
        return service_type in self._services
    
    def get_registered_services(self) -> List[Type]:
        """Get list of registered service types."""
        return list(self._services.keys())


class ServiceScope:
    """Context manager for scoped services."""
    
    def __init__(self, container: DependencyContainer):
        self.container = container
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.container.clear_scoped()


def inject(*dependencies: Type):
    """Decorator for automatic dependency injection."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            container = get_container()
            
            # Resolve dependencies
            for dep_type in dependencies:
                if dep_type.__name__.lower() not in kwargs:
                    kwargs[dep_type.__name__.lower()] = container.resolve(dep_type)
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def auto_inject(func):
    """Decorator for automatic dependency injection based on type hints."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        container = get_container()
        signature = inspect.signature(func)
        
        # Resolve dependencies based on type hints
        for param_name, param in signature.parameters.items():
            if (param_name not in kwargs and 
                param.annotation != inspect.Parameter.empty and
                container.is_registered(param.annotation)):
                kwargs[param_name] = container.resolve(param.annotation)
        
        return func(*args, **kwargs)
    return wrapper


# Global container instance
_container: Optional[DependencyContainer] = None


def get_container() -> DependencyContainer:
    """Get the global dependency container."""
    global _container
    if _container is None:
        _container = DependencyContainer()
    return _container


def configure_container() -> DependencyContainer:
    """Configure the dependency container with default services."""
    container = get_container()
    
    # Import here to avoid circular dependencies
    from .config import get_config
    from .logging import get_logger, get_metrics, get_performance_tracker
    from .cache import get_cache_manager, get_retry_manager
    
    # Register core services
    container.register_singleton(type(get_config()), instance=get_config())
    container.register_singleton(type(get_metrics()), instance=get_metrics())
    container.register_singleton(type(get_performance_tracker()), instance=get_performance_tracker())
    container.register_singleton(type(get_cache_manager()), instance=get_cache_manager())
    container.register_singleton(type(get_retry_manager()), instance=get_retry_manager())
    
    return container


class ServiceFactory:
    """Factory for creating services with dependency injection."""
    
    def __init__(self, container: DependencyContainer):
        self.container = container
    
    def create_service(self, service_type: Type[T]) -> T:
        """Create a service instance."""
        return self.container.resolve(service_type)
    
    def create_with_dependencies(self, cls: Type[T], **overrides) -> T:
        """Create instance with automatic dependency resolution."""
        signature = inspect.signature(cls.__init__)
        parameters = list(signature.parameters.values())[1:]  # Skip 'self'
        
        kwargs = overrides.copy()
        
        for param in parameters:
            if param.name not in kwargs and param.annotation != inspect.Parameter.empty:
                if self.container.is_registered(param.annotation):
                    kwargs[param.name] = self.container.resolve(param.annotation)
                elif param.default != inspect.Parameter.empty:
                    kwargs[param.name] = param.default
        
        return cls(**kwargs)


def get_service_factory() -> ServiceFactory:
    """Get service factory."""
    return ServiceFactory(get_container())


# Convenience functions
def singleton(cls: Type[T]) -> Type[T]:
    """Class decorator to register as singleton."""
    get_container().register_singleton(cls)
    return cls


def transient(cls: Type[T]) -> Type[T]:
    """Class decorator to register as transient."""
    get_container().register_transient(cls)
    return cls


def scoped(cls: Type[T]) -> Type[T]:
    """Class decorator to register as scoped."""
    get_container().register_scoped(cls)
    return cls
