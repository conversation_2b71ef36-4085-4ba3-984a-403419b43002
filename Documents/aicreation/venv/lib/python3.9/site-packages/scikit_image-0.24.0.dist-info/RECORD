scikit_image-0.24.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scikit_image-0.24.0.dist-info/LICENSE.txt,sha256=UfFTRPWXWe1djdttrUjouZS91FsFCoD-WKQZvQ-YrtA,6553
scikit_image-0.24.0.dist-info/METADATA,sha256=acgf51JUu-k3XR8pELd4jMZWaCEM7E61gOQNpmX0cp0,14448
scikit_image-0.24.0.dist-info/RECORD,,
scikit_image-0.24.0.dist-info/WHEEL,sha256=PgW5KKUtTLrgLGQ-97rN_rlK8vNszj7P-t7zdGTL1Ic,91
skimage/.dylibs/libomp.dylib,sha256=VHqiTraNFicKDOBAAnpCh1hX0C-f39wT5QKoUG3ilbg,678720
skimage/__init__.py,sha256=FBUq9oEsb3xSRy5ZEvf8X8Ae9A2-n5o-UKXorLQ9MJE,5550
skimage/__init__.pyi,sha256=TRYozK4YjwWJWfs64FS3xCfq4KBD-obE5uhLpx7z128,568
skimage/__pycache__/__init__.cpython-39.pyc,,
skimage/__pycache__/conftest.cpython-39.pyc,,
skimage/_shared/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_shared/__pycache__/__init__.cpython-39.pyc,,
skimage/_shared/__pycache__/_dependency_checks.cpython-39.pyc,,
skimage/_shared/__pycache__/_geometry.cpython-39.pyc,,
skimage/_shared/__pycache__/_tempfile.cpython-39.pyc,,
skimage/_shared/__pycache__/_warnings.cpython-39.pyc,,
skimage/_shared/__pycache__/compat.cpython-39.pyc,,
skimage/_shared/__pycache__/coord.cpython-39.pyc,,
skimage/_shared/__pycache__/dtype.cpython-39.pyc,,
skimage/_shared/__pycache__/filters.cpython-39.pyc,,
skimage/_shared/__pycache__/tester.cpython-39.pyc,,
skimage/_shared/__pycache__/testing.cpython-39.pyc,,
skimage/_shared/__pycache__/utils.cpython-39.pyc,,
skimage/_shared/__pycache__/version_requirements.cpython-39.pyc,,
skimage/_shared/_dependency_checks.py,sha256=6ZaHZf0d65U5cGMtSEegDUYQ_gJL1wSkC-w9zZkcA18,211
skimage/_shared/_geometry.py,sha256=vagNwhLEcKvM0k19agDXvhnZDjwjTL9au5O-CSuDCQY,1343
skimage/_shared/_tempfile.py,sha256=lWfbjc6DHgndl9D8L4Gflq6fPVfVp-GSOwLgc9mdmpo,763
skimage/_shared/_warnings.py,sha256=daVYJAaNlKvzrYNfoG6uRkz7IQos6zTkqZjnVflsfFE,5225
skimage/_shared/compat.py,sha256=GwI-eClJ6GpQgURalFbNfkH4P0az0441TRRwwPUxqus,976
skimage/_shared/coord.py,sha256=XUT2mOWB5vixHHiUibkdY4o2OAWWS4lnwuUL3Opl7b0,4337
skimage/_shared/dtype.py,sha256=-6l3DaRdHqFRB9EnvMU5xqHxwN7c519Svv47qy1R-Sk,2397
skimage/_shared/fast_exp.cpython-39-darwin.so,sha256=_pC_nNytlk8rSPr5-z7FNAb2LpI0fLJoXtVNqYdpe2Y,94826
skimage/_shared/fast_exp.h,sha256=HtIZ7X68IvcNN47SM5E1SxBgvVYziJKvzvawzO94vi8,1246
skimage/_shared/filters.py,sha256=SOVVk3yTGxoolCMmZGNvPt_DtCAb4TjF5gfbb5qoOt0,5035
skimage/_shared/geometry.cpython-39-darwin.so,sha256=x6qlxtCJAs0p0bkQCF3ximKZ_8529zhJbSNKX4RC4gI,169930
skimage/_shared/interpolation.cpython-39-darwin.so,sha256=uOmF30gIBY-AlrhyOPsb9_SqYCAFGuWWVZdVlrPKc2s,76079
skimage/_shared/tester.py,sha256=bA-Foz30kjF7LLpnUsOcllI5HS38f7e7hogP0uxG0vo,3589
skimage/_shared/testing.py,sha256=MLqXbxPGncfcs9YG25OfqRP3X0MqttQYihKet05zEtk,9195
skimage/_shared/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_shared/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_coord.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_dtype.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_fast_exp.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_geometry.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_interpolation.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_safe_as_int.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_testing.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_utils.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_version_requirements.cpython-39.pyc,,
skimage/_shared/tests/__pycache__/test_warnings.cpython-39.pyc,,
skimage/_shared/tests/test_coord.py,sha256=XjSxfX5yHcaLc9YwTj994kkPX823piDUC-z5LyCw9nc,3056
skimage/_shared/tests/test_dtype.py,sha256=wwqc5o2vwjTsxkjUrmYDrN-8nhM-WscaQaQe6K0aWB8,369
skimage/_shared/tests/test_fast_exp.py,sha256=w0Vmeds1ufT_bh8Y8jewLzB1X4luoOD82c_H2k3GMuk,490
skimage/_shared/tests/test_geometry.py,sha256=3CXvdgORLkvXZZm8xSNr9PLqG-prRADMWJTPWxRXDFs,2120
skimage/_shared/tests/test_interpolation.py,sha256=OxsQEISFgYSb_neW9hCMvT2R30fYIFAqK7CnLTY3WSI,1137
skimage/_shared/tests/test_safe_as_int.py,sha256=uNtuC58dXNXzcbErawF7ModKJZiVcaK8vUnrv4ZFkSs,1424
skimage/_shared/tests/test_testing.py,sha256=lbecr5FgB7xNsXdB5poBOicq2CgiOV6Lq0ZPzeJkkOM,4127
skimage/_shared/tests/test_utils.py,sha256=geCcw17iRF2viNq7G9xsydLtYzNCwvFnvd0QDMQoHR4,15661
skimage/_shared/tests/test_version_requirements.py,sha256=3h-aHn8QHOJ5zu5tVaPpB7jrWa4RUKhyJUD-Vs-w6w8,1077
skimage/_shared/tests/test_warnings.py,sha256=O-IVek3zadm58cuq_P-F59DX_6ETBHjiASAZ3sEB_8o,1250
skimage/_shared/transform.cpython-39-darwin.so,sha256=O4XvpNHlfXIjv-6ZeaMjsb0V9SWj9BH4Xrvrlh6DQMk,170171
skimage/_shared/utils.py,sha256=1d9jr5sv_FjifgJpjrpB0FaKUgWprc-hWjSVkznPnZQ,29358
skimage/_shared/version_requirements.py,sha256=ogsM5Qn6Zb6AAU_f6M4N7r3Vo2-NLUxT1RG0VSqZK2w,4347
skimage/_vendored/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_vendored/__pycache__/__init__.cpython-39.pyc,,
skimage/_vendored/__pycache__/numpy_lookfor.cpython-39.pyc,,
skimage/_vendored/numpy_lookfor.py,sha256=BLfXgwc9QoDQoeEkyjgCen4xp12Rv34iik0lFR0Ce1s,9786
skimage/color/__init__.py,sha256=OHb6Xou2v6u42swTgjRfzej4CIlRg4OmgOIQXUiRjKA,97
skimage/color/__init__.pyi,sha256=03z6CYNjJon-6DHGJF1xHPRr0ZhCcXOzkn1owqORxXY,2382
skimage/color/__pycache__/__init__.cpython-39.pyc,,
skimage/color/__pycache__/adapt_rgb.cpython-39.pyc,,
skimage/color/__pycache__/colorconv.cpython-39.pyc,,
skimage/color/__pycache__/colorlabel.cpython-39.pyc,,
skimage/color/__pycache__/delta_e.cpython-39.pyc,,
skimage/color/__pycache__/rgb_colors.cpython-39.pyc,,
skimage/color/adapt_rgb.py,sha256=eWZhGvWQ3nucRZkrDP6sgnF6Ka0rL6BB7jxeXL4pcp8,2489
skimage/color/colorconv.py,sha256=9Dg7WEYqI1A2ehEXMFNc7a6NO2FwKcXe6-84NvjEy9A,67519
skimage/color/colorlabel.py,sha256=v0VVZjMBf5_Dzq38w9txqJSzwjAQfA84ZL1_fnVE8JA,10506
skimage/color/delta_e.py,sha256=YwGF-yexqlZs6es1bFyuHEKa9wKuBeye74uLe6IF1JA,12706
skimage/color/rgb_colors.py,sha256=Nj-fsrXpuCtLFIjKMgzpvb6HkrpZiuvgDc5imTMGs9w,4493
skimage/color/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/color/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/color/tests/__pycache__/test_adapt_rgb.cpython-39.pyc,,
skimage/color/tests/__pycache__/test_colorconv.cpython-39.pyc,,
skimage/color/tests/__pycache__/test_colorlabel.cpython-39.pyc,,
skimage/color/tests/__pycache__/test_delta_e.cpython-39.pyc,,
skimage/color/tests/test_adapt_rgb.py,sha256=iXgHO2eYi3DTtou4Ddg1tquK2vVL-5DcWlJets14fyA,2761
skimage/color/tests/test_colorconv.py,sha256=ATNAUWNyTpzm_31HpXZeZwTEwTTYte2XsfZXh2oafxg,37267
skimage/color/tests/test_colorlabel.py,sha256=l1CUo4d0GNLXr-fcv4sedV9BokdNJKLwsmhYtf_JmDY,10906
skimage/color/tests/test_delta_e.py,sha256=-ggGG1FtoN1ShcWsLIaAEOTZ2eCwnOm5NS4OpA9QN0o,8087
skimage/conftest.py,sha256=KG_ignoqlB5vlbHIdtsVdobltaJRE83LhcG-9EjCKUI,310
skimage/data/README.txt,sha256=Wra5WN0KyiF-Pp3FNWCMJDKkdIKRCdEGTwPSSUYACyc,280
skimage/data/__init__.py,sha256=RvKm2gtP99COn-XdvSu4zgVp4khhUfw-0g2UJS6ca9o,384
skimage/data/__init__.pyi,sha256=varbukgcATJEhBB6ICnSPDI7SgNyIu1d8WQYZgXHJIE,1411
skimage/data/__pycache__/__init__.cpython-39.pyc,,
skimage/data/__pycache__/_binary_blobs.cpython-39.pyc,,
skimage/data/__pycache__/_fetchers.cpython-39.pyc,,
skimage/data/__pycache__/_registry.cpython-39.pyc,,
skimage/data/_binary_blobs.py,sha256=hkOjiAH4pbAqLUEeY3_d8b1mz8paqSKKncBIMHVaRz8,2193
skimage/data/_fetchers.py,sha256=D-WrrvhFc__5V1knibDysmOiDX8a68IWOJorGjnmb7Y,38815
skimage/data/_registry.py,sha256=NkDJJii6Ghn1LmBLX7uk6ZLmU3vLZVUI5O8MJQFdUDk,15901
skimage/data/astronaut.png,sha256=iEMc2WU8zVOXQbVV-wpGthVYswHUEQQStbwotePqbLU,791555
skimage/data/brick.png,sha256=eWbK8yT2uoQxGNmPegd0bSL2o0NDCt0CM-yl9uqqj88,106634
skimage/data/camera.png,sha256=sHk9Kt2g-mromcA5iUgr_5pC09VpD8fjZI8nldcwwjo,139512
skimage/data/cell.png,sha256=jSOn-4H3zId80J8zA1f8f1lWUTBuhOFyUvbgobP2FRU,74183
skimage/data/chelsea.png,sha256=WWqh58uHXrefQ34xA4HSazOKgcLaI0OXBKc8RlHoxLs,240512
skimage/data/chessboard_GRAY.png,sha256=PlGHB3RRWvTQfYIL2IJzZMcIOb-bVzx0bkhQleiT35A,418
skimage/data/chessboard_RGB.png,sha256=GsAe_y1OUPTtpVot3s3CimV2YjpY16fvhFE8XMGaAzE,1127
skimage/data/clock_motion.png,sha256=8Ckiayi2QugBE9hmIumyFe4Geglm_q9eYGBKHgVzOVU,58784
skimage/data/coffee.png,sha256=zAL4yhiLFnx3WnEBtddn0ecXks92LDPW-hWkWZtajec,466706
skimage/data/coins.png,sha256=-Ndz_Jz6b02OWULcNNCgeI_K7SpP77vtCu9TmNfvTLo,75825
skimage/data/color.png,sha256=fS35k94rT6KnjgTl34BQ9JqcURqnXlmrO9VqycmK734,85584
skimage/data/grass.png,sha256=trYCJCaziTbEOkrAljXNeK8HTpD0L_qCJ6yLdFLTn4k,217893
skimage/data/gravel.png,sha256=xIYVtFG_HmBvvXLAqp-MwPBoq3ER732Tu5sPJYZEDBI,194247
skimage/data/horse.png,sha256=x_tgeJ_jlMSF-EIpHqOyHlDRQPOdbctfuZF8wXgiVFU,16633
skimage/data/hubble_deep_field.jpg,sha256=OhnF3YqSepM0uxIpptY3EbHAx2f7J-IobnyEo-LC9fQ,527940
skimage/data/ihc.png,sha256=-N0ao4fd0fSditE7UJIbI3346bJiYG0lh3Boew75PO8,477916
skimage/data/lbpcascade_frontalface_opencv.xml,sha256=Awl3iaPcuw5A0gue-CU328O2cLan8iaNc1Rw8i4AOpE,51858
skimage/data/lfw_subset.npy,sha256=lWDsL17frAGXP2OoqZ0ABT_s0R4hh34YA4--UA-Ohyw,1000080
skimage/data/logo.png,sha256=8sV_6K8Inwi1ulI9lVc8JuYpBKxZZ_TIhRsn0DNpAWg,179723
skimage/data/microaneurysms.png,sha256=oeG-WapEf4zggvf6gJmXqzaaKxN8tsQgKrxkfHzPZFY,4950
skimage/data/moon.png,sha256=eHOWGdEffrnBZbtdLv1Hcs7lV4EuyEdTLbsdku9x9Xc,50177
skimage/data/motorcycle_disp.npz,sha256=LknIzr_z-iA1mgzGiAyC4cA7uxBtqBoXchgoG8LxE9c,1146173
skimage/data/motorcycle_left.png,sha256=2xjpxBV2F0A8NTemujVd_q_pp-q7a5uUyzP2Ul3UkXk,644701
skimage/data/motorcycle_right.png,sha256=X8kTrocOQqS2YjFLyQTReGvK2OLwubZ9uloilAY1d5c,640373
skimage/data/multipage.tif,sha256=TaCtDT30gHqYRyR9G15WW1DUZIH2Q6-1w3wUgCx4Ew8,940
skimage/data/no_time_for_that_tiny.gif,sha256=IKvpS6nkXxjeQWxfvvjR9XpJlgC-QPmiAPriRgEO784,4438
skimage/data/page.png,sha256=NBpvCmFVdmKwJzSptuVuwzqRWyxBiGuXUJ3t8qQ7R6M,47679
skimage/data/phantom.png,sha256=VS_2mBZ6pALM6xeYETBgeiKKCgqnxRkpnqpNXzAbo2w,3386
skimage/data/retina.jpg,sha256=OKB_NvJ_CV6Biup7ltNCAsBRdtMCU8ZnM_LgA3np4OY,269564
skimage/data/rocket.jpg,sha256=wt0N58U4340RHkeWGbEpRk0CadCuX9GMqR0zp_3-qVw,112525
skimage/data/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/data/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/data/tests/__pycache__/test_data.cpython-39.pyc,,
skimage/data/tests/test_data.py,sha256=vSwP7yx4lfukH7tlGVyM36cjV4NhYyCX3RE2bfE_qYM,5679
skimage/data/text.png,sha256=vYSqOm48mIeFDUXWBslrLllDP771AzhXC2PDGeZo5tE,42704
skimage/draw/__init__.py,sha256=OHb6Xou2v6u42swTgjRfzej4CIlRg4OmgOIQXUiRjKA,97
skimage/draw/__init__.pyi,sha256=dzGdH8dVkLPbpDxkcA9v7wQdAEbxHyhq1MqnWPWYLcM,963
skimage/draw/__pycache__/__init__.cpython-39.pyc,,
skimage/draw/__pycache__/_polygon2mask.cpython-39.pyc,,
skimage/draw/__pycache__/_random_shapes.cpython-39.pyc,,
skimage/draw/__pycache__/draw.cpython-39.pyc,,
skimage/draw/__pycache__/draw3d.cpython-39.pyc,,
skimage/draw/__pycache__/draw_nd.cpython-39.pyc,,
skimage/draw/_draw.cpython-39-darwin.so,sha256=np67z6w7L0cg-JIJ9JcGRXYC6yNcwVlwmCml9BUJVWk,325959
skimage/draw/_polygon2mask.py,sha256=Y1dShd48WRbxpRqvPEvURPPxwFKaR0g-RY11_epKoyU,2472
skimage/draw/_random_shapes.py,sha256=tkB2pDoNYxoW_9VP2qUtnKeplNewLSpS6_x9J1kuBdI,16010
skimage/draw/draw.py,sha256=-Nyt6p05jtPhYqz5z3Awy0HPRytLsGqqMnvHax-svkk,33672
skimage/draw/draw3d.py,sha256=OaNjPquWIHgHO5CHt6Dxk9jsB4u56C6mQHMDoDRuRKo,3292
skimage/draw/draw_nd.py,sha256=RKG1GHjsTyXN8J2nWJGVDeoqG3srqPuqmTReZsJst_4,3692
skimage/draw/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/draw/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/draw/tests/__pycache__/test_draw.cpython-39.pyc,,
skimage/draw/tests/__pycache__/test_draw3d.cpython-39.pyc,,
skimage/draw/tests/__pycache__/test_draw_nd.cpython-39.pyc,,
skimage/draw/tests/__pycache__/test_polygon2mask.cpython-39.pyc,,
skimage/draw/tests/__pycache__/test_random_shapes.cpython-39.pyc,,
skimage/draw/tests/test_draw.py,sha256=An0-U8Pt-lN2QT-j99mQvJBZrOYsjLn_-gt07IcJscI,41136
skimage/draw/tests/test_draw3d.py,sha256=bVOmrj5SIuFPicyE2N59Dw0PbjDgZAHpbtiGFam4iHY,6328
skimage/draw/tests/test_draw_nd.py,sha256=W9-C2gRov5aVmXhbKxdTep5X69bie0XpfuYu-u-KL44,485
skimage/draw/tests/test_polygon2mask.py,sha256=jvWREquUe7C1Ufjhc5yhe6qd4v15M1cLI0176lujjFs,329
skimage/draw/tests/test_random_shapes.py,sha256=PCgaS5KkUj1Gyf7JDYIiAjYjtLWNTLPyVHvbQSG3dWY,5481
skimage/exposure/__init__.py,sha256=OHb6Xou2v6u42swTgjRfzej4CIlRg4OmgOIQXUiRjKA,97
skimage/exposure/__init__.pyi,sha256=HKwoewNTdeXl1yHaCJqErXZbxT4tLpBtfjYz53PnPQk,682
skimage/exposure/__pycache__/__init__.cpython-39.pyc,,
skimage/exposure/__pycache__/_adapthist.cpython-39.pyc,,
skimage/exposure/__pycache__/exposure.cpython-39.pyc,,
skimage/exposure/__pycache__/histogram_matching.cpython-39.pyc,,
skimage/exposure/_adapthist.py,sha256=uxRzK1pMT0Gbp1dFS2W-22I8en155yqHDXwE8NodWsQ,10971
skimage/exposure/exposure.py,sha256=YJ23YDwc3b6Q5CfpMX6k3HduMI7wNIyZ-2IacrAQzgY,27785
skimage/exposure/histogram_matching.py,sha256=Q7_OwedrtvDcPO4KTRfqq-gxD6SQXqm2NfhPmUZhZA0,3194
skimage/exposure/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/exposure/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/exposure/tests/__pycache__/test_exposure.cpython-39.pyc,,
skimage/exposure/tests/__pycache__/test_histogram_matching.cpython-39.pyc,,
skimage/exposure/tests/test_exposure.py,sha256=JZwxWAhem1fHzgQ6QubKSZ8MzIhKv6PPEEYdq_M3UzU,37090
skimage/exposure/tests/test_histogram_matching.py,sha256=f1BByEh05wfR3v2VrxMcrjdLkevnmNuhmouLJPKeLsM,5157
skimage/feature/__init__.py,sha256=OHb6Xou2v6u42swTgjRfzej4CIlRg4OmgOIQXUiRjKA,97
skimage/feature/__init__.pyi,sha256=8YGpha9Ayv5Rp650WDl1jRJKR_Xfz9_ZIf9M_AdvWgs,2130
skimage/feature/__pycache__/__init__.cpython-39.pyc,,
skimage/feature/__pycache__/_basic_features.cpython-39.pyc,,
skimage/feature/__pycache__/_canny.cpython-39.pyc,,
skimage/feature/__pycache__/_daisy.cpython-39.pyc,,
skimage/feature/__pycache__/_fisher_vector.cpython-39.pyc,,
skimage/feature/__pycache__/_hog.cpython-39.pyc,,
skimage/feature/__pycache__/_orb_descriptor_positions.cpython-39.pyc,,
skimage/feature/__pycache__/blob.cpython-39.pyc,,
skimage/feature/__pycache__/brief.cpython-39.pyc,,
skimage/feature/__pycache__/censure.cpython-39.pyc,,
skimage/feature/__pycache__/corner.cpython-39.pyc,,
skimage/feature/__pycache__/haar.cpython-39.pyc,,
skimage/feature/__pycache__/match.cpython-39.pyc,,
skimage/feature/__pycache__/orb.cpython-39.pyc,,
skimage/feature/__pycache__/peak.cpython-39.pyc,,
skimage/feature/__pycache__/sift.cpython-39.pyc,,
skimage/feature/__pycache__/template.cpython-39.pyc,,
skimage/feature/__pycache__/texture.cpython-39.pyc,,
skimage/feature/__pycache__/util.cpython-39.pyc,,
skimage/feature/_basic_features.py,sha256=3WBA3AMZks0u4MeVDV1Ry1Hms_NLweiqva6RzxIiHEY,6778
skimage/feature/_canny.py,sha256=g4aAemdO553jRF04M7oSXWQDvaE9b_29eewQWbIX9GM,9466
skimage/feature/_canny_cy.cpython-39-darwin.so,sha256=1qIcLXmhrqQCMCcsvtW8Y29647scnsvIi05mj3FKEQ4,224699
skimage/feature/_cascade.cpython-39-darwin.so,sha256=eujdRiIbKxe2Fym_JJVJl2DJ4GGzln_eOrM3hlh8Qng,308224
skimage/feature/_daisy.py,sha256=BC42Hao8q2P5MHE0syRThFRfmBa5JeGNzDG1g3Ee9MA,10064
skimage/feature/_fisher_vector.py,sha256=QzY3m_kA6pJVowDSnNQzwnB1Bou0YTz8EIureYN6BL4,10511
skimage/feature/_haar.cpython-39-darwin.so,sha256=9PgLc4gVEKuwzcLETxSmMTwLw2g47VFtFYYGl3ERwM4,423175
skimage/feature/_hessian_det_appx.cpython-39-darwin.so,sha256=L__WAkgHCOLuXs-7JK8ifL0FnKGwQri8nBAjCP2cj7I,75747
skimage/feature/_hog.py,sha256=APuWjYzXNJId9FKAz3PXpn-g4pJfw2KDOO-rp99fqBI,13208
skimage/feature/_hoghistogram.cpython-39-darwin.so,sha256=ApmC0ZozQfF6-nJ8FB5PaEHDxVfm6pyBD4YV6SFPvI8,225599
skimage/feature/_orb_descriptor_positions.py,sha256=zM1l5vjzmuH9QhABX2ZkgS19VoIb_7k08OzYL467anM,450
skimage/feature/_sift.cpython-39-darwin.so,sha256=CM4BoaBmTmjKT9V4SUPaOfCuG0tvOrNTBlmrW-4vjUU,275191
skimage/feature/_texture.cpython-39-darwin.so,sha256=9-mr33k9bnGnm7LL23n1SdHul-mUoNmY406HOW6Vxjk,311098
skimage/feature/blob.py,sha256=IEkuZjPjAnrpNeBvDtpmXmky47pQqAWwiZLVsWoBfTA,27927
skimage/feature/brief.py,sha256=MsvHSdDjl1XaJ8-NGyXaBHjqM1OYvs4kVKcqq2dPz7Q,7975
skimage/feature/brief_cy.cpython-39-darwin.so,sha256=wO3PWatKXdQuFZaxzwben6b-4c0_j4xzyWDFlMKy7RE,222730
skimage/feature/censure.py,sha256=TCW-RMHVpyFf51qBhrwMjRlQ5OfJIapHkRDgA5IPgzM,12003
skimage/feature/censure_cy.cpython-39-darwin.so,sha256=9GwRgRKKNBuUIB5fzVuMvbVDy1_mRCjY22Y3GwdiD3k,189084
skimage/feature/corner.py,sha256=LPqLC5KreFSjcTI8sh10dpyM3x3FIuVSO13ImoQRwL0,45577
skimage/feature/corner_cy.cpython-39-darwin.so,sha256=DVfYLMz4BXoKhh3kuiy8rw87LQLL87DMuoPbhYnjTZY,293531
skimage/feature/haar.py,sha256=sGtUnmD8-KGGvsK-6risY-lTH-CYL4tBrbFTGFjhPRA,12921
skimage/feature/match.py,sha256=WayyFreFmSp02NEayvsjQt4HYwUoJPftHvTj9jVZBDs,4023
skimage/feature/orb.py,sha256=Bi41N8GYP6DtYlu5vjagmD0HTy_GqdEy5mdCwj2fCq8,13148
skimage/feature/orb_cy.cpython-39-darwin.so,sha256=_98Db5t70r95e74mjVHY-X5gsDrZ8WPZtZ81XU_m9jo,224888
skimage/feature/orb_descriptor_positions.txt,sha256=5lNSwCXegQMV0huO4FszyWGoPy1291cKt7Vpvddg8BE,2840
skimage/feature/peak.py,sha256=jWX5qXOhKKhsDXomiRZr-pdoRx4HHA_LQ_c-KGPT2Fs,14427
skimage/feature/sift.py,sha256=Rh7oRFE0XaA6w1emFjAxcal1Kd49AVkaXYvOi9Ktwkw,29407
skimage/feature/template.py,sha256=P8w0Wxx4voJQxMMbWGPIhled4NO9VSulT3Mf5qxEvfQ,6571
skimage/feature/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/feature/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_basic_features.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_blob.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_brief.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_canny.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_cascade.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_censure.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_corner.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_daisy.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_fisher_vector.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_haar.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_hog.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_match.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_orb.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_peak.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_sift.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_template.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_texture.cpython-39.pyc,,
skimage/feature/tests/__pycache__/test_util.cpython-39.pyc,,
skimage/feature/tests/test_basic_features.py,sha256=CpevV4VpfphjsQVIpfD8W6iPh56SejDhr-UpoDXTbO0,2092
skimage/feature/tests/test_blob.py,sha256=Eo-UNr1nJnPPZWjwzB6SSkHtAJ0MWamNTY-EeDegj2k,16199
skimage/feature/tests/test_brief.py,sha256=RBnlDa-J-XwaALha7ImGnIbVR_iraIyoG_JWeBWRqbw,3202
skimage/feature/tests/test_canny.py,sha256=oHWtVrwzEQadumXnBBdkS_zgvHDOgSOP7Q_u_zpUqvM,6388
skimage/feature/tests/test_cascade.py,sha256=uZnmMTtZUYeTRG-oJMLyKJDuS9o9ByGHmA0cPHpRdUs,514
skimage/feature/tests/test_censure.py,sha256=66Xit4dKcxnEmowgzy5ryc0Zdbii_fhUSISag_Ou5kE,3161
skimage/feature/tests/test_corner.py,sha256=rdsTEjdra7q14gOsnR1Kq7Eidy9y1Qa8dmK1qL2ptkY,24077
skimage/feature/tests/test_daisy.py,sha256=XYgJOwW-6tLaEcHAhUwo_2QV_BbG2H1yNAH2U2Dusus,3393
skimage/feature/tests/test_fisher_vector.py,sha256=pmA0mq2Q-b8KltbfD5Fpqfpnp5Ks5n0VTLRC4wpTP58,5730
skimage/feature/tests/test_haar.py,sha256=tdewpi8wvEnEpkq8aKkFODuP9_g_Nk1qKXq7nkKsEnI,6659
skimage/feature/tests/test_hog.py,sha256=ch433R-JcroWz18LMi3BAJ8-b6HxZOoUoVBI4peCXu0,11720
skimage/feature/tests/test_match.py,sha256=0iVIAbz_JZhq8PVh787zJti_E5IeaGSZ33peA_Qljx4,8065
skimage/feature/tests/test_orb.py,sha256=cJv2_hXboNZfXUoCRcXailrJozGADYQUkt4AvyDyP0M,6224
skimage/feature/tests/test_peak.py,sha256=BhSx6M4q8VNfh_RGFtjmg5R7AL_B8BQw8uxDy-rsICY,21988
skimage/feature/tests/test_sift.py,sha256=C1B2qOD3wrjlAuKoMnirXQhyPgCGpxCo3DDWwl6fQQo,5753
skimage/feature/tests/test_template.py,sha256=cSKzOc-o6X2ojyYqLe3I1LDSLcaa-EagVpfLt204VIY,6134
skimage/feature/tests/test_texture.py,sha256=6hYJlDnYtEd0bJ2gemJQfIpCF_s-Fj0bwJZFo2y_E5c,12066
skimage/feature/tests/test_util.py,sha256=0CTvY25tfEIG1cUgHfttil8OkWYTxIpkCzGNWWeWwbI,4862
skimage/feature/texture.py,sha256=2brj3zoCJWd8EFX41v9nOKy5MQaonX5JMKJJJr2nDAY,19477
skimage/feature/util.py,sha256=mnlxvFZubFQrsNwzAb_TWH_bDV3n9DdCOF-AF4g0Xec,10086
skimage/filters/__init__.py,sha256=OHb6Xou2v6u42swTgjRfzej4CIlRg4OmgOIQXUiRjKA,97
skimage/filters/__init__.pyi,sha256=KonQ6JGwzrRlNcoG5wOUmU7ujdNQkW2G5A7Ub52DGq8,2158
skimage/filters/__pycache__/__init__.cpython-39.pyc,,
skimage/filters/__pycache__/_fft_based.cpython-39.pyc,,
skimage/filters/__pycache__/_gabor.cpython-39.pyc,,
skimage/filters/__pycache__/_gaussian.cpython-39.pyc,,
skimage/filters/__pycache__/_median.cpython-39.pyc,,
skimage/filters/__pycache__/_rank_order.cpython-39.pyc,,
skimage/filters/__pycache__/_sparse.cpython-39.pyc,,
skimage/filters/__pycache__/_unsharp_mask.cpython-39.pyc,,
skimage/filters/__pycache__/_window.cpython-39.pyc,,
skimage/filters/__pycache__/edges.cpython-39.pyc,,
skimage/filters/__pycache__/lpi_filter.cpython-39.pyc,,
skimage/filters/__pycache__/ridges.cpython-39.pyc,,
skimage/filters/__pycache__/thresholding.cpython-39.pyc,,
skimage/filters/_fft_based.py,sha256=LUwb9JlvarVhLlSP5nPacsI6C5doVlJWufTh95QqOOg,6709
skimage/filters/_gabor.py,sha256=YgoW7JhDNyGCS2BCrJl8GZd2wbZbI8aNhXFC0r1GADU,7710
skimage/filters/_gaussian.py,sha256=fyHfkvJuEptkhm9USYrZMZ6dytWK39V2HqXYi7lxDvU,6037
skimage/filters/_median.py,sha256=4pp1HTIXijKWnt29j8rRTdqjXeRadYDPEGkOGQznjuQ,2964
skimage/filters/_multiotsu.cpython-39-darwin.so,sha256=n8k4DT1zDtIkLdyHxIJaZAExzPtB6wUgGyX-69Mq5SY,206220
skimage/filters/_rank_order.py,sha256=NUbi5sPtKTW-Xm6VZcwn0F5pV45yfaYDeHMEJtGy4nk,2057
skimage/filters/_sparse.py,sha256=jXiCqNqR5gUHFnhHoUVPsN16ufbU5CLEAQU_XexrI5g,4654
skimage/filters/_unsharp_mask.py,sha256=EoJ5JHrndcgrliDsef_WcQM6O3HGnswRUUW3tLBlUYs,5511
skimage/filters/_window.py,sha256=xO_yx7DSEXTBIWwPhXlsKNirmfH6Koc7k5Us_6A1wfY,4351
skimage/filters/edges.py,sha256=_z6jvZNt5_4DHc-tvhzgXv7ixQtNUvf0oZ5dmfzfV3M,25639
skimage/filters/lpi_filter.py,sha256=Z6SWW7EAM2Hj3sVOzQtUs9R_d7ZHwqIYAqNAdqpSOY8,7937
skimage/filters/rank/__init__.py,sha256=SFt54WGOb_evjktH87sKTcKk3i-4C2mqNN3c6nv46fw,1548
skimage/filters/rank/__pycache__/__init__.cpython-39.pyc,,
skimage/filters/rank/__pycache__/_percentile.cpython-39.pyc,,
skimage/filters/rank/__pycache__/bilateral.cpython-39.pyc,,
skimage/filters/rank/__pycache__/generic.cpython-39.pyc,,
skimage/filters/rank/_percentile.py,sha256=wmJYo8LeNrHgSYDMs-Lro8ErowHgGlbOjzxdcC8Dx-c,13895
skimage/filters/rank/bilateral.py,sha256=6_v233QFWBHhLCiXM9cXHoowl3d5FqVywzBCjpF--UM,7791
skimage/filters/rank/bilateral_cy.cpython-39-darwin.so,sha256=_2fUOciPyLUo-tKNx0Yhb2DGV85udePSPqq-cvS9eY0,394446
skimage/filters/rank/core_cy.cpython-39-darwin.so,sha256=cwkcKOf6UT7mXWWT20baSaNsOIcy3OxUny6_f7tmG2w,501913
skimage/filters/rank/core_cy_3d.cpython-39-darwin.so,sha256=3T84umibsdvVphlSM8Cfwfjm7fQiWWCRtg0SGZk7IYM,287788
skimage/filters/rank/generic.py,sha256=MNiZRvwOT8x8IIIIJGS15YR8-pqXabNPbUiDS9JYKxs,55909
skimage/filters/rank/generic_cy.cpython-39-darwin.so,sha256=6s3o9XBJqFJ8N0MGtJgO5KH9isQRCVFMPOvHR7EYSj8,2436044
skimage/filters/rank/percentile_cy.cpython-39-darwin.so,sha256=Tx-tiarQXb_2I-kq73AhyHGf-ehRP_baPjwB0u37sAo,751967
skimage/filters/rank/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/filters/rank/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/filters/rank/tests/__pycache__/test_rank.cpython-39.pyc,,
skimage/filters/rank/tests/test_rank.py,sha256=Juf040oJMF53n1ieo1i_-Dppvd-LV_mZkZvEd7KmdD0,38992
skimage/filters/ridges.py,sha256=vH-NFuH4M5VzbNUjW4fgs3-gOgaOyJeGhRi8lWXOC_M,14247
skimage/filters/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/filters/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_correlate.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_edges.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_fft_based.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_gabor.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_gaussian.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_lpi_filter.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_median.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_ridges.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_thresholding.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_unsharp_mask.cpython-39.pyc,,
skimage/filters/tests/__pycache__/test_window.cpython-39.pyc,,
skimage/filters/tests/test_correlate.py,sha256=kd80vN3t94fOaZLvD9kl_iql3slFelLZ1A4wzzL7T_M,1995
skimage/filters/tests/test_edges.py,sha256=txSSnwoR2-XbesvX97oYTtp4vk6_SRLkJbbvv7XIqeg,21462
skimage/filters/tests/test_fft_based.py,sha256=OhqNpO1-1n5HotznDIoBMfc79e_8ua_RqBOuJrDTLh4,14556
skimage/filters/tests/test_gabor.py,sha256=BMfvYzHi1ZCmpXsVMcs12Rfawzz2pA_AG-YyuThkReU,3689
skimage/filters/tests/test_gaussian.py,sha256=SkdpupmWj3Hx29Wd6dRWVjXb5gULbp4TwHp8SH-FVNk,5881
skimage/filters/tests/test_lpi_filter.py,sha256=joUHee4ZFTvUuGs6SZKYw_WZVEAYCeMghSb7i27R7ts,3097
skimage/filters/tests/test_median.py,sha256=T9JoGWrDMAHwAZZ-_uFvonx9Fj4Q14aAjz4GTnHLjeQ,2113
skimage/filters/tests/test_ridges.py,sha256=T8QK0BJv8W282q0H-dA8LsYXtaRszFmYDpqYMDUZoe8,9538
skimage/filters/tests/test_thresholding.py,sha256=6bA59x30RQnPM8SQRDf7AjTsUMiKUg4L1gqXqzYlvPQ,27007
skimage/filters/tests/test_unsharp_mask.py,sha256=hv6ZVRGiH93Z5f1SxsBgNG83IRRtjCavuSnd2PIAq_o,5056
skimage/filters/tests/test_window.py,sha256=6_HHYrhGAIhh4QHE4XwSAMrido4gLiwIZRc_OfYKgEE,1624
skimage/filters/thresholding.py,sha256=0NuTX4VQbA2vRDN8VqAdIN4HlTkB-IthBZ1dsSoaZf0,47905
skimage/future/__init__.py,sha256=vdpZ1o3avVJ17yAIdztXN9GwLDiR4yqZGgYfSASZ-DU,504
skimage/future/__init__.pyi,sha256=q-RbSvWEFevrbvLiFJ44XiFL6jPKvSV-NqwVQ0FZMAk,493
skimage/future/__pycache__/__init__.cpython-39.pyc,,
skimage/future/__pycache__/manual_segmentation.cpython-39.pyc,,
skimage/future/__pycache__/trainable_segmentation.cpython-39.pyc,,
skimage/future/manual_segmentation.py,sha256=bYZuVV0XKMfc6_XUYITocrbm8y4St-21rrhk4hHqNFU,7352
skimage/future/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/future/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/future/tests/__pycache__/test_trainable_segmentation.cpython-39.pyc,,
skimage/future/tests/test_trainable_segmentation.py,sha256=Wr2uvOtG4LUf1mzrRtD5HphzSkSpTDJOZhIE_d9FTqY,4223
skimage/future/trainable_segmentation.py,sha256=ziB_HovaKyLENPGUaCPABXoIxBh7wkOW0894qTWWFIQ,5604
skimage/graph/__init__.py,sha256=sdzcgqsxj3N38Cajb54zZNcK2n-safmiTB2w7CuAYE4,356
skimage/graph/__init__.pyi,sha256=-fh5gug7Wi0FFVl0BDxTla37aXRDOsNfmHtHC_r7HJk,782
skimage/graph/__pycache__/__init__.cpython-39.pyc,,
skimage/graph/__pycache__/_graph.cpython-39.pyc,,
skimage/graph/__pycache__/_graph_cut.cpython-39.pyc,,
skimage/graph/__pycache__/_graph_merge.cpython-39.pyc,,
skimage/graph/__pycache__/_ncut.cpython-39.pyc,,
skimage/graph/__pycache__/_rag.cpython-39.pyc,,
skimage/graph/__pycache__/mcp.cpython-39.pyc,,
skimage/graph/__pycache__/spath.cpython-39.pyc,,
skimage/graph/_graph.py,sha256=RBMvQVqbJ-KrFlAVgyqAKFKFtTeX5uQu5LYz4-pYbgU,8502
skimage/graph/_graph_cut.py,sha256=Hd4MjEl44l8MbNtLsRcr2fZ0VOBtyqi8ZxA_SLVqppI,9518
skimage/graph/_graph_merge.py,sha256=lrgunWZGrplz42xIDIz3IJoR4AfhzNLc_MdD5O-3tkQ,4304
skimage/graph/_mcp.cpython-39-darwin.so,sha256=iff1LbU5RwxjXFsPzNw-OM4l87zrYcX70Vb1f3MGQBw,416774
skimage/graph/_ncut.py,sha256=QpjNJZ7QjkKLwWWQbwhX-N1Bs1ejUhiEVymnRyfb4CQ,1830
skimage/graph/_ncut_cy.cpython-39-darwin.so,sha256=F9f8R9uVb26pJLnXJW0W3copuZjcusD_gaSv81IXRyM,225146
skimage/graph/_rag.py,sha256=q1Gi3ETLv_EGbY4TtIyoKI85KqMA1lKDii6BaM5Fp0o,20676
skimage/graph/_spath.cpython-39-darwin.so,sha256=SlSNlgChlQmrKjSxdl4Ra6X7n65LSPXLYN55IgH_JuQ,207272
skimage/graph/heap.cpython-39-darwin.so,sha256=O8jmY7Ap72-IsWbO0-5AwHSEoIp4xrSgqJwExp7p8c4,134470
skimage/graph/mcp.py,sha256=1gRa1f2QhCiup4WFaaCr9YcnNHZVc8H1DoIA0oS-vVM,3182
skimage/graph/spath.py,sha256=uav6ZRcaw3O93NyLiq-EVQouZUtxK16VIYUYoWGuJNM,3399
skimage/graph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/graph/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/graph/tests/__pycache__/test_anisotropy.cpython-39.pyc,,
skimage/graph/tests/__pycache__/test_connect.cpython-39.pyc,,
skimage/graph/tests/__pycache__/test_flexible.cpython-39.pyc,,
skimage/graph/tests/__pycache__/test_heap.cpython-39.pyc,,
skimage/graph/tests/__pycache__/test_mcp.cpython-39.pyc,,
skimage/graph/tests/__pycache__/test_pixel_graph.cpython-39.pyc,,
skimage/graph/tests/__pycache__/test_rag.cpython-39.pyc,,
skimage/graph/tests/__pycache__/test_spath.cpython-39.pyc,,
skimage/graph/tests/test_anisotropy.py,sha256=_PGkxQt2is3f1bYmAzWPZJ-KKmAh-_9OYXkQ89-ceTY,3518
skimage/graph/tests/test_connect.py,sha256=XU3UMqDRacTy-dMulRNwEFBixyvYmxbK-RtfhFc8HzY,2367
skimage/graph/tests/test_flexible.py,sha256=TgkxK1QpeHAZu6BoULWoIY0ir83_vG9VZFplLjhYz5s,1560
skimage/graph/tests/test_heap.py,sha256=PHdBGa90S3k0VuEAZhUOi7-zSoDSeduQdtQUCaD8GIY,1105
skimage/graph/tests/test_mcp.py,sha256=hB5fPpB73Rj2c9YPtukyA0Hxeg424yuIOMkmVfeY3BM,5402
skimage/graph/tests/test_pixel_graph.py,sha256=OKvoYxMKMgxPS8hRsx6SgsC1mL8kYmM7tMSe69NoFQE,2765
skimage/graph/tests/test_rag.py,sha256=VEdfa47xlwForkv0q1vfFiW-4nfGB9wjR46wVz9sSdc,7857
skimage/graph/tests/test_spath.py,sha256=keQxRt7EyZyEMO41d1UcAdT5ZVHvnp5HJ83l9RQYXVg,826
skimage/io/__init__.py,sha256=COa2CzPzL02FsiMwAdtZjhcLFwD7oTGKdP2cfJS3PTQ,1773
skimage/io/__pycache__/__init__.cpython-39.pyc,,
skimage/io/__pycache__/_image_stack.cpython-39.pyc,,
skimage/io/__pycache__/_io.cpython-39.pyc,,
skimage/io/__pycache__/collection.cpython-39.pyc,,
skimage/io/__pycache__/manage_plugins.cpython-39.pyc,,
skimage/io/__pycache__/sift.cpython-39.pyc,,
skimage/io/__pycache__/util.cpython-39.pyc,,
skimage/io/_image_stack.py,sha256=A75v07PK6yd-5Qi60JfoaKG2esjJI5bMtlou8dNjUSc,570
skimage/io/_io.py,sha256=bWRVcnbVV6KXP2fbQ0qJsVlBJn3sTdq7hySiFvNGq-s,6586
skimage/io/_plugins/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/io/_plugins/__pycache__/__init__.cpython-39.pyc,,
skimage/io/_plugins/__pycache__/fits_plugin.cpython-39.pyc,,
skimage/io/_plugins/__pycache__/gdal_plugin.cpython-39.pyc,,
skimage/io/_plugins/__pycache__/imageio_plugin.cpython-39.pyc,,
skimage/io/_plugins/__pycache__/imread_plugin.cpython-39.pyc,,
skimage/io/_plugins/__pycache__/matplotlib_plugin.cpython-39.pyc,,
skimage/io/_plugins/__pycache__/pil_plugin.cpython-39.pyc,,
skimage/io/_plugins/__pycache__/simpleitk_plugin.cpython-39.pyc,,
skimage/io/_plugins/__pycache__/tifffile_plugin.cpython-39.pyc,,
skimage/io/_plugins/_colormixer.cpython-39-darwin.so,sha256=EmFJqbmlsamlxZy8KviZCEX9F4byo__p4xwv-6NZiF4,128029
skimage/io/_plugins/_histograms.cpython-39-darwin.so,sha256=PmTluuNOI88Wy3AwYOMEnMH7yQNzUuq_dNpKuRQZQBY,93661
skimage/io/_plugins/fits_plugin.ini,sha256=60y6Ey9etFQTf3oRxr4hAgptiBxkmNG_PpJaP5LdhR8,88
skimage/io/_plugins/fits_plugin.py,sha256=hE0QRLBX6AcTtodB9VVopw5O7U62ektN251opWSu6MQ,4407
skimage/io/_plugins/gdal_plugin.ini,sha256=Yx6iK4NqTllzpc726aXZXOlktuhm8IR28i4hfB7Qkn8,89
skimage/io/_plugins/gdal_plugin.py,sha256=oNQjajA4GUxb3swAQMf19X_XJhyY3pRdaFde145K4J4,349
skimage/io/_plugins/imageio_plugin.ini,sha256=Gi4D65yDOFzg7gRCwXYGxlJoHsvlFSEwyt1wJkedqWc,88
skimage/io/_plugins/imageio_plugin.py,sha256=d9MQzuDOWadJlfDhNptaNQJEI8sPsXWgzmwJuDxldLk,330
skimage/io/_plugins/imread_plugin.ini,sha256=yNvmnjjif85MJpwIfSheQ0IMQ86rPZuuTDjuaZZH6Pk,86
skimage/io/_plugins/imread_plugin.py,sha256=dM_Z_GsqUIbZQatLUPKJEiOC6nYQP7k3nscDyTLjJvs,956
skimage/io/_plugins/matplotlib_plugin.ini,sha256=re_KwCXXwedUEqJ749d7q6YqPDde5_RxH72M7y3PYY0,123
skimage/io/_plugins/matplotlib_plugin.py,sha256=QOIyQmgZGQ7NBZf4mZ5SWHC8P86y64bmAROy_4DYhKk,6467
skimage/io/_plugins/pil_plugin.ini,sha256=ZSFIMpRtr2-tUPvK_cG0xLOrPSWgw1ObRWVe8TMIJF0,91
skimage/io/_plugins/pil_plugin.py,sha256=28qO6oO8ZUbac8wFnMw3E1Lv6KMUiNqf5lwsj9XgDU0,7843
skimage/io/_plugins/simpleitk_plugin.ini,sha256=Zu1mh1RaPIu6vw2cSPev_T2or0DTMYZeLxyc1u7Hcig,92
skimage/io/_plugins/simpleitk_plugin.py,sha256=N2Ja-_0TpfrixNl0iEWJydrIrHgOYlzuxHkF10KrYOE,531
skimage/io/_plugins/tifffile_plugin.ini,sha256=vQpbXXPSQN242crgASKbsxqn1aDUfHwftslt9vvuSkQ,110
skimage/io/_plugins/tifffile_plugin.py,sha256=mAT73YEKXXefD2DtMqZyUioqljO4FmKRi7D5b-gHeps,2071
skimage/io/collection.py,sha256=GoXoj5LNTEIiFVA44ZIVmOug4M2eS_p2yF3q_RfoDNg,15956
skimage/io/manage_plugins.py,sha256=NKJJApdfEMwRLMgAvAHuNIQ9FcKye9PprLvW7zAdcfs,10351
skimage/io/sift.py,sha256=mfUV7SJhTqmxpwHXwyPMYhQT_1d6y5JloNfHgH40qnc,2561
skimage/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/io/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_collection.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_fits.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_histograms.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_imageio.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_imread.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_io.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_mpl_imshow.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_multi_image.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_pil.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_plugin.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_sift.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_simpleitk.cpython-39.pyc,,
skimage/io/tests/__pycache__/test_tifffile.cpython-39.pyc,,
skimage/io/tests/test_collection.py,sha256=oyDQBMq7uD4Dt6PYkr8q_GGskWh8I_vy3vRlorJrWSc,5357
skimage/io/tests/test_fits.py,sha256=sn5VaWkM1dr94F-WqbfIUooAW0wet7wCa43w5PZ4s1Q,884
skimage/io/tests/test_histograms.py,sha256=31hxBxy80xXaeNTvESI96OqVz3tC796VFI5-GEqC8Ss,742
skimage/io/tests/test_imageio.py,sha256=e2IgcZL3PWKZK3A-Guj849PjVUhC4sPz9V_hKDWD27Y,2724
skimage/io/tests/test_imread.py,sha256=id3_fw7zwgWODWpHvzrCoidMfzyTtPB_8JcTLbljUfk,1946
skimage/io/tests/test_io.py,sha256=GGoZ4gdqEEFzc8BPRBsLOFKekorVm1CBbDFuc81Fh1w,3974
skimage/io/tests/test_mpl_imshow.py,sha256=Oatd35OO2Js0OQexOmtZXzNtC72f5xXzVhrweeuTFlc,4530
skimage/io/tests/test_multi_image.py,sha256=3jx3EAfU4_nSw4grgq-TCBeQ_CaGa74iQJHk6d3RytY,2527
skimage/io/tests/test_pil.py,sha256=kGKIOkqGWyxk9UVvQ6G3B4oonG2zSTFO-UF3CqPmvIQ,9049
skimage/io/tests/test_plugin.py,sha256=ksfRZZF2jNGdcDJcFOdCM0zzY18vJ8JoJhuvCpaV1Xc,2310
skimage/io/tests/test_sift.py,sha256=RE4W1QRzqBKaLSdhElhDO806EvcDcFwidR8RxYH2mic,3336
skimage/io/tests/test_simpleitk.py,sha256=Yu7HnQT-MATMnwEhp1IaIGobG81jnhAebSoYrP59cDA,2690
skimage/io/tests/test_tifffile.py,sha256=IF8wH16H9iqSFL55hforf9FNWtkbnqYGn72hWmqJbCY,2665
skimage/io/util.py,sha256=7gQdn6BD2HX-R5yAI6A8GCjG3HMWzUFjzjPJD1Qa3HE,1283
skimage/measure/__init__.py,sha256=OHb6Xou2v6u42swTgjRfzej4CIlRg4OmgOIQXUiRjKA,97
skimage/measure/__init__.pyi,sha256=Vg5SQxdQywJkRnvqf8jsO_5Lw3MYvQ_n_Lp0bJr38R8,1842
skimage/measure/__pycache__/__init__.cpython-39.pyc,,
skimage/measure/__pycache__/_blur_effect.cpython-39.pyc,,
skimage/measure/__pycache__/_colocalization.cpython-39.pyc,,
skimage/measure/__pycache__/_find_contours.cpython-39.pyc,,
skimage/measure/__pycache__/_label.cpython-39.pyc,,
skimage/measure/__pycache__/_marching_cubes_lewiner.cpython-39.pyc,,
skimage/measure/__pycache__/_marching_cubes_lewiner_luts.cpython-39.pyc,,
skimage/measure/__pycache__/_moments.cpython-39.pyc,,
skimage/measure/__pycache__/_moments_analytical.cpython-39.pyc,,
skimage/measure/__pycache__/_polygon.cpython-39.pyc,,
skimage/measure/__pycache__/_regionprops.cpython-39.pyc,,
skimage/measure/__pycache__/_regionprops_utils.cpython-39.pyc,,
skimage/measure/__pycache__/block.cpython-39.pyc,,
skimage/measure/__pycache__/entropy.cpython-39.pyc,,
skimage/measure/__pycache__/fit.cpython-39.pyc,,
skimage/measure/__pycache__/pnpoly.cpython-39.pyc,,
skimage/measure/__pycache__/profile.cpython-39.pyc,,
skimage/measure/_blur_effect.py,sha256=nnvp3SMsgpy8J2E5hMWjZ2BC_w6AWsDGs4UrukIOa4s,3145
skimage/measure/_ccomp.cpython-39-darwin.so,sha256=t7jPusVJXZC57EsANyiLR-j8nxGh-q4efPJdhnRc1yw,130616
skimage/measure/_colocalization.py,sha256=IrpqSQlg8vLOoOVkPtxrlOD2bfGkIL_53XyVa5nIuL8,12249
skimage/measure/_find_contours.py,sha256=9XR2aSiN9vl0WpaA0s19HzP0cbiKxd1zvg7Qaht-I6M,9585
skimage/measure/_find_contours_cy.cpython-39-darwin.so,sha256=HnqGhf_m_UNrkqA8S-kwNR2BwXC3gI-Ft-cWIrvaPeM,189283
skimage/measure/_label.py,sha256=WoRzje_lZLblB0BSpGsb4rHifWg9ZO9u19Fudq_HmPU,3960
skimage/measure/_marching_cubes_lewiner.py,sha256=qX3b_vAdibqb34AkLqL1zK8sW9mwf5fJ3E3aRENNMMk,12856
skimage/measure/_marching_cubes_lewiner_cy.cpython-39-darwin.so,sha256=hY0bfs8tpDVREGABjbulcdDS4tdQqMuan7Vr3Jn7NaE,297964
skimage/measure/_marching_cubes_lewiner_luts.py,sha256=puYUThcl9gATqqmerwTqAdpqooOeMoR0oV6J_gvP5wE,27802
skimage/measure/_moments.py,sha256=85KWsIKWuYDMifNeEADXsHvZt8khf13O2GpkbLwHcl8,17848
skimage/measure/_moments_analytical.py,sha256=1OzL8mhKfqxfKT6Q4t4WwpFyM_1cgKlquMM7jr5LJ-Q,6563
skimage/measure/_moments_cy.cpython-39-darwin.so,sha256=XPV9ewTj8CZLUeFszgkyipsY5lUkZRno0Fij83-STjE,224541
skimage/measure/_pnpoly.cpython-39-darwin.so,sha256=8xKHPYRI8vddZLosgh5ORoEEX2kn5cwNvzwlWwjGx-k,206249
skimage/measure/_polygon.py,sha256=JqJ9iVRdFY9rVChXKXmVY5slHtOvBCjIyGpWyEkoqr4,5268
skimage/measure/_regionprops.py,sha256=gvJ7rvPw-P-WvOxEVFnzXQbemVLuBOsdTOgAQ_K0Ngo,51160
skimage/measure/_regionprops_utils.py,sha256=CC4bxkhCTIZH7c7QBWLEu4HafFZBo_MsBn17L2eMOyQ,15989
skimage/measure/block.py,sha256=b4dhXeeaiBerPmFpNjtm52KvwRmIByPI1U-Y0S5gws4,3261
skimage/measure/entropy.py,sha256=aNypYauao_uvywjL1DewtbCDB01jrwX6BnUIAo_CbhM,1144
skimage/measure/fit.py,sha256=EXKboAtTnjkmg2ABJME9vZdiGH4ZdGB3NnFBCXnlYpI,31957
skimage/measure/pnpoly.py,sha256=DGgYjnvOANCvZOb_oOgzHTiDi62vWD03AOFQhuctJXo,2023
skimage/measure/profile.py,sha256=-r9sI9iSsu57zaFKtGc0rE5-x1W5ZgP_XleR_bAS0d0,6781
skimage/measure/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/measure/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_block.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_blur_effect.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_ccomp.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_colocalization.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_entropy.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_find_contours.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_fit.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_label.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_marching_cubes.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_moments.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_pnpoly.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_polygon.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_profile.cpython-39.pyc,,
skimage/measure/tests/__pycache__/test_regionprops.cpython-39.pyc,,
skimage/measure/tests/test_block.py,sha256=lOMhCz-a-zEPLJvK6fGiGVrBFGvFGKucb3fuf9W2yN0,4022
skimage/measure/tests/test_blur_effect.py,sha256=iFmOb1Tbh3O36Yw-Bz7kihyyN3ncfJCW3a33Mppw5lI,1737
skimage/measure/tests/test_ccomp.py,sha256=pHQ8jGMj7WlShxyaunDb3kwd2DopZxKq-MmL_2mmCoA,8033
skimage/measure/tests/test_colocalization.py,sha256=Hr1D1pu1jU7PuBb_Lp-Wwp37athNjIUHzycnPBJe74E,4672
skimage/measure/tests/test_entropy.py,sha256=mMUvbULp-qBSbEJ73k25xqG-D8dLymExO4I5WGrJsJI,400
skimage/measure/tests/test_find_contours.py,sha256=gexiCwV1eWsQy4boYOe3pUHGlJEbD0rRalHtrMBUZSc,5276
skimage/measure/tests/test_fit.py,sha256=5otSVEKE3eBySqk4702jUMnFVML1ey5QZLeRQCS52mU,21156
skimage/measure/tests/test_label.py,sha256=6tTyVeNbgjhxxnwnyN0VhXKLuFTk21QWCvg1C-5Aa34,1783
skimage/measure/tests/test_marching_cubes.py,sha256=A4M4I8bnXm5bxVi6ChEvckje0AiN8m0CTWcGB15_fpg,7055
skimage/measure/tests/test_moments.py,sha256=kDxKemYhwnYajGEFa4EP2VALEOdUri9Z8lcvOR1RWQU,11768
skimage/measure/tests/test_pnpoly.py,sha256=fLKSNA2r7ABNqS6vpY_3D8AVJb0wAEnkWhAhLy_XMrI,1239
skimage/measure/tests/test_polygon.py,sha256=jor2cmpYG4XXfEu-jlURTmPUEho5YRF8i_NDJNid57s,2295
skimage/measure/tests/test_profile.py,sha256=OHBxbElqoVXz4HoeSrKXapDgLpoIoHriSt0KHVI-NIY,7822
skimage/measure/tests/test_regionprops.py,sha256=hjzsKS5zkF8-ygOl_GkS8dcljS7Y2N791DsX4R7yaow,52595
skimage/metrics/__init__.py,sha256=OHb6Xou2v6u42swTgjRfzej4CIlRg4OmgOIQXUiRjKA,97
skimage/metrics/__init__.pyi,sha256=1t3MLs46xdqirO2XUiVV__auPvFJY0rAM-S1PDVoHKQ,886
skimage/metrics/__pycache__/__init__.cpython-39.pyc,,
skimage/metrics/__pycache__/_adapted_rand_error.cpython-39.pyc,,
skimage/metrics/__pycache__/_contingency_table.cpython-39.pyc,,
skimage/metrics/__pycache__/_structural_similarity.cpython-39.pyc,,
skimage/metrics/__pycache__/_variation_of_information.cpython-39.pyc,,
skimage/metrics/__pycache__/set_metrics.cpython-39.pyc,,
skimage/metrics/__pycache__/simple_metrics.cpython-39.pyc,,
skimage/metrics/_adapted_rand_error.py,sha256=tlbnsI54GjSmTZUMx-patcqZGsx8FhJodxv9DeCDwr0,3505
skimage/metrics/_contingency_table.py,sha256=fOXnz3p0_bD8Mfu64i2ekZ1V1csdv7RqOE44tI5M3Is,1224
skimage/metrics/_structural_similarity.py,sha256=l4HTTumNP0vDABKt06R0Dq-7ZpUtupxqWz64e9C0sKA,10422
skimage/metrics/_variation_of_information.py,sha256=2SQssDv2jTl3ASPqMuhQKZ_klc-Vt4gwmv4yWD4B-Jw,4193
skimage/metrics/set_metrics.py,sha256=VNcOP2w9oNrrOS0qjPkVuI8IKTG1yYliMWGlzqbVtEk,4896
skimage/metrics/simple_metrics.py,sha256=RTpf2sSCOpLG6q7AtYMIdPHkYqchhqdEGXh4dB8Yt7Q,8247
skimage/metrics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/metrics/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/metrics/tests/__pycache__/test_segmentation_metrics.cpython-39.pyc,,
skimage/metrics/tests/__pycache__/test_set_metrics.cpython-39.pyc,,
skimage/metrics/tests/__pycache__/test_simple_metrics.cpython-39.pyc,,
skimage/metrics/tests/__pycache__/test_structural_similarity.cpython-39.pyc,,
skimage/metrics/tests/test_segmentation_metrics.py,sha256=DIot12zLqOxBij76LbXaDe78dzCmQwoGEQwueaxNsng,1842
skimage/metrics/tests/test_set_metrics.py,sha256=o42FKIdel_BcnmBUm1cWZ1nRj1u8Ik3EmJ1p8TEcg5g,6930
skimage/metrics/tests/test_simple_metrics.py,sha256=zIWUFxH8fjRCdhZ4PUpmgIERy5V_a4_uY_BoGmQXe_0,4833
skimage/metrics/tests/test_structural_similarity.py,sha256=9nnhLMskw3zWS3XjQzNNTX_3AD_0IJRkfkWglXCvQCc,9749
skimage/morphology/__init__.py,sha256=Kc31N-1xEj35YHcV44todkoKBqwylJ_WJ_S3XkllG2Y,2288
skimage/morphology/__pycache__/__init__.cpython-39.pyc,,
skimage/morphology/__pycache__/_flood_fill.cpython-39.pyc,,
skimage/morphology/__pycache__/_skeletonize.cpython-39.pyc,,
skimage/morphology/__pycache__/_util.cpython-39.pyc,,
skimage/morphology/__pycache__/binary.cpython-39.pyc,,
skimage/morphology/__pycache__/convex_hull.cpython-39.pyc,,
skimage/morphology/__pycache__/extrema.cpython-39.pyc,,
skimage/morphology/__pycache__/footprints.cpython-39.pyc,,
skimage/morphology/__pycache__/gray.cpython-39.pyc,,
skimage/morphology/__pycache__/grayreconstruct.cpython-39.pyc,,
skimage/morphology/__pycache__/isotropic.cpython-39.pyc,,
skimage/morphology/__pycache__/max_tree.cpython-39.pyc,,
skimage/morphology/__pycache__/misc.cpython-39.pyc,,
skimage/morphology/_convex_hull.cpython-39-darwin.so,sha256=B1EItnAXopENggOexAkLIrjTNpYKsdeJVd_EbzlnmWc,189230
skimage/morphology/_extrema_cy.cpython-39-darwin.so,sha256=_GNGpmeVwx7fkhoUfUVBpuZKHp_hAPhxOMX3TW6c0e4,258621
skimage/morphology/_flood_fill.py,sha256=349jaVevFjmsQHM6ZRRR3Z-Oi0-gelWj6WP5MILXqwk,10733
skimage/morphology/_flood_fill_cy.cpython-39-darwin.so,sha256=cVlTA92tweJFQ37QkQS4E0GBPGBqWn6qvXETvqhcVX8,310304
skimage/morphology/_grayreconstruct.cpython-39-darwin.so,sha256=oNkiiTwp5yBsZnLiAAuWMBTt-KVsDG6WWxothYtKp6c,225058
skimage/morphology/_max_tree.cpython-39-darwin.so,sha256=NJk_8cuaKnIY_6nCpuOVS_IalEV6kONuEPaaLcatvBo,661403
skimage/morphology/_misc_cy.cpython-39-darwin.so,sha256=CH1krr-pPqnZhQNwkdbRS89VaqVMf7vzg2KKJybm9I4,258810
skimage/morphology/_skeletonize.py,sha256=EDwl6WWB7wbGgXeqm2tIbefaKupMlx0ttGCmmDXo3dU,23548
skimage/morphology/_skeletonize_3d_cy.cpython-39-darwin.so,sha256=Cf9hBQ2dmBAk61I4LGu046GlnQYGLIlvdEtXbEkRlgc,213332
skimage/morphology/_skeletonize_cy.cpython-39-darwin.so,sha256=ZF2tkshqWR5w6OBk0N2nfWGcFOdsKtwyhnecoe3E9ag,207265
skimage/morphology/_util.py,sha256=TBba9j3tF3srL-q54cNYTRaYy3_8rPuD73v1r1i63vc,12019
skimage/morphology/ball_decompositions.npy,sha256=T561HzYf19fSLTQt7Hu5gXiiGqnJRFB_JYmLjKIT5U0,431
skimage/morphology/binary.py,sha256=4Py-51eyBa64w1c_WInfWRNyZiknHQKmtC-y--EAo8I,12112
skimage/morphology/convex_hull.py,sha256=5jLe9JbKXW9OBIuklxqYEm_ns-2usVuEx0wTZVmRnJA,8419
skimage/morphology/disk_decompositions.npy,sha256=dpc557PYxwYZk2gs30WgwEiUCVL7eOMbFy2DD3545-4,881
skimage/morphology/extrema.py,sha256=7LGyAgrN3MTtdqkd_x3Ne1FTNKpnbhGA6kLsDCZeN2c,20797
skimage/morphology/footprints.py,sha256=Vy3Ls-Ho7h1W8BXphGQcvdlCMXZaBCjyE84HgJ-stiE,40077
skimage/morphology/gray.py,sha256=GnY3aO3kYGMFONhEYymshJduq-IfJi4sIgxUUKCd7vE,25742
skimage/morphology/grayreconstruct.py,sha256=-HwSfTlqe29Yp-IQmxhNQdfbJtgCHMoixj3fynFXEU0,9350
skimage/morphology/isotropic.py,sha256=XRVAX2ha5tw_XLQWd5HiQUGtcZ8MvChzaQPYDzXcinQ,7879
skimage/morphology/max_tree.py,sha256=7ZhC3CU-L4_GTq4yd_mwLPWC1xw7-uT_lGi5wS86hnQ,26983
skimage/morphology/misc.py,sha256=tgS7qYTLVsx1dpsLIADf5Mo0l1FJMHqdqEc5noXbkPQ,16678
skimage/morphology/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/morphology/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_binary.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_convex_hull.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_extrema.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_flood_fill.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_footprints.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_gray.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_isotropic.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_max_tree.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_misc.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_reconstruction.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_skeletonize.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_skeletonize_3d.cpython-39.pyc,,
skimage/morphology/tests/__pycache__/test_util.cpython-39.pyc,,
skimage/morphology/tests/test_binary.py,sha256=_C-wp0gBOfLyONo5Dp2GKyIo0naZJAztCi2O46mbf8w,12489
skimage/morphology/tests/test_convex_hull.py,sha256=VJTgBb1BgQucfdm79vVwxasw_1K6ez4iDNgOADB80W8,8860
skimage/morphology/tests/test_extrema.py,sha256=0vgnohjH1igY7ncBZxnXKZhBuDi06Km56TmOtZfPrjE,26122
skimage/morphology/tests/test_flood_fill.py,sha256=YQrhCQWlDE9nnu_G280p7t3bsUvOLD2UEmKx9uuoLhQ,9418
skimage/morphology/tests/test_footprints.py,sha256=zqCt0cz3I_0FkXLY4L0mCVJWteO1tNdqlCwCzQ8qsrc,10985
skimage/morphology/tests/test_gray.py,sha256=8EI5-XFEhourCIbGuOr2aG8HWWnSnMYPpCOGzYTaZKs,16898
skimage/morphology/tests/test_isotropic.py,sha256=U-04r2oLsEJ-mh1nlSpIlYAxD0Cg4BqWZ9xS-KSZ9N8,2845
skimage/morphology/tests/test_max_tree.py,sha256=72PZorjk-37dJ_vm7jxD92pH1EnU-82IVCJOlIyjsME,20611
skimage/morphology/tests/test_misc.py,sha256=AxN4D5tZFxERyqhTxz8KpKAIhBfm_eay0iRwozgJ9pM,18009
skimage/morphology/tests/test_reconstruction.py,sha256=FVzLHWnKvsxWE73lLEmnwQIIUNSkfuQ-NXXoaeLtN3c,5868
skimage/morphology/tests/test_skeletonize.py,sha256=yD3sgncyLJtg0qI700DeD8OO76k53j5jTffxbsVO3zg,9185
skimage/morphology/tests/test_skeletonize_3d.py,sha256=oXwYNXp-2a7c1SXKrjMJEwIJughP3ZeBWeupqfDOQXg,6390
skimage/morphology/tests/test_util.py,sha256=Vi6MJVYtIaWtFdDrxeK1e-hXv7Ff8WKeAfmzeK5HHTs,5660
skimage/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/registration/__init__.py,sha256=OHb6Xou2v6u42swTgjRfzej4CIlRg4OmgOIQXUiRjKA,97
skimage/registration/__init__.pyi,sha256=FDJiD5iCOBzWws3vx3ON2PhpoujSvUhtcCjdArGrM74,366
skimage/registration/__pycache__/__init__.cpython-39.pyc,,
skimage/registration/__pycache__/_masked_phase_cross_correlation.cpython-39.pyc,,
skimage/registration/__pycache__/_optical_flow.cpython-39.pyc,,
skimage/registration/__pycache__/_optical_flow_utils.cpython-39.pyc,,
skimage/registration/__pycache__/_phase_cross_correlation.cpython-39.pyc,,
skimage/registration/_masked_phase_cross_correlation.py,sha256=BigH4m9EEQL0p-ZrZ9YTirNuJ9u0jbYi5YmOZKGH_30,12412
skimage/registration/_optical_flow.py,sha256=IW_a2bYQLWe5INYhD4KL4bgscxyCUpL5KMZdo4ShT1k,14549
skimage/registration/_optical_flow_utils.py,sha256=MmCRpKMU2CmbZ9QR_t9EaM6F6Ehlx8U_Ndm9VwGkE-0,3682
skimage/registration/_phase_cross_correlation.py,sha256=Lg0gsrIujU2fIytSnHm64P55XlYWpVuXgklOVPKWQME,17867
skimage/registration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/registration/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/registration/tests/__pycache__/test_ilk.cpython-39.pyc,,
skimage/registration/tests/__pycache__/test_masked_phase_cross_correlation.cpython-39.pyc,,
skimage/registration/tests/__pycache__/test_phase_cross_correlation.cpython-39.pyc,,
skimage/registration/tests/__pycache__/test_tvl1.cpython-39.pyc,,
skimage/registration/tests/test_ilk.py,sha256=xeAUy-zHmcyXulomUGujIoB8NZ8NdM6zzfGliYcIM_M,3096
skimage/registration/tests/test_masked_phase_cross_correlation.py,sha256=-QQxqNJhL3P9OqlO6Msk_vsCezXIlirMlA_TWw3ltLc,9824
skimage/registration/tests/test_phase_cross_correlation.py,sha256=CB6_eqOa_j_GuZBZPAlHACD3kTyGBprlVZm6WpLIHJ4,8377
skimage/registration/tests/test_tvl1.py,sha256=gIIgiMW7mGkoiWr9LjEz-c3n5F7h2IW64aiqMjUKdhI,3564
skimage/restoration/__init__.py,sha256=X_856Sg1wkzduhAQAkGLkIY99edaeatM1xUyePSr4ho,132
skimage/restoration/__init__.pyi,sha256=mw9Cug3REvQ1R0NveVQXbSkT-JRo6uqSp0gx4eyEG8Y,1067
skimage/restoration/__pycache__/__init__.cpython-39.pyc,,
skimage/restoration/__pycache__/_cycle_spin.cpython-39.pyc,,
skimage/restoration/__pycache__/_denoise.cpython-39.pyc,,
skimage/restoration/__pycache__/_rolling_ball.cpython-39.pyc,,
skimage/restoration/__pycache__/deconvolution.cpython-39.pyc,,
skimage/restoration/__pycache__/inpaint.cpython-39.pyc,,
skimage/restoration/__pycache__/j_invariant.cpython-39.pyc,,
skimage/restoration/__pycache__/non_local_means.cpython-39.pyc,,
skimage/restoration/__pycache__/uft.cpython-39.pyc,,
skimage/restoration/__pycache__/unwrap.cpython-39.pyc,,
skimage/restoration/_cycle_spin.py,sha256=r1nsPl64vDqdKMhH5wnzVaWVJL0S5XfN7PEmUclteZM,5856
skimage/restoration/_denoise.py,sha256=_vOvvb4SZyll-WcGUDNyLMSLkbPLQIiWBns7W8i6J2s,41403
skimage/restoration/_denoise_cy.cpython-39-darwin.so,sha256=UGGEsgbUoV0ddX-6dY-t7KWkFs1YMcU3Dvdi9s2Uruk,276925
skimage/restoration/_inpaint.cpython-39-darwin.so,sha256=gEV4PoDKFWkmT8Xwf1Esk40bYF5MHx500ed6RuidI7s,224186
skimage/restoration/_nl_means_denoising.cpython-39-darwin.so,sha256=dtD0LjV06AOo8xOxEcAJ0WkcT7YWYam5j5DSlgk7vaw,495269
skimage/restoration/_rolling_ball.py,sha256=ST9nOWarTNaroHjz4LcEIRMwG_Ue5cKJ_LV0yfbrlL4,6932
skimage/restoration/_rolling_ball_cy.cpython-39-darwin.so,sha256=cEBX-SIcGXo6P_jw7bNXhRvlkJAzTegd4QZrkjPzdAc,293872
skimage/restoration/_unwrap_1d.cpython-39-darwin.so,sha256=RAh5kx3HbSq94ZP5WJnMbrxjbo3PCdTEqbqwi1p_0mA,188828
skimage/restoration/_unwrap_2d.cpython-39-darwin.so,sha256=v1_x-i1IvfuzFyqsDfSlx-PBbTdrpNgdDm7eGhPDimk,189532
skimage/restoration/_unwrap_3d.cpython-39-darwin.so,sha256=dn7oIPcsftImfw-jZkZ2k2wiyQNbj6WmKAdvudVzxVU,206108
skimage/restoration/deconvolution.py,sha256=Qfj3Znd7_0pyotEntKwpMxXlet30MMsFbcv_ARe5Bfc,15841
skimage/restoration/inpaint.py,sha256=Zrzgqvl_mx1xQN5MlaTNOIdmoJ2V0IitgUjjIiH8Mog,12666
skimage/restoration/j_invariant.py,sha256=7e_m9oFc2gFlY_9eM5vRJ_6KLMzrqFmkdodC-0JOmC4,12402
skimage/restoration/non_local_means.py,sha256=ftcQMALybovCQn-aJCMCsdmUtZh0BYx5dT9wAVIZFLI,7718
skimage/restoration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/restoration/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/restoration/tests/__pycache__/test_denoise.cpython-39.pyc,,
skimage/restoration/tests/__pycache__/test_inpaint.cpython-39.pyc,,
skimage/restoration/tests/__pycache__/test_j_invariant.cpython-39.pyc,,
skimage/restoration/tests/__pycache__/test_restoration.cpython-39.pyc,,
skimage/restoration/tests/__pycache__/test_rolling_ball.cpython-39.pyc,,
skimage/restoration/tests/__pycache__/test_unwrap.cpython-39.pyc,,
skimage/restoration/tests/test_denoise.py,sha256=l97RnNfxN9xfe0ymygYpcnJ1zpFPeoxeVPweKf1rmT4,40375
skimage/restoration/tests/test_inpaint.py,sha256=TdZRykL8xCuu5d1kRT7gtXCT8N-zeswu4mn35jNY7GU,6853
skimage/restoration/tests/test_j_invariant.py,sha256=rRlL8t4SIHsczzyk1VB9GFlL_LFfo8HyuJnKckVqyTM,3273
skimage/restoration/tests/test_restoration.py,sha256=CMd-epKF5WkD4fVg9vevnrhU__OHZBeqkqd-d8F_rpw,6451
skimage/restoration/tests/test_rolling_ball.py,sha256=hIGTGLrlk4LHidMUxjkIxRBsBw7lLscA8jGAR447MfM,3071
skimage/restoration/tests/test_unwrap.py,sha256=6J0xxzISR5CvTZm6di22AlaylLbDf0z63bVZCIg7zXY,8347
skimage/restoration/uft.py,sha256=gTCw6pnKeRO0R7DqMTrOkV3H1r00qHQKoht_k0WnExs,12613
skimage/restoration/unwrap.py,sha256=BxeM0cWdKpQSMCEUR2uzlI5kbTryFtk8tkLBV0zXDn0,4830
skimage/segmentation/__init__.py,sha256=C91Og8vtSoUA7iLroL_byuRj2-JIAhwXF4TAOh1m8KE,1254
skimage/segmentation/__pycache__/__init__.cpython-39.pyc,,
skimage/segmentation/__pycache__/_chan_vese.cpython-39.pyc,,
skimage/segmentation/__pycache__/_clear_border.cpython-39.pyc,,
skimage/segmentation/__pycache__/_expand_labels.cpython-39.pyc,,
skimage/segmentation/__pycache__/_felzenszwalb.cpython-39.pyc,,
skimage/segmentation/__pycache__/_join.cpython-39.pyc,,
skimage/segmentation/__pycache__/_quickshift.cpython-39.pyc,,
skimage/segmentation/__pycache__/_watershed.cpython-39.pyc,,
skimage/segmentation/__pycache__/active_contour_model.cpython-39.pyc,,
skimage/segmentation/__pycache__/boundaries.cpython-39.pyc,,
skimage/segmentation/__pycache__/morphsnakes.cpython-39.pyc,,
skimage/segmentation/__pycache__/random_walker_segmentation.cpython-39.pyc,,
skimage/segmentation/__pycache__/slic_superpixels.cpython-39.pyc,,
skimage/segmentation/_chan_vese.py,sha256=HHgn7gjE8dOqdAt6QM8zyyYZt1SE_EQDtp73c9Gd09I,13774
skimage/segmentation/_clear_border.py,sha256=d9JzobF-EdVq6rQh3IhcSNPDW9FJCWDyNUHSOu9fIgE,3989
skimage/segmentation/_expand_labels.py,sha256=Q8NYU2LYQO5tQ2rXKhg0xpKu-RMwI6WwA_LxrskYlUg,4201
skimage/segmentation/_felzenszwalb.py,sha256=wl6nKzjrWW_fJw-sFeLDq5fhGVtJlZEBD3W4yu8DbFs,2486
skimage/segmentation/_felzenszwalb_cy.cpython-39-darwin.so,sha256=tAPLWFQWP-iFQO4dr_7Hvb-H7YUn16S4hr5PQyPmtcM,146754
skimage/segmentation/_join.py,sha256=j9NIZeDfbDhuU_ARKSZwwJ2QtQVHpHaIryNvG4RXqdI,7124
skimage/segmentation/_quickshift.py,sha256=jPSH6-X45DTnxEA_17EMo0W2fzEoVpc3G2hY_nzlzzs,3469
skimage/segmentation/_quickshift_cy.cpython-39-darwin.so,sha256=VUaxgXhnfb4y5L8v9vdnGmTo49oOsKK6bSJ_GjJtkhA,259408
skimage/segmentation/_slic.cpython-39-darwin.so,sha256=fDAp0nO2CkXIkEAV6ZsQZferl4exQcDnt8lKraX__Io,276519
skimage/segmentation/_watershed.py,sha256=2OR1dqtdKI4HA7wNfbWc-lq-jaA03vRhhDqz7ecmv_8,9770
skimage/segmentation/_watershed_cy.cpython-39-darwin.so,sha256=ZkXlSs_wPe1rr1X9UJTkijFariPmAd5p2FBNaYTokyk,258463
skimage/segmentation/active_contour_model.py,sha256=7jp_RUAJgRMVECywTVT-ofU7Clbac-7yAX5phZgkAJ8,7837
skimage/segmentation/boundaries.py,sha256=-X9FiPehnqGubYIQP4Yq7fcw2-rsj3WNMgzGeQ9lzSY,9985
skimage/segmentation/morphsnakes.py,sha256=cc7Qm7qzkYUjw4q-2hI1p5yjTrdGfXaHXg71sT0gktY,14882
skimage/segmentation/random_walker_segmentation.py,sha256=yw6jiQB2FVAUYcJ1N6fPYdXXhSifUlCVx-oxGfT17KQ,21134
skimage/segmentation/slic_superpixels.py,sha256=FLukNVkXZIEaXepIzzFJjLoG0jv2xr1lZqHtaOfGXaU,16353
skimage/segmentation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/segmentation/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_active_contour_model.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_boundaries.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_chan_vese.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_clear_border.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_expand_labels.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_felzenszwalb.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_join.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_morphsnakes.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_quickshift.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_random_walker.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_slic.cpython-39.pyc,,
skimage/segmentation/tests/__pycache__/test_watershed.cpython-39.pyc,,
skimage/segmentation/tests/test_active_contour_model.py,sha256=HEtlByzNaoFJ4zm-Te6bW6vCsTr4W0cseTM0LM9QgnM,5878
skimage/segmentation/tests/test_boundaries.py,sha256=d7MUg8exUxgWuKLqyV9I_6F0nJ11sPZEJIX1cd4jFOE,5212
skimage/segmentation/tests/test_chan_vese.py,sha256=7uf4IzKfGMzmENIGtHEhXqn2e5-x5MbHmCoi-IlIyxI,3369
skimage/segmentation/tests/test_clear_border.py,sha256=B6HGgyLAuZznVH-8ZI1GJGzhXYJU7nR__byCoExAgco,5517
skimage/segmentation/tests/test_expand_labels.py,sha256=OYRVdLrvrilXfykHrOua8fTmFzSL8IgtJdMpsKSUCHg,6620
skimage/segmentation/tests/test_felzenszwalb.py,sha256=Ffy_yzqAYypIim_GnqHWGFgIdkQAn042GhWrw87iJ2U,2853
skimage/segmentation/tests/test_join.py,sha256=qxzaVATDIZI6OlmDdQQkGA8XZvkV9AkG_t2tc-1kqQc,7106
skimage/segmentation/tests/test_morphsnakes.py,sha256=U2PfLgu1yK0OYQmOH2c9cwZGVMUZPnxbUTp1HmrhLss,4605
skimage/segmentation/tests/test_quickshift.py,sha256=Id75BasgHc5trx6Q6PZDc2T5iELEeqD8mLkwgacLKjY,2388
skimage/segmentation/tests/test_random_walker.py,sha256=_Vdq0HYuX5baoB9QS8wveQrgQzTMIVMP9zrskNc-H-s,18433
skimage/segmentation/tests/test_slic.py,sha256=4qRp3R4bLhkLcpuLH0Yx4JWKDchCw9B84FUAXmP2wSY,18490
skimage/segmentation/tests/test_watershed.py,sha256=1VBRZprLShR1HOJfFXxQdAtgm2McbfWv21Y0x6feUHA,27693
skimage/transform/__init__.py,sha256=cc3l1WUOJrxxgogEj09XMM1cqAMQFnKx3ZIlTb2he6w,1488
skimage/transform/__init__.pyi,sha256=uDxd4AETK9S_MLmnSXoTTAVNUXCy2idNbiWtW4IavPE,1981
skimage/transform/__pycache__/__init__.cpython-39.pyc,,
skimage/transform/__pycache__/_geometric.cpython-39.pyc,,
skimage/transform/__pycache__/_thin_plate_splines.cpython-39.pyc,,
skimage/transform/__pycache__/_warps.cpython-39.pyc,,
skimage/transform/__pycache__/finite_radon_transform.cpython-39.pyc,,
skimage/transform/__pycache__/hough_transform.cpython-39.pyc,,
skimage/transform/__pycache__/integral.cpython-39.pyc,,
skimage/transform/__pycache__/pyramids.cpython-39.pyc,,
skimage/transform/__pycache__/radon_transform.cpython-39.pyc,,
skimage/transform/_geometric.py,sha256=RQuhuIU_4WCqSHoLov8H9BPoty2BLFV-_jFMkdwKMhI,59638
skimage/transform/_hough_transform.cpython-39-darwin.so,sha256=Kxq-c0iKxUFrWrZIADk42PfadRFXPWpSc_Ovkyjdfvg,276514
skimage/transform/_radon_transform.cpython-39-darwin.so,sha256=Fdn1yEnXapwL10bslf_JtWme9XjClxhqjDH4Hm86o64,224930
skimage/transform/_thin_plate_splines.py,sha256=WZxyhkzasSY0v9tCPM8pa6eP36M1l-zbrztIT7rXTLQ,5801
skimage/transform/_warps.py,sha256=0SOXcrkW-C8zzJC8_GSpJfDrGDWSRDODTFz234WaDh4,48511
skimage/transform/_warps_cy.cpython-39-darwin.so,sha256=JbzF2yv7jPoaw9Qby51J_TR8nXh-iVkqGvvlO5LDJeo,276363
skimage/transform/finite_radon_transform.py,sha256=Jln0B-406LJcnpSknQui7xD-igI0-SzO57hGmCkBccw,3179
skimage/transform/hough_transform.py,sha256=z5bPOHpGFjsatTomi9_LHddR_wYjuPZVQ_UKd37S-ZY,15867
skimage/transform/integral.py,sha256=7Rh9I7C0fbuEV7Z9RR-aoZ45I3vzIsgakvq1oYApJ9Y,5096
skimage/transform/pyramids.py,sha256=s6eEuuaVfT7gm2U7oPY-IfKradesoVwTKcjFoDkEemU,13357
skimage/transform/radon_transform.py,sha256=y03ACiOrIETso2bUT9Gp4cbkMzDED0F7reL99I45rK8,20738
skimage/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/transform/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/transform/tests/__pycache__/test_finite_radon_transform.cpython-39.pyc,,
skimage/transform/tests/__pycache__/test_geometric.cpython-39.pyc,,
skimage/transform/tests/__pycache__/test_hough_transform.cpython-39.pyc,,
skimage/transform/tests/__pycache__/test_integral.cpython-39.pyc,,
skimage/transform/tests/__pycache__/test_pyramids.cpython-39.pyc,,
skimage/transform/tests/__pycache__/test_radon_transform.cpython-39.pyc,,
skimage/transform/tests/__pycache__/test_thin_plate_splines.cpython-39.pyc,,
skimage/transform/tests/__pycache__/test_warps.cpython-39.pyc,,
skimage/transform/tests/test_finite_radon_transform.py,sha256=Ms2EwaWZ6irxVaUU-zovgL_3tF-YiFA-r8v3UKV78Ak,316
skimage/transform/tests/test_geometric.py,sha256=j70Jdq-J8w4hxIeC--zDIbZ1MMDsoztYykBHCX9UooU,34383
skimage/transform/tests/test_hough_transform.py,sha256=bEuKtpe3jEofqDVdbOJ8k6m8rrxIzZtfCi32thyn4Jk,19060
skimage/transform/tests/test_integral.py,sha256=4yL_gCYjzk5OtI7unTmBNbEu4k1KLRHiOrXx2rS42Os,2337
skimage/transform/tests/test_pyramids.py,sha256=YLylJjJkJyvE9QeAyAiPL-6w6LdN1KefFdS81waamlI,8047
skimage/transform/tests/test_radon_transform.py,sha256=gmo0Goii6SgriMUtf0Rw4PeV1ZHw0CV42Fb8UPi945c,18625
skimage/transform/tests/test_thin_plate_splines.py,sha256=UfkAFYDuLL7kBUIQ5n_S992fxqMVMtUFthzb0LEy6Cg,2742
skimage/transform/tests/test_warps.py,sha256=hoddh3wEAG0GLCDNHjFyXPwfLNxDp6pRR_aU3J5RUK0,32993
skimage/util/__init__.py,sha256=xYHTdMCfgCBThaDeb9GdeMQKiVQ42jACiP9Ah3EqxK4,1335
skimage/util/__pycache__/__init__.cpython-39.pyc,,
skimage/util/__pycache__/_invert.cpython-39.pyc,,
skimage/util/__pycache__/_label.cpython-39.pyc,,
skimage/util/__pycache__/_map_array.cpython-39.pyc,,
skimage/util/__pycache__/_montage.cpython-39.pyc,,
skimage/util/__pycache__/_regular_grid.cpython-39.pyc,,
skimage/util/__pycache__/_slice_along_axes.cpython-39.pyc,,
skimage/util/__pycache__/apply_parallel.cpython-39.pyc,,
skimage/util/__pycache__/arraycrop.cpython-39.pyc,,
skimage/util/__pycache__/compare.cpython-39.pyc,,
skimage/util/__pycache__/dtype.cpython-39.pyc,,
skimage/util/__pycache__/lookfor.cpython-39.pyc,,
skimage/util/__pycache__/noise.cpython-39.pyc,,
skimage/util/__pycache__/shape.cpython-39.pyc,,
skimage/util/__pycache__/unique.cpython-39.pyc,,
skimage/util/_invert.py,sha256=Yb-ML5OWLDxkrZ1KezI0SUUToJoiWmMDNiQ2bhMGYbw,2560
skimage/util/_label.py,sha256=IIZB-YW5o8kfWw6gWnaiOlKpN9QA2YaTF1k1_cvl7lg,1568
skimage/util/_map_array.py,sha256=isPtpgWGRGxvjjsmghJIu5ZSs1_BgKvvB9hAKFc034U,6684
skimage/util/_montage.py,sha256=zDz0BAOyBIgVU080c2L_qLqWzLM8_BKxDkp5e5i_9LE,4779
skimage/util/_regular_grid.py,sha256=ZPzONVbwy_jK_QWlTqNUW2Wxi9yrvuJblTohugaHVYE,3889
skimage/util/_remap.cpython-39-darwin.so,sha256=A5UblAw3dqeVO2zlwSeZb59SSWl3vf-sKUD5KDhsbY4,783456
skimage/util/_slice_along_axes.py,sha256=GnVxeqAjbDULUc27edh4dBHXFRvsLc0zWkdII7K-G6w,2577
skimage/util/apply_parallel.py,sha256=RmxuSK7XzT3I09W7HNpmxTeFWB6mNNXl4wlMsvZwrM0,7649
skimage/util/arraycrop.py,sha256=VY355ZEFWG0LDjdKM4Nr1EeRnp5dkfIVhhJ7fK-r-6w,2486
skimage/util/compare.py,sha256=99-dgt1POAOxXGe25O3WrU0mVEnf8VR2YTDEp8Sot48,4507
skimage/util/dtype.py,sha256=lo4Q6NjGv8B5VT-JRhIX7kIOAZYlVvl8_3XdtO55c6s,17681
skimage/util/lookfor.py,sha256=dVNQdXVwNjifvr0U7hl8lWnIkA-yx4aUQZhayBdkawE,790
skimage/util/noise.py,sha256=US93udCAXXM9Hyrua8mMfd3fnV91BL50kIv9qJ4LD08,8563
skimage/util/shape.py,sha256=_JqxvcCfpGSCi27jzpbmENBGBrkME6PtWPgBOOZch2s,7828
skimage/util/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/util/tests/__pycache__/__init__.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_apply_parallel.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_arraycrop.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_compare.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_dtype.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_invert.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_labels.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_lookfor.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_map_array.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_montage.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_random_noise.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_regular_grid.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_shape.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_slice_along_axes.cpython-39.pyc,,
skimage/util/tests/__pycache__/test_unique_rows.cpython-39.pyc,,
skimage/util/tests/test_apply_parallel.py,sha256=JJktiQVZthEx31ez46tgh31b9B8XBGXoB8fBVrRbISE,5028
skimage/util/tests/test_arraycrop.py,sha256=74HQ6L3Fr-M7SbzsSysQX5C5WJi_rkCYaT1CEPt2TUY,1848
skimage/util/tests/test_compare.py,sha256=6vw-pKwGQmoeksMI7xaXEDb6t0-fl9oEU-04n8Z2elY,3890
skimage/util/tests/test_dtype.py,sha256=1WgIQDe2AJ6mJSa2Jc51dHdBgLjGal8_cxyyr_7meTM,6538
skimage/util/tests/test_invert.py,sha256=zyGo_pKyh4lkGBYMoH2NUybt8bS8mrLCKORFhfldegg,2415
skimage/util/tests/test_labels.py,sha256=jQoYQ2MwO4TfHLrWOWBc7eXS08M8r4YdAyNR39KsLLE,1791
skimage/util/tests/test_lookfor.py,sha256=mWlprwlDVZ5wZCF5-vpqNCG3JszKzW1pbuFi_u7f-I0,305
skimage/util/tests/test_map_array.py,sha256=_sZd5Y0cOSH4vrffi0ytQv61LChB64pSrtc8yzD3FUA,2900
skimage/util/tests/test_montage.py,sha256=NtQ6oOPXBnP1IedY6PhcAJvJLH0DE11ifXU02CZCsO0,5670
skimage/util/tests/test_random_noise.py,sha256=M-AfwvDPTGx5knXRRg8Fgj76EML8dPmnkKDXFhWjFZU,7670
skimage/util/tests/test_regular_grid.py,sha256=mBlLa6recFg17Re44x25nts1ZLF9GKFs-skHc_yxPdc,980
skimage/util/tests/test_shape.py,sha256=5xNtsof9rRzi_-GfHNeyuHvhCyZE-Q3HIlZ2Y-tOqUs,4549
skimage/util/tests/test_slice_along_axes.py,sha256=3JwGWfOXd9tU1pRtVJQhB1PxFxS5p5FUDomRt9AgGeY,1681
skimage/util/tests/test_unique_rows.py,sha256=YZvcX1tHXRGyg7x9w71v7dJCi4CuS_ZZkZtkfjM2H3k,1099
skimage/util/unique.py,sha256=ctiKRbgA7Zra5-yA58gKup0fksPuPSHYcx_O_HU711E,1516
