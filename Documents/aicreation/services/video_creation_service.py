"""
Main video creation service.

This service orchestrates the entire video creation process using dependency
injection and following clean architecture principles.
"""

import asyncio
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from ..core.interfaces import (
    IVideoCreationService, ITTSProvider, IVideoProvider, 
    IScriptGenerator, IAudioProcessor, IVideoProcessor, IMusicGenerator
)
from ..core.models import VideoRequest, VideoResponse, ScriptData, AudioConfig, VideoConfig
from ..core.logging import get_logger, get_performance_tracker, get_metrics
from ..core.cache import get_cache_manager, CircuitBreaker, RateLimiter
from ..core.exceptions import (
    VideoCreationError, TTSError, VideoProcessingError, 
    AudioProcessingError, ScriptGenerationError
)
from ..core.config import get_config
from ..core.container import auto_inject


class VideoCreationService(IVideoCreationService):
    """Main service for creating videos with full orchestration."""
    
    def __init__(
        self,
        tts_provider: ITTSProvider,
        video_provider: IVideoProvider,
        script_generator: IScriptGenerator,
        audio_processor: IAudioProcessor,
        video_processor: IVideoProcessor,
        music_generator: Optional[IMusicGenerator] = None
    ):
        self.tts_provider = tts_provider
        self.video_provider = video_provider
        self.script_generator = script_generator
        self.audio_processor = audio_processor
        self.video_processor = video_processor
        self.music_generator = music_generator
        
        self.logger = get_logger("video_creation")
        self.performance_tracker = get_performance_tracker()
        self.metrics = get_metrics()
        self.cache_manager = get_cache_manager()
        self.config = get_config()
        
        # Circuit breakers for external services
        self.tts_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=60)
        self.video_circuit_breaker = CircuitBreaker(failure_threshold=5, recovery_timeout=120)
        self.script_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=60)
        
        # Rate limiters
        self.api_rate_limiter = RateLimiter(max_tokens=100, refill_rate=1.0)
        
        # Active video creation tasks
        self._active_tasks: Dict[str, asyncio.Task] = {}
    
    async def create_video(self, request: VideoRequest) -> VideoResponse:
        """Create video from request with full error handling and monitoring."""
        video_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        self.logger.info(
            f"Starting video creation for keyword: {request.keyword}",
            video_id=video_id,
            keyword=request.keyword,
            category=request.category,
            music_type=request.music_type
        )
        
        try:
            # Check rate limits
            if not await self.api_rate_limiter.acquire(f"video_creation_{request.keyword}"):
                raise VideoCreationError(
                    "Rate limit exceeded for video creation",
                    error_code="RATE_LIMIT_EXCEEDED"
                )
            
            # Create task for async processing
            task = asyncio.create_task(self._create_video_internal(request, video_id))
            self._active_tasks[video_id] = task
            
            try:
                response = await task
                response.processing_time = (datetime.utcnow() - start_time).total_seconds()
                
                self.metrics.record_histogram("video_creation_duration", response.processing_time)
                self.metrics.increment_counter("video_creation_success")
                
                return response
                
            finally:
                self._active_tasks.pop(video_id, None)
                
        except Exception as e:
            self.metrics.increment_counter("video_creation_failure")
            
            error_response = VideoResponse(
                success=False,
                error_message=str(e),
                processing_time=(datetime.utcnow() - start_time).total_seconds(),
                metadata={"video_id": video_id, "error_type": type(e).__name__}
            )
            
            self.logger.error(
                f"Video creation failed for keyword: {request.keyword}",
                video_id=video_id,
                error=str(e),
                error_type=type(e).__name__
            )
            
            return error_response
    
    async def _create_video_internal(self, request: VideoRequest, video_id: str) -> VideoResponse:
        """Internal video creation logic."""
        with self.performance_tracker.track_time("video_creation_total"):
            
            # Step 1: Generate script
            script = await self._generate_script(request.keyword, video_id)
            
            # Step 2: Create audio
            audio_path = await self._create_audio(script, request, video_id)
            
            # Step 3: Get video content
            video_clips = await self._get_video_content(request, video_id)
            
            # Step 4: Generate music (if requested)
            music_path = None
            if request.music_type != "none" and self.music_generator:
                music_path = await self._generate_music(request, script.estimated_duration, video_id)
            
            # Step 5: Process audio (enhance, add music)
            final_audio_path = await self._process_audio(audio_path, music_path, request, video_id)
            
            # Step 6: Create final video
            video_path = await self._create_final_video(
                video_clips, final_audio_path, request, video_id
            )
            
            # Step 7: Create thumbnail
            thumbnail_path = await self._create_thumbnail(video_path, script, video_id)
            
            # Step 8: Validate and return response
            return await self._create_response(
                video_path, final_audio_path, thumbnail_path, script, video_id
            )
    
    async def _generate_script(self, keyword: str, video_id: str) -> ScriptData:
        """Generate script with circuit breaker protection."""
        with self.performance_tracker.track_time("script_generation"):
            try:
                script = await self.script_circuit_breaker.call(
                    self.script_generator.generate_script, keyword
                )
                
                self.logger.info(
                    f"Script generated successfully",
                    video_id=video_id,
                    word_count=script.word_count,
                    estimated_duration=script.estimated_duration
                )
                
                return script
                
            except Exception as e:
                raise ScriptGenerationError(
                    f"Failed to generate script for keyword '{keyword}': {str(e)}",
                    original_exception=e
                )
    
    async def _create_audio(self, script: ScriptData, request: VideoRequest, video_id: str) -> Path:
        """Create audio from script with TTS."""
        with self.performance_tracker.track_time("audio_creation"):
            try:
                # Prepare audio config
                audio_config = request.audio_config or AudioConfig()
                
                # Create full text
                full_text = f"{script.introduction} {' '.join(script.key_points)} {script.conclusion}"
                
                # Generate audio file path
                output_dir = self.config.get_directories()["audio"]
                audio_path = output_dir / f"{request.keyword.replace(' ', '_')}_{video_id}.mp3"
                
                # Generate audio with circuit breaker
                success = await self.tts_circuit_breaker.call(
                    self.tts_provider.synthesize, full_text, audio_config, audio_path
                )
                
                if not success or not audio_path.exists():
                    raise TTSError("TTS synthesis failed")
                
                self.logger.info(
                    f"Audio created successfully",
                    video_id=video_id,
                    audio_path=str(audio_path),
                    file_size=audio_path.stat().st_size
                )
                
                return audio_path
                
            except Exception as e:
                raise TTSError(
                    f"Failed to create audio: {str(e)}",
                    provider=self.tts_provider.name,
                    original_exception=e
                )
    
    async def _get_video_content(self, request: VideoRequest, video_id: str) -> list[Path]:
        """Get video content from provider."""
        with self.performance_tracker.track_time("video_content_acquisition"):
            try:
                # Search for videos
                videos = await self.video_circuit_breaker.call(
                    self.video_provider.search_videos,
                    request.keyword,
                    request.category,
                    8  # Get 8 videos for variety
                )
                
                if not videos:
                    raise VideoProcessingError("No videos found for keyword")
                
                # Download videos
                video_paths = []
                temp_dir = self.config.get_directories()["temp"]
                
                for i, video_info in enumerate(videos):
                    video_path = temp_dir / f"{request.keyword}_{video_id}_{i}.mp4"
                    
                    success = await self.video_provider.download_video(video_info, video_path)
                    if success and video_path.exists():
                        video_paths.append(video_path)
                
                if not video_paths:
                    raise VideoProcessingError("Failed to download any videos")
                
                self.logger.info(
                    f"Downloaded {len(video_paths)} video clips",
                    video_id=video_id,
                    clip_count=len(video_paths)
                )
                
                return video_paths
                
            except Exception as e:
                raise VideoProcessingError(
                    f"Failed to get video content: {str(e)}",
                    original_exception=e
                )
    
    async def _generate_music(
        self, 
        request: VideoRequest, 
        duration: float, 
        video_id: str
    ) -> Optional[Path]:
        """Generate background music."""
        if not self.music_generator:
            return None
        
        with self.performance_tracker.track_time("music_generation"):
            try:
                music_path = await self.music_generator.generate_music(
                    style="nature",  # Default style
                    duration=duration,
                    with_vocals=request.with_vocals
                )
                
                if music_path:
                    self.logger.info(
                        f"Music generated successfully",
                        video_id=video_id,
                        music_path=str(music_path),
                        duration=duration
                    )
                
                return music_path
                
            except Exception as e:
                self.logger.warning(
                    f"Music generation failed, continuing without music: {str(e)}",
                    video_id=video_id
                )
                return None
    
    async def _process_audio(
        self, 
        audio_path: Path, 
        music_path: Optional[Path], 
        request: VideoRequest, 
        video_id: str
    ) -> Path:
        """Process and enhance audio."""
        with self.performance_tracker.track_time("audio_processing"):
            try:
                output_dir = self.config.get_directories()["audio"]
                processed_path = output_dir / f"{request.keyword}_processed_{video_id}.mp3"
                
                # Enhance audio
                audio_config = request.audio_config or AudioConfig()
                await self.audio_processor.enhance_audio(audio_path, processed_path, audio_config)
                
                # Add background music if available
                if music_path and music_path.exists():
                    final_path = output_dir / f"{request.keyword}_final_{video_id}.mp3"
                    await self.audio_processor.add_background_music(
                        processed_path, music_path, final_path
                    )
                    return final_path
                
                return processed_path
                
            except Exception as e:
                raise AudioProcessingError(
                    f"Failed to process audio: {str(e)}",
                    original_exception=e
                )
    
    async def _create_final_video(
        self, 
        video_clips: list[Path], 
        audio_path: Path, 
        request: VideoRequest, 
        video_id: str
    ) -> Path:
        """Create final video from clips and audio."""
        with self.performance_tracker.track_time("video_creation"):
            try:
                output_dir = self.config.get_directories()["videos"]
                video_path = output_dir / f"{request.keyword.replace(' ', '_')}_{video_id}.mp4"
                
                video_config = request.video_config or VideoConfig()
                
                success = await self.video_processor.create_video(
                    video_clips, audio_path, video_path, video_config
                )
                
                if not success or not video_path.exists():
                    raise VideoProcessingError("Video creation failed")
                
                self.logger.info(
                    f"Final video created successfully",
                    video_id=video_id,
                    video_path=str(video_path),
                    file_size=video_path.stat().st_size
                )
                
                return video_path
                
            except Exception as e:
                raise VideoProcessingError(
                    f"Failed to create final video: {str(e)}",
                    original_exception=e
                )
    
    async def _create_thumbnail(
        self, 
        video_path: Path, 
        script: ScriptData, 
        video_id: str
    ) -> Optional[Path]:
        """Create video thumbnail."""
        try:
            output_dir = self.config.get_directories()["thumbnails"]
            thumbnail_path = output_dir / f"{video_path.stem}_{video_id}.jpg"
            
            success = await self.video_processor.create_thumbnail(
                video_path, thumbnail_path
            )
            
            if success and thumbnail_path.exists():
                return thumbnail_path
            
        except Exception as e:
            self.logger.warning(f"Thumbnail creation failed: {str(e)}", video_id=video_id)
        
        return None
    
    async def _create_response(
        self, 
        video_path: Path, 
        audio_path: Path, 
        thumbnail_path: Optional[Path], 
        script: ScriptData, 
        video_id: str
    ) -> VideoResponse:
        """Create final response with validation."""
        # Validate files exist
        if not video_path.exists():
            raise VideoCreationError("Video file not found after creation")
        
        # Get file info
        file_size = video_path.stat().st_size
        
        # Estimate duration from audio
        duration = script.estimated_duration
        
        response = VideoResponse(
            success=True,
            video_path=video_path,
            audio_path=audio_path,
            thumbnail_path=thumbnail_path,
            script=script,
            duration=duration,
            file_size=file_size,
            metadata={
                "video_id": video_id,
                "tts_provider": self.tts_provider.name,
                "video_provider": self.video_provider.name
            }
        )
        
        self.logger.info(
            f"Video creation completed successfully",
            video_id=video_id,
            duration=duration,
            file_size=file_size
        )
        
        return response
    
    async def get_video_status(self, video_id: str) -> Dict[str, Any]:
        """Get video creation status."""
        if video_id in self._active_tasks:
            task = self._active_tasks[video_id]
            return {
                "video_id": video_id,
                "status": "processing" if not task.done() else "completed",
                "done": task.done()
            }
        
        return {
            "video_id": video_id,
            "status": "not_found",
            "done": True
        }
    
    async def cancel_video_creation(self, video_id: str) -> bool:
        """Cancel video creation."""
        if video_id in self._active_tasks:
            task = self._active_tasks[video_id]
            task.cancel()
            self._active_tasks.pop(video_id, None)
            
            self.logger.info(f"Video creation cancelled", video_id=video_id)
            return True
        
        return False
