#!/bin/bash
# piper_wrapper.sh - Wrapper script for Piper TTS with proper library paths

# Set library paths for macOS
export DYLD_LIBRARY_PATH="/opt/homebrew/lib:./piper:$DYLD_LIBRARY_PATH"
export DYLD_FALLBACK_LIBRARY_PATH="/opt/homebrew/lib:./piper:$DYLD_FALLBACK_LIBRARY_PATH"

# Set espeak data path
export ESPEAK_DATA_PATH="./piper/espeak-ng-data"

# Run Piper with all arguments passed through
exec ./piper/piper "$@"
