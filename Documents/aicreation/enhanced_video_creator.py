#!/usr/bin/env python3
"""
Enhanced Video Creator v2.0 - Système de création vidéo professionnel
Intègre SkyReels, Piper TTS, Pixabay et toutes les améliorations demandées.
"""

import os
import sys
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import yaml
from loguru import logger
import moviepy.editor as mpy
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import torch

# Imports locaux
from skyreels_manager import SkyReelsManager
from audio_manager import AudioManager
from ollama_manager import OllamaManager
from pixabay_videos import PixabayVideoManager
from music_manager import MusicManager

class VideoFormat(Enum):
    """Formats de vidéo supportés"""
    YOUTUBE = "16:9"      # 1920x1080
    TIKTOK = "9:16"       # 1080x1920
    INSTAGRAM = "1:1"     # 1080x1080
    YOUTUBE_SHORT = "9:16" # 1080x1920

class VoiceGender(Enum):
    """Genre de voix disponibles"""
    FEMALE = "female"
    MALE = "male"
    NEUTRAL = "neutral"

@dataclass
class VideoConfig:
    """Configuration complète pour la création vidéo"""
    # Format et qualité
    format: VideoFormat = VideoFormat.YOUTUBE
    duration: int = 60  # secondes
    fps: int = 30
    quality: str = "high"  # low, medium, high, ultra
    
    # Audio
    voice_gender: VoiceGender = VoiceGender.FEMALE
    add_music: bool = True
    music_volume: float = 0.3
    voice_volume: float = 0.8
    
    # Contenu
    add_subtitles: bool = True
    subtitle_style: str = "modern"  # classic, modern, bold
    add_transitions: bool = True
    transition_type: str = "fade"  # fade, slide, zoom
    
    # IA et génération
    use_skyreels: bool = True
    use_pixabay: bool = True
    script_style: str = "engaging"  # informative, engaging, educational
    
    # Sortie
    output_dir: Path = Path("output/enhanced_videos")
    generate_thumbnail: bool = True
    generate_transcript: bool = True

class EnhancedVideoCreator:
    """Créateur de vidéos amélioré avec toutes les fonctionnalités v2.0"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialise le créateur vidéo amélioré"""
        self.config_path = config_path
        self.setup_logging()
        
        # Chargement de la configuration
        self.yaml_config = self._load_yaml_config()
        
        # Initialisation des composants
        self.skyreels = None
        self.audio_manager = AudioManager()
        self.ollama_manager = OllamaManager()
        self.pixabay_manager = PixabayVideoManager()
        self.music_manager = MusicManager()
        
        # Métriques et cache
        self.generation_stats = {
            "videos_created": 0,
            "total_duration": 0,
            "errors": 0
        }
        
    def setup_logging(self):
        """Configure le système de logging avancé"""
        logger.remove()
        
        # Console avec couleurs
        logger.add(
            sys.stderr,
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO"
        )
        
        # Fichier de logs rotatif
        logger.add(
            "logs/enhanced_video_creator.log",
            rotation="10 MB",
            retention="7 days",
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
        )
        
        logger.info("🚀 Enhanced Video Creator v2.0 initialisé")
    
    def _load_yaml_config(self) -> Dict:
        """Charge la configuration YAML"""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"Configuration chargée depuis {self.config_path}")
            return config
        except Exception as e:
            logger.warning(f"Impossible de charger {self.config_path}: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Configuration par défaut"""
        return {
            "models": {
                "base_path": "./pretrained_models/SkyReels-A1-5B"
            },
            "performance": {
                "use_gpu": torch.cuda.is_available(),
                "use_fp16": True,
                "offload_to_cpu": True
            },
            "video": {
                "default_format": "youtube",
                "quality": "high",
                "fps": 30
            }
        }
    
    def _init_skyreels(self):
        """Initialise SkyReels à la demande"""
        if self.skyreels is None:
            try:
                self.skyreels = SkyReelsManager(self.config_path)
                logger.info("✅ SkyReels initialisé")
            except Exception as e:
                logger.error(f"❌ Erreur initialisation SkyReels: {e}")
                self.skyreels = None
    
    def get_format_dimensions(self, format_type: VideoFormat) -> Tuple[int, int]:
        """Retourne les dimensions pour un format donné"""
        dimensions = {
            VideoFormat.YOUTUBE: (1920, 1080),
            VideoFormat.TIKTOK: (1080, 1920),
            VideoFormat.INSTAGRAM: (1080, 1080),
            VideoFormat.YOUTUBE_SHORT: (1080, 1920)
        }
        return dimensions.get(format_type, (1920, 1080))
    
    async def create_enhanced_video(
        self,
        keyword: str,
        config: VideoConfig
    ) -> Dict[str, str]:
        """
        Crée une vidéo complète avec toutes les améliorations
        
        Args:
            keyword: Mot-clé ou sujet de la vidéo
            config: Configuration de la vidéo
            
        Returns:
            Dict avec les chemins des fichiers générés
        """
        logger.info(f"🎬 Création vidéo améliorée: '{keyword}'")
        logger.info(f"📐 Format: {config.format.value}, Durée: {config.duration}s")
        
        try:
            # Étape 1: Génération du script amélioré
            script_data = await self._generate_enhanced_script(keyword, config)
            
            # Étape 2: Génération audio avec voix sélectionnée
            audio_path = await self._generate_enhanced_audio(script_data, config)
            
            # Étape 3: Génération du contenu visuel
            visual_content = await self._generate_visual_content(keyword, script_data, config)
            
            # Étape 4: Assemblage vidéo avec transitions
            video_path = await self._assemble_enhanced_video(
                visual_content, audio_path, script_data, config
            )
            
            # Étape 5: Génération des fichiers annexes
            extras = await self._generate_extras(video_path, script_data, config)
            
            # Mise à jour des statistiques
            self.generation_stats["videos_created"] += 1
            self.generation_stats["total_duration"] += config.duration
            
            result = {
                "video_path": video_path,
                "audio_path": audio_path,
                **extras
            }
            
            logger.success(f"✅ Vidéo créée avec succès: {video_path}")
            return result
            
        except Exception as e:
            self.generation_stats["errors"] += 1
            logger.error(f"❌ Erreur création vidéo: {e}")
            raise
    
    async def _generate_enhanced_script(self, keyword: str, config: VideoConfig) -> Dict:
        """Génère un script structuré avec Ollama"""
        logger.info("🧠 Génération du script amélioré...")
        
        # Prompt optimisé selon le format
        format_context = {
            VideoFormat.YOUTUBE: "vidéo YouTube éducative de 60 secondes",
            VideoFormat.TIKTOK: "vidéo TikTok engageante de 15-30 secondes",
            VideoFormat.INSTAGRAM: "post Instagram captivant",
            VideoFormat.YOUTUBE_SHORT: "YouTube Short viral"
        }
        
        enhanced_prompt = f"""
        Crée un script pour une {format_context.get(config.format, 'vidéo')} sur le sujet: "{keyword}"
        
        Structure requise:
        1. TITRE: Titre accrocheur (max 60 caractères)
        2. HOOK: Phrase d'accroche captivante (5-10 mots)
        3. INTRODUCTION: Présentation du sujet (15-20 mots)
        4. POINTS_CLES: 3-5 points principaux (15-25 mots chacun)
        5. CONCLUSION: Résumé et CTA (15-20 mots)
        6. HASHTAGS: 5-10 hashtags pertinents
        
        Style: {config.script_style}
        Durée cible: {config.duration} secondes
        
        Réponds en JSON avec cette structure exacte.
        """
        
        try:
            script_response = self.ollama_manager.generate_script(enhanced_prompt)
            
            # Parse et validation du JSON
            if isinstance(script_response, str):
                script_data = json.loads(script_response)
            else:
                script_data = script_response
            
            # Validation des champs requis
            required_fields = ["TITRE", "HOOK", "INTRODUCTION", "POINTS_CLES", "CONCLUSION"]
            for field in required_fields:
                if field not in script_data:
                    script_data[field] = f"Contenu {field.lower()} pour {keyword}"
            
            logger.info(f"✅ Script généré: {script_data.get('TITRE', 'Sans titre')}")
            return script_data
            
        except Exception as e:
            logger.error(f"Erreur génération script: {e}")
            # Script de fallback
            return {
                "TITRE": f"Découvrez {keyword}",
                "HOOK": "Incroyable !",
                "INTRODUCTION": f"Aujourd'hui, nous explorons {keyword}",
                "POINTS_CLES": [
                    f"Point important sur {keyword}",
                    f"Aspect fascinant de {keyword}",
                    f"Impact de {keyword}"
                ],
                "CONCLUSION": "N'oubliez pas de vous abonner !",
                "HASHTAGS": [f"#{keyword.replace(' ', '')}", "#video", "#contenu"]
            }
    
    async def _generate_enhanced_audio(self, script_data: Dict, config: VideoConfig) -> str:
        """Génère l'audio avec la voix sélectionnée"""
        logger.info(f"🎤 Génération audio (voix: {config.voice_gender.value})...")
        
        # Construction du texte complet
        full_text = f"{script_data['INTRODUCTION']} "
        full_text += " ".join(script_data['POINTS_CLES'])
        full_text += f" {script_data['CONCLUSION']}"
        
        # Configuration audio selon le genre de voix
        audio_config = {
            "language": "en",
            "voice_gender": config.voice_gender.value,
            "speed": 1.0,
            "volume": config.voice_volume
        }
        
        try:
            # Génération avec le gestionnaire audio
            audio_path = self.audio_manager.generate_audio(
                {"full_text": full_text}, 
                f"enhanced_{hash(full_text) % 10000}",
                audio_config
            )
            
            if audio_path and Path(audio_path).exists():
                logger.info(f"✅ Audio généré: {audio_path}")
                return audio_path
            else:
                raise Exception("Échec génération audio")
                
        except Exception as e:
            logger.error(f"Erreur génération audio: {e}")
            raise
    
    async def _generate_visual_content(
        self, 
        keyword: str, 
        script_data: Dict, 
        config: VideoConfig
    ) -> Dict:
        """Génère le contenu visuel (SkyReels + Pixabay)"""
        logger.info("🎨 Génération du contenu visuel...")
        
        width, height = self.get_format_dimensions(config.format)
        visual_content = {
            "background_images": [],
            "background_videos": [],
            "generated_images": []
        }
        
        # Génération d'images avec SkyReels si activé
        if config.use_skyreels:
            try:
                self._init_skyreels()
                if self.skyreels:
                    # Prompts pour chaque section
                    prompts = [
                        f"Professional image about {keyword}, high quality, cinematic",
                        f"Modern illustration of {script_data.get('HOOK', keyword)}",
                        f"Visual representation of {keyword}, engaging style"
                    ]
                    
                    for i, prompt in enumerate(prompts):
                        img = self.skyreels.generate_image(
                            prompt=prompt,
                            width=width,
                            height=height,
                            num_inference_steps=30
                        )
                        
                        if img is not None:
                            # Sauvegarde de l'image
                            img_path = config.output_dir / f"generated_{i}_{hash(prompt) % 1000}.png"
                            Image.fromarray(img).save(img_path)
                            visual_content["generated_images"].append(str(img_path))
                            
                    logger.info(f"✅ {len(visual_content['generated_images'])} images SkyReels générées")
            except Exception as e:
                logger.warning(f"SkyReels non disponible: {e}")
        
        # Téléchargement de vidéos Pixabay si activé
        if config.use_pixabay and self.pixabay_manager.available:
            try:
                videos = self.pixabay_manager.get_videos_for_keyword(keyword, count=3)
                visual_content["background_videos"] = videos
                logger.info(f"✅ {len(videos)} vidéos Pixabay téléchargées")
            except Exception as e:
                logger.warning(f"Pixabay non disponible: {e}")
        
        return visual_content
    
    async def _assemble_enhanced_video(
        self,
        visual_content: Dict,
        audio_path: str,
        script_data: Dict,
        config: VideoConfig
    ) -> str:
        """Assemble la vidéo finale avec transitions et effets"""
        logger.info("🎬 Assemblage de la vidéo finale...")
        
        width, height = self.get_format_dimensions(config.format)
        
        try:
            # Chargement de l'audio principal
            audio = mpy.AudioFileClip(audio_path)
            duration = min(audio.duration, config.duration)
            
            # Création des clips visuels
            video_clips = []
            
            # Utilisation des vidéos Pixabay en priorité
            if visual_content["background_videos"]:
                for i, video_path in enumerate(visual_content["background_videos"]):
                    if Path(video_path).exists():
                        clip = (mpy.VideoFileClip(video_path)
                               .resize((width, height))
                               .set_duration(duration / len(visual_content["background_videos"]))
                               .set_start(i * (duration / len(visual_content["background_videos"]))))
                        video_clips.append(clip)
            
            # Fallback sur les images générées
            elif visual_content["generated_images"]:
                for i, img_path in enumerate(visual_content["generated_images"]):
                    clip = (mpy.ImageClip(img_path)
                           .resize((width, height))
                           .set_duration(duration / len(visual_content["generated_images"]))
                           .set_start(i * (duration / len(visual_content["generated_images"]))))
                    video_clips.append(clip)
            
            # Clip de base si pas de contenu visuel
            if not video_clips:
                # Création d'un arrière-plan coloré
                background = mpy.ColorClip(size=(width, height), color=(20, 20, 40), duration=duration)
                video_clips.append(background)
            
            # Ajout des transitions si activé
            if config.add_transitions and len(video_clips) > 1:
                video_clips = self._add_transitions(video_clips, config.transition_type)
            
            # Composition de la vidéo
            final_video = mpy.CompositeVideoClip(video_clips, size=(width, height))
            
            # Ajout des sous-titres si activé
            if config.add_subtitles:
                subtitle_clips = self._create_subtitles(script_data, config, duration)
                final_video = mpy.CompositeVideoClip([final_video] + subtitle_clips)
            
            # Ajout de la musique de fond
            if config.add_music:
                music_path = self.music_manager.generate_music("nature", duration, False)
                if music_path and Path(music_path).exists():
                    music = (mpy.AudioFileClip(music_path)
                            .volumex(config.music_volume)
                            .set_duration(duration))
                    
                    # Mixage audio
                    final_audio = mpy.CompositeAudioClip([audio, music])
                    final_video = final_video.set_audio(final_audio)
                else:
                    final_video = final_video.set_audio(audio)
            else:
                final_video = final_video.set_audio(audio)
            
            # Export de la vidéo
            output_path = config.output_dir / f"enhanced_{hash(str(script_data)) % 10000}.mp4"
            config.output_dir.mkdir(parents=True, exist_ok=True)
            
            final_video.write_videofile(
                str(output_path),
                fps=config.fps,
                codec='libx264',
                audio_codec='aac',
                preset='medium',
                verbose=False,
                logger=None
            )
            
            # Nettoyage
            final_video.close()
            audio.close()
            
            logger.info(f"✅ Vidéo assemblée: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Erreur assemblage vidéo: {e}")
            raise
    
    def _add_transitions(self, clips: List, transition_type: str) -> List:
        """Ajoute des transitions entre les clips"""
        if transition_type == "fade":
            for i in range(len(clips) - 1):
                clips[i] = clips[i].crossfadeout(0.5)
                clips[i + 1] = clips[i + 1].crossfadein(0.5)
        
        return clips
    
    def _create_subtitles(self, script_data: Dict, config: VideoConfig, duration: float) -> List:
        """Crée les clips de sous-titres"""
        subtitle_clips = []
        width, height = self.get_format_dimensions(config.format)
        
        # Style des sous-titres selon la configuration
        font_size = 60 if config.format in [VideoFormat.YOUTUBE] else 45
        
        # Texte principal
        main_text = f"{script_data['INTRODUCTION']} {' '.join(script_data['POINTS_CLES'])} {script_data['CONCLUSION']}"
        
        # Création du clip de sous-titres
        subtitle = (mpy.TextClip(
            main_text,
            fontsize=font_size,
            color='white',
            stroke_color='black',
            stroke_width=2,
            method='caption',
            size=(width * 0.8, None)
        )
        .set_position(('center', height * 0.8))
        .set_duration(duration)
        .crossfadein(0.5)
        .crossfadeout(0.5))
        
        subtitle_clips.append(subtitle)
        
        return subtitle_clips
    
    async def _generate_extras(self, video_path: str, script_data: Dict, config: VideoConfig) -> Dict:
        """Génère les fichiers annexes (thumbnail, transcript, etc.)"""
        extras = {}
        
        # Génération de la vignette
        if config.generate_thumbnail:
            try:
                thumbnail_path = await self._generate_thumbnail(video_path, script_data, config)
                extras["thumbnail_path"] = thumbnail_path
            except Exception as e:
                logger.warning(f"Erreur génération vignette: {e}")
        
        # Génération du transcript
        if config.generate_transcript:
            try:
                transcript_path = await self._generate_transcript(script_data, config)
                extras["transcript_path"] = transcript_path
            except Exception as e:
                logger.warning(f"Erreur génération transcript: {e}")
        
        return extras
    
    async def _generate_thumbnail(self, video_path: str, script_data: Dict, config: VideoConfig) -> str:
        """Génère une vignette attractive"""
        logger.info("🎨 Génération de la vignette...")
        
        width, height = self.get_format_dimensions(config.format)
        
        # Création d'une image de base
        img = Image.new('RGB', (width, height), color=(30, 30, 50))
        draw = ImageDraw.Draw(img)
        
        # Ajout du titre
        try:
            font = ImageFont.truetype("Arial.ttf", 72)
        except:
            font = ImageFont.load_default()
        
        title = script_data.get('TITRE', 'Vidéo')
        
        # Calcul de la position du texte
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # Dessin du texte avec contour
        draw.text((x-2, y-2), title, font=font, fill=(0, 0, 0))
        draw.text((x+2, y+2), title, font=font, fill=(0, 0, 0))
        draw.text((x, y), title, font=font, fill=(255, 255, 255))
        
        # Sauvegarde
        thumbnail_path = config.output_dir / f"thumbnail_{hash(title) % 1000}.jpg"
        img.save(thumbnail_path, quality=95)
        
        logger.info(f"✅ Vignette générée: {thumbnail_path}")
        return str(thumbnail_path)
    
    async def _generate_transcript(self, script_data: Dict, config: VideoConfig) -> str:
        """Génère un fichier de transcript SRT"""
        logger.info("📝 Génération du transcript...")
        
        # Construction du contenu SRT
        srt_content = []
        
        # Timing approximatif basé sur la durée
        sections = [
            ("HOOK", 0, 3),
            ("INTRODUCTION", 3, 8),
            ("POINTS_CLES", 8, config.duration - 5),
            ("CONCLUSION", config.duration - 5, config.duration)
        ]
        
        subtitle_id = 1
        for section, start_time, end_time in sections:
            if section in script_data:
                text = script_data[section]
                if isinstance(text, list):
                    text = " ".join(text)
                
                # Format SRT
                start_srt = self._seconds_to_srt_time(start_time)
                end_srt = self._seconds_to_srt_time(end_time)
                
                srt_content.append(f"{subtitle_id}")
                srt_content.append(f"{start_srt} --> {end_srt}")
                srt_content.append(text)
                srt_content.append("")
                
                subtitle_id += 1
        
        # Sauvegarde
        transcript_path = config.output_dir / f"transcript_{hash(str(script_data)) % 1000}.srt"
        with open(transcript_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(srt_content))
        
        logger.info(f"✅ Transcript généré: {transcript_path}")
        return str(transcript_path)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """Convertit les secondes en format SRT (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def get_stats(self) -> Dict:
        """Retourne les statistiques de génération"""
        return self.generation_stats.copy()
    
    def cleanup(self):
        """Nettoie les ressources"""
        if self.skyreels:
            self.skyreels.cleanup()
        logger.info("🧹 Nettoyage terminé")

# Interface CLI améliorée
async def main():
    """Interface en ligne de commande améliorée"""
    print("🎬 Enhanced Video Creator v2.0")
    print("=" * 50)
    
    creator = EnhancedVideoCreator()
    
    try:
        # Configuration interactive
        keyword = input("🎯 Sujet de la vidéo: ").strip()
        if not keyword:
            print("❌ Le sujet ne peut pas être vide")
            return
        
        # Sélection du format
        print("\n📐 Format de vidéo:")
        formats = list(VideoFormat)
        for i, fmt in enumerate(formats, 1):
            print(f"   {i}. {fmt.name} ({fmt.value})")
        
        format_choice = input("Choisissez (1-4): ").strip()
        try:
            selected_format = formats[int(format_choice) - 1]
        except (ValueError, IndexError):
            selected_format = VideoFormat.YOUTUBE
        
        # Configuration de base
        config = VideoConfig(
            format=selected_format,
            duration=60 if selected_format == VideoFormat.YOUTUBE else 30,
            add_subtitles=True,
            add_transitions=True,
            use_skyreels=True,
            use_pixabay=True
        )
        
        print(f"\n🚀 Création en cours...")
        print(f"📐 Format: {config.format.name}")
        print(f"⏱️ Durée: {config.duration}s")
        
        # Création de la vidéo
        result = await creator.create_enhanced_video(keyword, config)
        
        # Affichage des résultats
        print("\n✅ VIDÉO CRÉÉE AVEC SUCCÈS!")
        print("=" * 40)
        for key, path in result.items():
            if path:
                file_size = Path(path).stat().st_size / (1024 * 1024)
                print(f"📁 {key}: {path} ({file_size:.1f} MB)")
        
        # Statistiques
        stats = creator.get_stats()
        print(f"\n📊 Statistiques: {stats['videos_created']} vidéos créées")
        
    except KeyboardInterrupt:
        print("\n👋 Arrêt demandé par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
    finally:
        creator.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
