"""
Text-to-Speech provider implementations.

This module contains various TTS provider implementations following the
ITTSProvider interface for consistent behavior and easy swapping.
"""

import asyncio
import subprocess
import tempfile
from pathlib import Path
from typing import List, Optional, Dict, Any
from abc import ABC

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from ..core.interfaces import ITTSProvider
from ..core.models import AudioConfig
from ..core.logging import get_logger, get_performance_tracker
from ..core.cache import get_cache_manager
from ..core.exceptions import TTSError, ConfigurationError
from ..core.config import get_config


class BaseTTSProvider(ITTSProvider, ABC):
    """Base class for TTS providers with common functionality."""
    
    def __init__(self):
        self.logger = get_logger(f"tts.{self.name}")
        self.performance_tracker = get_performance_tracker()
        self.cache_manager = get_cache_manager()
        self.config = get_config()
    
    def estimate_duration(self, text: str) -> float:
        """Estimate audio duration based on word count."""
        words = len(text.split())
        # Average speaking rate: 150-160 words per minute
        return (words / 155) * 60
    
    async def _validate_config(self, config: AudioConfig):
        """Validate audio configuration."""
        if config.language not in self.get_supported_languages():
            raise TTSError(
                f"Language {config.language} not supported by {self.name}",
                provider=self.name
            )


class PiperTTSProvider(BaseTTSProvider):
    """Piper TTS provider for high-quality local synthesis."""
    
    def __init__(self, piper_path: Optional[Path] = None):
        super().__init__()
        self.piper_path = piper_path or self._find_piper_executable()
        self._models_cache: Dict[str, Path] = {}
    
    @property
    def name(self) -> str:
        return "piper"
    
    @property
    def is_available(self) -> bool:
        """Check if Piper TTS is available."""
        return (
            self.piper_path is not None and 
            self.piper_path.exists() and
            self._has_models()
        )
    
    def _find_piper_executable(self) -> Optional[Path]:
        """Find Piper executable in common locations."""
        possible_paths = [
            Path("/Users/<USER>/Downloads/piper/piper"),
            Path("/usr/local/bin/piper"),
            Path("/opt/piper/piper"),
            Path("./piper/piper")
        ]
        
        for path in possible_paths:
            if path.exists() and path.is_file():
                return path
        
        return None
    
    def _has_models(self) -> bool:
        """Check if any Piper models are available."""
        models_dir = Path.home() / ".local/share/piper/models"
        if not models_dir.exists():
            return False
        
        model_files = list(models_dir.glob("*.onnx"))
        return len(model_files) > 0
    
    def _get_best_model(self, language: str) -> Optional[Path]:
        """Get the best available model for the language."""
        models_dir = Path.home() / ".local/share/piper/models"
        
        # Language-specific model preferences
        model_preferences = {
            "en": ["en_US-lessac-medium.onnx", "en_US-ryan-medium.onnx", "en_GB-alan-medium.onnx"],
            "fr": ["fr_FR-gilles-low.onnx", "fr_FR-mls_1840-low.onnx"],
            "es": ["es_ES-mls_9972-low.onnx", "es_MX-ald-medium.onnx"],
            "de": ["de_DE-thorsten-medium.onnx", "de_DE-mls_1840-low.onnx"]
        }
        
        # Try preferred models first
        if language in model_preferences:
            for model_name in model_preferences[language]:
                model_path = models_dir / model_name
                if model_path.exists():
                    return model_path
        
        # Fallback to any model for the language
        pattern = f"{language}_*-*.onnx"
        models = list(models_dir.glob(pattern))
        if models:
            return models[0]
        
        # Last resort: any available model
        all_models = list(models_dir.glob("*.onnx"))
        return all_models[0] if all_models else None
    
    async def synthesize(
        self, 
        text: str, 
        config: AudioConfig,
        output_path: Path
    ) -> bool:
        """Synthesize text using Piper TTS."""
        if not self.is_available:
            raise TTSError("Piper TTS not available", provider=self.name)
        
        await self._validate_config(config)
        
        with self.performance_tracker.track_time("piper_synthesis"):
            try:
                model_path = self._get_best_model(config.language)
                if not model_path:
                    raise TTSError(f"No Piper model found for language {config.language}")
                
                # Create temporary text file
                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                    f.write(text)
                    text_file = Path(f.name)
                
                try:
                    # Run Piper synthesis
                    cmd = [
                        str(self.piper_path),
                        "--model", str(model_path),
                        "--output_file", str(output_path)
                    ]
                    
                    with open(text_file, 'r') as input_file:
                        result = await asyncio.create_subprocess_exec(
                            *cmd,
                            stdin=input_file,
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )
                        
                        stdout, stderr = await asyncio.wait_for(
                            result.communicate(), 
                            timeout=60.0
                        )
                    
                    if result.returncode == 0 and output_path.exists():
                        self.logger.info(f"Piper synthesis successful: {output_path}")
                        return True
                    else:
                        error_msg = stderr.decode() if stderr else "Unknown error"
                        raise TTSError(f"Piper synthesis failed: {error_msg}")
                
                finally:
                    text_file.unlink(missing_ok=True)
                    
            except asyncio.TimeoutError:
                raise TTSError("Piper synthesis timeout", provider=self.name)
            except Exception as e:
                raise TTSError(f"Piper synthesis error: {str(e)}", provider=self.name)
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return ["en", "fr", "es", "de", "it", "pt", "ru", "zh", "ja", "ko"]


class GTTSProvider(BaseTTSProvider):
    """Google Text-to-Speech provider."""
    
    @property
    def name(self) -> str:
        return "gtts"
    
    @property
    def is_available(self) -> bool:
        return GTTS_AVAILABLE
    
    @get_cache_manager().cached(ttl=3600, key_prefix="gtts")
    async def synthesize(
        self, 
        text: str, 
        config: AudioConfig,
        output_path: Path
    ) -> bool:
        """Synthesize text using Google TTS."""
        if not self.is_available:
            raise TTSError("gTTS not available", provider=self.name)
        
        await self._validate_config(config)
        
        with self.performance_tracker.track_time("gtts_synthesis"):
            try:
                # Create gTTS object
                tts = gTTS(
                    text=text,
                    lang=config.language,
                    slow=False
                )
                
                # Save to temporary file first
                temp_path = output_path.with_suffix('.tmp.mp3')
                
                # Run in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, tts.save, str(temp_path))
                
                # Move to final location
                temp_path.rename(output_path)
                
                self.logger.info(f"gTTS synthesis successful: {output_path}")
                return True
                
            except Exception as e:
                raise TTSError(f"gTTS synthesis error: {str(e)}", provider=self.name)
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return [
            "en", "fr", "es", "de", "it", "pt", "ru", "zh", "ja", "ko",
            "ar", "hi", "tr", "pl", "nl", "sv", "da", "no", "fi"
        ]


class OpenAITTSProvider(BaseTTSProvider):
    """OpenAI TTS provider."""
    
    def __init__(self):
        super().__init__()
        self.client = None
        if OPENAI_AVAILABLE and self.config.apis.openai_api_key:
            openai.api_key = self.config.apis.openai_api_key
            self.client = openai.OpenAI()
    
    @property
    def name(self) -> str:
        return "openai"
    
    @property
    def is_available(self) -> bool:
        return OPENAI_AVAILABLE and self.client is not None
    
    async def synthesize(
        self, 
        text: str, 
        config: AudioConfig,
        output_path: Path
    ) -> bool:
        """Synthesize text using OpenAI TTS."""
        if not self.is_available:
            raise TTSError("OpenAI TTS not available", provider=self.name)
        
        with self.performance_tracker.track_time("openai_synthesis"):
            try:
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.client.audio.speech.create(
                        model="tts-1-hd",
                        voice="alloy",
                        input=text,
                        response_format="mp3"
                    )
                )
                
                # Save response to file
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_bytes():
                        f.write(chunk)
                
                self.logger.info(f"OpenAI TTS synthesis successful: {output_path}")
                return True
                
            except Exception as e:
                raise TTSError(f"OpenAI TTS error: {str(e)}", provider=self.name)
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return ["en", "fr", "es", "de", "it", "pt", "ru", "zh", "ja", "ko", "ar", "hi"]


class TTSProviderFactory:
    """Factory for creating TTS providers."""
    
    def __init__(self):
        self.logger = get_logger("tts_factory")
        self._providers: Dict[str, type] = {
            "piper": PiperTTSProvider,
            "gtts": GTTSProvider,
            "openai": OpenAITTSProvider
        }
    
    def create_provider(self, provider_name: str) -> ITTSProvider:
        """Create a TTS provider by name."""
        if provider_name not in self._providers:
            raise ConfigurationError(f"Unknown TTS provider: {provider_name}")
        
        provider_class = self._providers[provider_name]
        provider = provider_class()
        
        if not provider.is_available:
            raise ConfigurationError(f"TTS provider {provider_name} is not available")
        
        return provider
    
    def get_available_providers(self) -> List[str]:
        """Get list of available TTS providers."""
        available = []
        for name, provider_class in self._providers.items():
            try:
                provider = provider_class()
                if provider.is_available:
                    available.append(name)
            except Exception:
                pass
        return available
    
    def get_best_provider(self, language: str = "en") -> ITTSProvider:
        """Get the best available provider for the language."""
        # Priority order: Piper > OpenAI > gTTS
        priority_order = ["piper", "openai", "gtts"]
        
        for provider_name in priority_order:
            try:
                provider = self.create_provider(provider_name)
                if language in provider.get_supported_languages():
                    return provider
            except ConfigurationError:
                continue
        
        raise ConfigurationError(f"No TTS provider available for language {language}")
