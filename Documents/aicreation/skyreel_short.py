#!/usr/bin/env python3
"""
Générateur de vidéos courtes avec SkyReels
"""

import os
from pathlib import Path
import random
from typing import Optional, List, Dict
import numpy as np
import moviepy.editor as mpy
from PIL import Image
from loguru import logger
import sys
import torch

from skyreels_manager import SkyReelsManager
from audio_manager import AudioManager

class SkyreelShortConfig:
    """Configuration pour la génération de vidéos courtes avec Skyreel"""
    def __init__(self):
        self.width = 1080  # Format vertical 9:16
        self.height = 1920
        self.fps = 30
        self.duration = 8  # 8 secondes
        self.font_size = 72
        self.font = "Arial"
        self.text_color = 'white'
        self.stroke_color = 'black'
        self.music_volume = 0.4

class SkyreelShortCreator:
    """Créateur de vidéos courtes avec SkyReels"""
    
    def __init__(self):
        self.config = SkyreelShortConfig()
        self.skyreel = SkyReelsManager()
        self.audio = AudioManager()
        self.output_dir = Path("output/shorts")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def create_video(
        self,
        prompt: str,
        title: str,
        text: List[str],
        add_music: bool = True
    ) -> str:
        """
        Crée une vidéo courte avec SkyReels
        
        Args:
            prompt: Description pour la génération d'image
            title: Titre de la vidéo
            text: Liste des textes à afficher
            add_music: Ajouter de la musique de fond
        """
        try:
            logger.info(f"Création d'une vidéo courte: {title}")
            
            # Génération de l'image avec SkyReels
            background_img = self.skyreel.generate_image(
                prompt=prompt,
                width=self.config.width,
                height=self.config.height
            )
            
            # Création du clip vidéo de base
            background_clip = mpy.ImageClip(background_img).set_duration(self.config.duration)
            
            # Création des clips de texte
            text_clips = self._create_text_animation(title, text)
            
            # Composition de la vidéo
            video = mpy.CompositeVideoClip(
                [background_clip] + text_clips,
                size=(self.config.width, self.config.height)
            )
            
            # Ajout de la musique si demandé
            if add_music:
                music_path = self.audio.get_background_music()
                if music_path:
                    music = (mpy.AudioFileClip(music_path)
                            .subclip(0, self.config.duration)
                            .volumex(self.config.music_volume))
                    video = video.set_audio(music)
            
            # Export de la vidéo
            output_path = str(self.output_dir / f"skyreel_short_{random.randint(1000,9999)}.mp4")
            video.write_videofile(
                output_path,
                fps=self.config.fps,
                codec='libx264',
                audio_codec='aac',
                preset='ultrafast'
            )
            
            logger.info(f"✅ Vidéo créée: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Erreur lors de la création de la vidéo: {e}")
            return ""
            
    def _create_text_animation(self, title: str, text_lines: List[str]) -> List[mpy.VideoClip]:
        """Crée l'animation du texte"""
        clips = []
        
        # Animation du titre
        title_clip = (mpy.TextClip(
            title,
            font=self.config.font,
            fontsize=self.config.font_size * 1.2,
            color=self.config.text_color,
            stroke_color=self.config.stroke_color,
            stroke_width=2,
            method='caption',
            size=(self.config.width * 0.8, None)
        )
        .set_position(('center', self.config.height * 0.2))
        .set_duration(self.config.duration)
        .crossfadein(0.5)
        .crossfadeout(0.5))
        
        clips.append(title_clip)
        
        # Animation du texte ligne par ligne
        for i, line in enumerate(text_lines):
            y_pos = self.config.height * (0.4 + i * 0.15)
            delay = i * 0.5  # Délai entre chaque ligne
            
            text_clip = (mpy.TextClip(
                line,
                font=self.config.font,
                fontsize=self.config.font_size,
                color=self.config.text_color,
                stroke_color=self.config.stroke_color,
                stroke_width=2,
                method='caption',
                size=(self.config.width * 0.8, None)
            )
            .set_position(('center', y_pos))
            .set_start(delay)
            .set_duration(self.config.duration - delay)
            .crossfadein(0.5)
            .crossfadeout(0.5))
            
            clips.append(text_clip)
            
        return clips

def setup_directories():
    """Crée les répertoires nécessaires s'ils n'existent pas"""
    directories = [
        "output/shorts",
        "output/audio",
        "assets/music",
        "logs"
    ]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Répertoire vérifié: {directory}")

def main():
    """Point d'entrée principal"""
    try:
        # Création des répertoires nécessaires
        setup_directories()
        
        creator = SkyreelShortCreator()
        
        # Configuration de la vidéo test
        prompt = "Un paysage naturel époustouflant avec des montagnes majestueuses reflétées dans un lac cristallin au coucher du soleil, style photoréaliste"
        title = "🏔️ La Magie de la Nature"
        text = [
            "Découvrez la beauté",
            "de nos paysages",
            "à chaque instant ✨"
        ]
        
        # Création de la vidéo
        logger.info("Début de la création de la vidéo...")
        video_path = creator.create_video(prompt, title, text, add_music=True)
        
        if video_path:
            file_size = Path(video_path).stat().st_size / (1024 * 1024)
            logger.info("✅ Succès!")
            logger.info(f"📍 Vidéo créée: {video_path}")
            logger.info(f"📦 Taille: {file_size:.1f} MB")
        else:
            logger.error("❌ Échec de la création de la vidéo")
            
    except KeyboardInterrupt:
        logger.warning("Interruption manuelle du programme")
    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Configuration du logger
    logger.remove()
    logger.add(
        sys.stderr,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>"
    )
    logger.add(
        "logs/skyreel_shorts.log",
        rotation="1 day",
        retention="7 days",
        level="INFO"
    )
    
    main()
