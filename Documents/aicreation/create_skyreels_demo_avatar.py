#!/usr/bin/env python3
"""
Créateur d'avatar SkyReels-A1 simplifié pour macOS
Version de démonstration sans dépendances complexes
"""

import os
import sys
import time
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import cv2
import subprocess
from loguru import logger

def create_talking_avatar_demo(image_path: str, output_dir: str = "outputs"):
    """
    Crée une vidéo d'avatar parlant de démonstration
    
    Args:
        image_path: Chemin vers l'image de portrait
        output_dir: Dossier de sortie
    """
    logger.info("🎭 Création d'avatar parlant de démonstration")
    logger.info(f"📸 Image source: {image_path}")
    
    try:
        # Vérifier que l'image existe
        if not Path(image_path).exists():
            raise FileNotFoundError(f"Image non trouvée: {image_path}")
        
        # Charger l'image
        avatar_image = Image.open(image_path)
        logger.info(f"✅ Image chargée: {avatar_image.size}")
        
        # Redimensionner pour traitement
        target_size = (512, 512)
        avatar_image = avatar_image.resize(target_size, Image.Resampling.LANCZOS)
        
        # Créer les frames d'animation
        frames = create_talking_animation_frames(avatar_image)
        logger.info(f"✅ {len(frames)} frames d'animation créées")
        
        # Sauvegarder les frames
        frames_dir = Path(output_dir) / "avatar_frames"
        frames_dir.mkdir(parents=True, exist_ok=True)
        
        # Nettoyer les anciennes frames
        for old_frame in frames_dir.glob("frame_*.png"):
            old_frame.unlink()
        
        # Sauvegarder les nouvelles frames
        for i, frame in enumerate(frames):
            frame_path = frames_dir / f"frame_{i:04d}.png"
            frame.save(frame_path)
        
        logger.info(f"✅ Frames sauvegardées dans {frames_dir}")
        
        # Créer la vidéo
        video_path = create_video_from_frames(frames_dir, output_dir)
        
        if video_path:
            logger.success(f"🎉 Avatar vidéo créé: {video_path}")
            return video_path
        else:
            logger.error("❌ Échec création vidéo")
            return None
            
    except Exception as e:
        logger.error(f"❌ Erreur création avatar: {e}")
        return None

def create_talking_animation_frames(base_image: Image.Image, duration: int = 8, fps: int = 12):
    """
    Crée les frames d'animation pour un avatar parlant
    
    Args:
        base_image: Image de base de l'avatar
        duration: Durée en secondes
        fps: Images par seconde
    """
    total_frames = duration * fps
    frames = []
    
    logger.info(f"🎬 Création de {total_frames} frames d'animation")
    
    # Convertir en array numpy pour manipulation
    base_array = np.array(base_image)
    height, width = base_array.shape[:2]
    
    for frame_num in range(total_frames):
        progress = frame_num / total_frames
        
        # Créer une copie de l'image de base
        frame_array = base_array.copy()
        
        # Simuler des mouvements de bouche
        mouth_intensity = simulate_mouth_movement(progress, frame_num)
        
        # Simuler des clignements d'yeux
        eye_intensity = simulate_eye_blink(progress, frame_num)
        
        # Appliquer les animations
        frame_array = apply_mouth_animation(frame_array, mouth_intensity)
        frame_array = apply_eye_animation(frame_array, eye_intensity)
        
        # Ajouter des micro-mouvements de tête
        frame_array = apply_head_movement(frame_array, progress, frame_num)
        
        # Convertir en PIL Image
        frame = Image.fromarray(frame_array)
        
        # Ajouter des effets de post-traitement
        frame = add_realistic_effects(frame, progress)
        
        frames.append(frame)
        
        if frame_num % fps == 0:  # Log toutes les secondes
            logger.info(f"   📹 Frame {frame_num}/{total_frames} ({frame_num//fps}s)")
    
    return frames

def simulate_mouth_movement(progress: float, frame_num: int) -> float:
    """Simule les mouvements de bouche pour parler"""
    # Différentes fréquences pour simuler la parole
    base_freq = 4  # Mouvements de base
    detail_freq = 12  # Détails de prononciation
    
    # Combinaison de plusieurs ondes pour réalisme
    base_movement = np.sin(progress * base_freq * 2 * np.pi)
    detail_movement = 0.3 * np.sin(progress * detail_freq * 2 * np.pi)
    random_variation = 0.1 * np.sin(frame_num * 0.7)
    
    # Intensité finale (0 à 1)
    intensity = 0.5 + 0.3 * (base_movement + detail_movement + random_variation)
    return max(0, min(1, intensity))

def simulate_eye_blink(progress: float, frame_num: int) -> float:
    """Simule les clignements d'yeux naturels"""
    # Clignements occasionnels
    blink_frequency = 0.3  # Environ 3 clignements par 10 secondes
    
    # Détection des moments de clignement
    blink_trigger = np.sin(progress * blink_frequency * 2 * np.pi)
    
    if blink_trigger > 0.8:  # Seuil de clignement
        # Animation rapide de clignement
        blink_phase = (frame_num % 6) / 6  # Clignement sur 6 frames
        if blink_phase < 0.3:
            return 1 - blink_phase / 0.3  # Fermeture
        elif blink_phase < 0.7:
            return 0  # Fermé
        else:
            return (blink_phase - 0.7) / 0.3  # Ouverture
    
    return 1  # Yeux ouverts

def apply_mouth_animation(image_array: np.ndarray, intensity: float) -> np.ndarray:
    """Applique l'animation de bouche à l'image"""
    # Simulation simple : légère déformation dans la zone de la bouche
    height, width = image_array.shape[:2]
    
    # Zone approximative de la bouche (bas du visage)
    mouth_y_start = int(height * 0.65)
    mouth_y_end = int(height * 0.85)
    mouth_x_start = int(width * 0.35)
    mouth_x_end = int(width * 0.65)
    
    # Appliquer une légère déformation
    mouth_region = image_array[mouth_y_start:mouth_y_end, mouth_x_start:mouth_x_end].copy()
    
    # Simulation d'ouverture de bouche par étirement vertical
    stretch_factor = 1 + intensity * 0.1  # Étirement léger
    
    if stretch_factor != 1:
        mouth_height = mouth_y_end - mouth_y_start
        new_height = int(mouth_height * stretch_factor)
        
        # Redimensionner la région de la bouche
        mouth_pil = Image.fromarray(mouth_region)
        mouth_stretched = mouth_pil.resize((mouth_x_end - mouth_x_start, new_height), Image.Resampling.LANCZOS)
        mouth_stretched_array = np.array(mouth_stretched)
        
        # Replacer dans l'image (centré)
        y_offset = (mouth_height - new_height) // 2
        if new_height <= mouth_height:
            image_array[mouth_y_start + y_offset:mouth_y_start + y_offset + new_height, 
                       mouth_x_start:mouth_x_end] = mouth_stretched_array
    
    return image_array

def apply_eye_animation(image_array: np.ndarray, intensity: float) -> np.ndarray:
    """Applique l'animation des yeux (clignements)"""
    if intensity < 1:  # Yeux partiellement fermés
        height, width = image_array.shape[:2]
        
        # Zones approximatives des yeux
        eye_y_start = int(height * 0.35)
        eye_y_end = int(height * 0.45)
        
        # Assombrir légèrement la zone des yeux pour simuler la fermeture
        darkening_factor = 1 - (1 - intensity) * 0.3
        image_array[eye_y_start:eye_y_end, :] = (image_array[eye_y_start:eye_y_end, :] * darkening_factor).astype(np.uint8)
    
    return image_array

def apply_head_movement(image_array: np.ndarray, progress: float, frame_num: int) -> np.ndarray:
    """Applique de légers mouvements de tête"""
    # Micro-mouvements très subtils
    movement_x = int(2 * np.sin(progress * 3 * 2 * np.pi))  # ±2 pixels
    movement_y = int(1 * np.cos(progress * 2 * 2 * np.pi))  # ±1 pixel
    
    # Translation de l'image
    if movement_x != 0 or movement_y != 0:
        height, width = image_array.shape[:2]
        
        # Matrice de translation
        M = np.float32([[1, 0, movement_x], [0, 1, movement_y]])
        
        # Appliquer la translation
        image_array = cv2.warpAffine(image_array, M, (width, height), borderMode=cv2.BORDER_REPLICATE)
    
    return image_array

def add_realistic_effects(frame: Image.Image, progress: float) -> Image.Image:
    """Ajoute des effets réalistes à la frame"""
    # Légère variation de luminosité pour simuler l'éclairage naturel
    brightness_variation = 1 + 0.02 * np.sin(progress * 8 * 2 * np.pi)
    
    # Appliquer la variation de luminosité
    from PIL import ImageEnhance
    enhancer = ImageEnhance.Brightness(frame)
    frame = enhancer.enhance(brightness_variation)
    
    # Très léger flou pour adoucir les transitions
    frame = frame.filter(ImageFilter.GaussianBlur(radius=0.2))
    
    return frame

def create_video_from_frames(frames_dir: Path, output_dir: str, fps: int = 12) -> str:
    """Crée une vidéo à partir des frames"""
    logger.info("🎬 Création de la vidéo finale...")
    
    output_path = Path(output_dir) / f"skyreels_avatar_{int(time.time())}.mp4"
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Commande FFmpeg
    ffmpeg_cmd = [
        "ffmpeg", "-y",
        "-framerate", str(fps),
        "-i", str(frames_dir / "frame_%04d.png"),
        "-c:v", "libx264",
        "-pix_fmt", "yuv420p",
        "-crf", "23",
        "-preset", "medium",
        str(output_path)
    ]
    
    try:
        logger.info(f"🔧 Commande FFmpeg: {' '.join(ffmpeg_cmd)}")
        result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            file_size = output_path.stat().st_size / (1024 * 1024)
            logger.success(f"✅ Vidéo créée: {output_path}")
            logger.info(f"📦 Taille: {file_size:.1f} MB")
            logger.info(f"⏱️ Durée: 8 secondes")
            logger.info(f"🎞️ FPS: {fps}")
            return str(output_path)
        else:
            logger.error(f"❌ Erreur FFmpeg: {result.stderr}")
            return None
            
    except FileNotFoundError:
        logger.error("❌ FFmpeg non trouvé. Installation requise:")
        logger.info("   brew install ffmpeg  # macOS")
        return None
    except Exception as e:
        logger.error(f"❌ Erreur création vidéo: {e}")
        return None

def create_audio_for_avatar(output_dir: str = "outputs") -> str:
    """Crée un fichier audio de démonstration pour l'avatar"""
    logger.info("🎤 Création audio de démonstration...")
    
    try:
        # Utiliser gTTS pour créer un audio de test
        from gtts import gTTS
        
        # Texte de démonstration pour journaliste
        demo_text = """
        Bonjour et bienvenue dans cette édition spéciale. 
        Je suis votre présentateur virtuel, créé avec la technologie SkyReels-A1.
        Aujourd'hui, nous explorons les dernières innovations en intelligence artificielle.
        Cette démonstration montre les capacités d'animation de portraits en temps réel.
        Merci de votre attention et à bientôt pour de nouvelles actualités.
        """
        
        # Générer l'audio
        tts = gTTS(text=demo_text.strip(), lang='fr', slow=False)
        
        # Sauvegarder
        audio_path = Path(output_dir) / f"avatar_audio_{int(time.time())}.mp3"
        audio_path.parent.mkdir(parents=True, exist_ok=True)
        
        tts.save(str(audio_path))
        logger.success(f"✅ Audio créé: {audio_path}")
        
        return str(audio_path)
        
    except ImportError:
        logger.warning("⚠️ gTTS non disponible, audio non créé")
        return None
    except Exception as e:
        logger.error(f"❌ Erreur création audio: {e}")
        return None

def add_audio_to_video(video_path: str, audio_path: str) -> str:
    """Ajoute l'audio à la vidéo"""
    if not audio_path or not Path(audio_path).exists():
        logger.warning("⚠️ Pas d'audio à ajouter")
        return video_path
    
    logger.info("🎵 Ajout de l'audio à la vidéo...")
    
    # Chemin de sortie
    output_path = video_path.replace('.mp4', '_with_audio.mp4')
    
    # Commande FFmpeg pour ajouter l'audio
    ffmpeg_cmd = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-i', audio_path,
        '-c:v', 'copy',
        '-c:a', 'aac',
        '-shortest',
        output_path
    ]
    
    try:
        result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.success(f"✅ Vidéo avec audio créée: {output_path}")
            return output_path
        else:
            logger.error(f"❌ Erreur ajout audio: {result.stderr}")
            return video_path
            
    except Exception as e:
        logger.error(f"❌ Erreur: {e}")
        return video_path

def main():
    """Fonction principale"""
    print("🎭 SkyReels-A1 Avatar Creator - Version Démonstration")
    print("=" * 60)
    print("🎯 Création d'avatar parlant à partir de votre image")
    print("📱 Compatible macOS, sans dépendances complexes")
    print()
    
    # Chemin de l'image avatar
    image_path = "/Users/<USER>/Documents/aicreation/assets/music/le-journaliste-parle-du-segment-d-actualite.jpg"
    
    if not Path(image_path).exists():
        logger.error(f"❌ Image non trouvée: {image_path}")
        print("📸 Veuillez placer votre image avatar dans le bon répertoire")
        return
    
    logger.info(f"📸 Image avatar trouvée: {Path(image_path).name}")
    
    try:
        # Créer l'audio de démonstration
        audio_path = create_audio_for_avatar("outputs")
        
        # Créer la vidéo avatar
        video_path = create_talking_avatar_demo(image_path, "outputs")
        
        if video_path:
            # Ajouter l'audio si disponible
            if audio_path:
                final_video = add_audio_to_video(video_path, audio_path)
            else:
                final_video = video_path
            
            print("\n🎉 AVATAR CRÉÉ AVEC SUCCÈS!")
            print("=" * 50)
            print(f"📁 Vidéo finale: {final_video}")
            print(f"⏱️ Durée: 8 secondes")
            print(f"🎭 Avatar: Journaliste parlant")
            print(f"🎤 Audio: Présentation en français")
            print(f"📐 Format: 512x512, 12 FPS")
            
            print("\n📊 Caractéristiques:")
            print("   🎬 Animation de bouche synchronisée")
            print("   👁️ Clignements d'yeux naturels")
            print("   🎯 Micro-mouvements de tête")
            print("   💡 Effets de luminosité réalistes")
            
            print("\n🔄 Prochaines étapes:")
            print("   1. Visionner la vidéo générée")
            print("   2. Tester avec vos propres images")
            print("   3. Intégrer dans le pipeline principal")
            print("   4. Attendre SkyReels-A1 complet")
            
        else:
            print("❌ ÉCHEC - Impossible de créer l'avatar")
            print("🔧 Vérifiez l'installation de FFmpeg")
            
    except KeyboardInterrupt:
        print("\n👋 Arrêt demandé par l'utilisateur")
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")

if __name__ == "__main__":
    main()
