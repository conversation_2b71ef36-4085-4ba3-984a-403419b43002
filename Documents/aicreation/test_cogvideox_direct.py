#!/usr/bin/env python3
"""
Test direct du modèle CogVideoX pour génération d'images/vidéos
Test avec le bon pipeline selon model_index.json
"""

import os
import sys
import time
import traceback
from pathlib import Path
import numpy as np
from PIL import Image
import torch

def test_cogvideox_direct():
    """Test direct du modèle CogVideoX"""
    print("🚀 Test Direct CogVideoX")
    print("=" * 50)
    
    try:
        # Import des modules nécessaires
        from diffusers import CogVideoXImageToVideoPipeline
        print("✅ Import CogVideoXImageToVideoPipeline réussi")
        
        # Chemin vers les modèles
        model_path = "./pretrained_models/SkyReels-A1-5B"
        print(f"📁 Chargement depuis: {model_path}")
        
        # Configuration pour macOS (CPU)
        device = "cpu"  # Force CPU pour macOS
        torch_dtype = torch.float32  # Utiliser float32 sur CPU
        
        print(f"💻 Device: {device}")
        print(f"🔢 Dtype: {torch_dtype}")
        
        # Chargement du pipeline
        print("🔧 Chargement du pipeline...")
        start_time = time.time()
        
        pipeline = CogVideoXImageToVideoPipeline.from_pretrained(
            model_path,
            torch_dtype=torch_dtype,
            device_map=None,  # Pas de device_map sur CPU
            low_cpu_mem_usage=True,
            use_safetensors=True
        )
        
        # Déplacer vers CPU
        pipeline = pipeline.to(device)
        
        # Optimisations mémoire
        pipeline.enable_attention_slicing()
        pipeline.enable_vae_slicing()
        
        load_time = time.time() - start_time
        print(f"✅ Pipeline chargé en {load_time:.2f}s")
        
        # Test de génération simple
        print("\n🎨 Test de génération...")
        
        # Créer une image de base simple pour le test
        test_image = Image.new('RGB', (256, 256), color=(100, 150, 200))
        prompt = "beautiful landscape, high quality, cinematic"
        
        print(f"📝 Prompt: {prompt}")
        print(f"🖼️ Image d'entrée: {test_image.size}")
        
        # Génération avec paramètres réduits
        start_time = time.time()
        
        with torch.no_grad():
            result = pipeline(
                image=test_image,
                prompt=prompt,
                num_inference_steps=10,  # Très réduit pour test
                guidance_scale=6.0,
                num_videos_per_prompt=1,
                generator=torch.Generator(device=device).manual_seed(42)
            )
        
        generation_time = time.time() - start_time
        print(f"⏱️ Génération en {generation_time:.2f}s")
        
        # Vérification du résultat
        if hasattr(result, 'frames') and result.frames is not None:
            frames = result.frames[0]  # Premier (et seul) batch
            print(f"✅ Génération réussie!")
            print(f"📹 Nombre de frames: {len(frames)}")
            print(f"📐 Taille frame: {frames[0].size if frames else 'N/A'}")
            
            # Sauvegarde de la première frame comme test
            if frames:
                output_dir = Path("output/test_images")
                output_dir.mkdir(parents=True, exist_ok=True)
                
                first_frame = frames[0]
                output_path = output_dir / f"cogvideox_test_{int(time.time())}.png"
                first_frame.save(output_path)
                print(f"💾 Première frame sauvegardée: {output_path}")
                
                return True
        else:
            print("❌ Aucun résultat généré")
            return False
            
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        print("💡 Installez avec: pip install diffusers[cogvideox]")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("📋 Traceback:")
        traceback.print_exc()
        return False

def test_simple_image_generation():
    """Test de génération d'image simple avec Stable Diffusion"""
    print("\n🎨 Test Génération Image Simple")
    print("=" * 40)
    
    try:
        from diffusers import StableDiffusionPipeline
        print("✅ Import StableDiffusionPipeline réussi")
        
        # Utiliser un modèle Stable Diffusion standard
        model_id = "runwayml/stable-diffusion-v1-5"
        device = "cpu"
        
        print(f"📥 Téléchargement du modèle: {model_id}")
        
        pipeline = StableDiffusionPipeline.from_pretrained(
            model_id,
            torch_dtype=torch.float32,
            use_safetensors=True,
            low_cpu_mem_usage=True
        )
        pipeline = pipeline.to(device)
        
        # Optimisations
        pipeline.enable_attention_slicing()
        pipeline.enable_vae_slicing()
        
        print("✅ Pipeline Stable Diffusion chargé")
        
        # Génération de test
        prompt = "beautiful landscape, high quality, cinematic"
        print(f"📝 Prompt: {prompt}")
        
        with torch.no_grad():
            result = pipeline(
                prompt=prompt,
                num_inference_steps=10,
                guidance_scale=7.5,
                width=256,
                height=256,
                generator=torch.Generator(device=device).manual_seed(42)
            )
        
        if result.images:
            image = result.images[0]
            output_dir = Path("output/test_images")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            output_path = output_dir / f"sd_test_{int(time.time())}.png"
            image.save(output_path)
            print(f"✅ Image générée: {output_path}")
            return True
        else:
            print("❌ Aucune image générée")
            return False
            
    except Exception as e:
        print(f"❌ Erreur Stable Diffusion: {e}")
        return False

def main():
    """Test principal"""
    print("🎬 Test Génération IA - SkyReels/CogVideoX")
    print("=" * 60)
    
    # Test 1: CogVideoX direct
    success1 = test_cogvideox_direct()
    
    # Test 2: Stable Diffusion simple (fallback)
    if not success1:
        print("\n🔄 Fallback vers Stable Diffusion...")
        success2 = test_simple_image_generation()
    else:
        success2 = True
    
    print("\n" + "=" * 60)
    if success1:
        print("🎉 CogVideoX fonctionne!")
        print("✅ Génération vidéo/image IA opérationnelle")
    elif success2:
        print("⚠️ CogVideoX non compatible, mais Stable Diffusion fonctionne")
        print("💡 Utilisez Stable Diffusion comme alternative")
    else:
        print("❌ Aucun système de génération IA fonctionnel")
        print("🔧 Vérifiez l'installation des dépendances")
    
    print("\n📋 Recommandations:")
    if success1:
        print("   1. Intégrer CogVideoX dans le système vidéo")
        print("   2. Optimiser les paramètres de génération")
    else:
        print("   1. Utiliser Stable Diffusion pour les images")
        print("   2. Considérer d'autres modèles compatibles")
    print("   3. Tester avec différents prompts")

if __name__ == "__main__":
    main()
