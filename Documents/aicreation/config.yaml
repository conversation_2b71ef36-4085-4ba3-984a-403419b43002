# Configuration Enhanced Video Creator v2.0
# Système de création vidéo professionnel avec IA

# ===== MODÈLES IA =====
models:
  # SkyReels pour génération d'images
  skyreels:
    base_path: "./pretrained_models/SkyReels-A1-5B"
    siglip: "${base_path}/siglip-so400m-patch14-384"
    text_encoder: "${base_path}/text_encoder"
    transformer: "${base_path}/transformer"
    pose_guider: "${base_path}/pose_guider"
    vae: "${base_path}/vae"
    enabled: true

  # Piper TTS local
  piper:
    executable_path: "./piper/piper"
    models_dir: "./piper/models"
    default_voice: "en_US-lessac-medium"
    enabled: true

  # Ollama pour génération de scripts
  ollama:
    base_url: "http://localhost:11434"
    model: "llama2"
    timeout: 60
    enabled: true

# ===== PERFORMANCE =====
performance:
  # GPU/CPU
  use_gpu: false  # macOS compatibility
  max_workers: 2
  memory_limit: 6144  # 6GB

  # Optimisations IA
  use_fp16: true
  offload_to_cpu: true
  use_attention_slicing: true
  use_vae_slicing: true
  use_xformers: false

  # Cache et retry
  enable_cache: true
  cache_ttl: 3600  # 1 heure
  max_retries: 3
  retry_delay: 2.0

# ===== APIS EXTERNES =====
apis:
  # Pixabay pour vidéos
  pixabay:
    api_key: "${PIXABAY_API_KEY}"
    base_url: "https://pixabay.com/api/videos/"
    timeout: 30
    max_downloads: 10
    enabled: true

  # Pexels (alternatif)
  pexels:
    api_key: "${PEXELS_API_KEY}"
    base_url: "https://api.pexels.com/videos/"
    enabled: false

  # Free Music Archive
  freemusicarchive:
    enabled: true
    cache_dir: "./assets/music"

# ===== FORMATS VIDÉO =====
video_formats:
  youtube:
    width: 1920
    height: 1080
    fps: 30
    aspect_ratio: "16:9"
    max_duration: 600  # 10 minutes

  tiktok:
    width: 1080
    height: 1920
    fps: 30
    aspect_ratio: "9:16"
    max_duration: 60  # 1 minute

  instagram:
    width: 1080
    height: 1080
    fps: 30
    aspect_ratio: "1:1"
    max_duration: 60

  youtube_short:
    width: 1080
    height: 1920
    fps: 30
    aspect_ratio: "9:16"
    max_duration: 60

# ===== AUDIO =====
audio:
  # TTS Configuration
  tts:
    primary_provider: "piper"
    fallback_provider: "gtts"
    voice_gender: "female"  # male, female, neutral
    speed: 1.0
    volume: 0.8

  # Musique
  music:
    enabled: true
    volume: 0.3
    fade_in: 2.0
    fade_out: 2.0
    sources: ["freemusicarchive", "generated"]

  # Qualité audio
  quality:
    sample_rate: 44100
    bitrate: "192k"
    format: "mp3"
    normalize: true

# ===== SOUS-TITRES =====
subtitles:
  enabled: true
  style: "modern"  # classic, modern, bold, minimal
  font_size: 24
  font_color: "white"
  outline_color: "black"
  outline_width: 2
  position: "bottom"  # top, center, bottom
  max_chars_per_line: 40
  max_lines: 2

  # Timing automatique
  auto_timing: true
  words_per_second: 2.5
  min_duration: 1.0
  max_duration: 6.0

# ===== EFFETS VISUELS =====
effects:
  # Transitions
  transitions:
    enabled: true
    type: "fade"  # fade, slide, zoom, dissolve
    duration: 0.5

  # Texte animé
  animated_text:
    enabled: true
    style: "typewriter"  # typewriter, fade, slide
    speed: 0.1

  # Filtres
  filters:
    color_correction: true
    brightness: 1.0
    contrast: 1.0
    saturation: 1.1

# ===== GÉNÉRATION DE CONTENU =====
content_generation:
  # Scripts
  scripts:
    style: "engaging"  # informative, engaging, educational
    target_duration: 60
    include_hook: true
    include_cta: true
    max_points: 5

  # Prompts IA
  prompts:
    image_style: "professional, high quality, cinematic"
    negative_prompt: "blurry, low quality, distorted"
    creativity: 0.7  # 0.0 à 1.0

  # Hashtags
  hashtags:
    auto_generate: true
    max_count: 10
    trending_boost: true

# ===== SORTIE =====
output:
  # Répertoires
  directories:
    base: "./output"
    videos: "${base}/videos"
    audio: "${base}/audio"
    thumbnails: "${base}/thumbnails"
    subtitles: "${base}/subtitles"
    scripts: "${base}/scripts"
    temp: "${base}/temp"
    cache: "${base}/cache"

  # Qualité
  quality:
    video_codec: "libx264"
    audio_codec: "aac"
    preset: "medium"  # ultrafast, fast, medium, slow
    crf: 23  # 0-51, plus bas = meilleure qualité

  # Métadonnées
  metadata:
    include_title: true
    include_description: true
    include_tags: true
    include_timestamp: true

# ===== LOGGING =====
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "detailed"  # simple, detailed, json

  # Fichiers
  files:
    main: "./logs/enhanced_video_creator.log"
    errors: "./logs/errors.log"
    performance: "./logs/performance.log"

  # Rotation
  rotation:
    max_size: "10 MB"
    backup_count: 7

  # Console
  console:
    enabled: true
    colors: true
    level: "INFO"

# ===== MONITORING =====
monitoring:
  # Métriques
  metrics:
    enabled: true
    export_interval: 60  # secondes

  # Alertes
  alerts:
    memory_threshold: 80  # %
    disk_threshold: 90   # %
    error_threshold: 5   # erreurs/minute

  # Santé système
  health_checks:
    interval: 30  # secondes
    timeout: 10   # secondes

# ===== SÉCURITÉ =====
security:
  # Validation des entrées
  input_validation:
    max_keyword_length: 100
    allowed_characters: "alphanumeric_spaces"

  # Limites de ressources
  resource_limits:
    max_video_duration: 600  # secondes
    max_file_size: 100  # MB
    max_concurrent_jobs: 3

  # API rate limiting
  rate_limiting:
    requests_per_minute: 60
    burst_limit: 10

# ===== DÉVELOPPEMENT =====
development:
  debug_mode: false
  verbose_logging: false
  save_intermediate_files: false
  skip_cleanup: false

  # Tests
  testing:
    mock_apis: false
    fast_generation: false
    small_models: false
