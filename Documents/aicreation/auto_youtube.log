2025-05-19 22:31:51,152 - __main__ - WARNING - pytrends module not found. Trending keyword functionality will be limited.
2025-05-19 22:31:51,201 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 22:31:52,202 - __main__ - INFO - Available modules:
2025-05-19 22:31:52,202 - __main__ - INFO -   pytrends: Not available
2025-05-19 22:31:52,202 - __main__ - INFO -   ollama: Not available
2025-05-19 22:31:52,202 - __main__ - INFO -   gtts: Available
2025-05-19 22:31:52,202 - __main__ - INFO -   PIL: Available
2025-05-19 22:31:52,202 - __main__ - INFO -   google.oauth2: Available
2025-05-19 22:31:52,202 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 22:31:52,202 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 22:31:57,872 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 22:32:01,644 - __main__ - INFO - Created video for 'history facts'
2025-05-19 22:32:01,740 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 22:32:01,741 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 22:32:01,741 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 22:33:01,743 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 22:33:07,381 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 22:33:09,516 - __main__ - INFO - Created video for 'economic trends'
2025-05-19 22:33:09,558 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 22:33:09,558 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 22:33:09,558 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 22:34:09,563 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 22:34:15,310 - __main__ - INFO - Generated audio for 'current events'
2025-05-19 22:34:17,551 - __main__ - INFO - Created video for 'current events'
2025-05-19 22:34:17,592 - __main__ - INFO - Created thumbnail for 'current events'
2025-05-19 22:34:17,592 - __main__ - ERROR - Error processing keyword 'current events': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 22:34:17,592 - __main__ - WARNING - Failed to process 'current events'
2025-05-19 22:35:17,597 - __main__ - INFO - Completed processing all keywords. Waiting for 24 hours...
2025-05-19 22:35:42,983 - __main__ - INFO - Process interrupted by user. Exiting...
2025-05-19 23:00:22,812 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:00:23,474 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:00:24,274 - __main__ - INFO - Available modules:
2025-05-19 23:00:24,274 - __main__ - INFO -   pytrends: Available
2025-05-19 23:00:24,275 - __main__ - INFO -   ollama: Not available
2025-05-19 23:00:24,275 - __main__ - INFO -   gtts: Available
2025-05-19 23:00:24,275 - __main__ - INFO -   PIL: Available
2025-05-19 23:00:24,275 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:00:24,590 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:00:24,592 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:00:24,592 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:00:30,063 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:00:32,652 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:00:32,720 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:00:32,720 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:00:32,720 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:01:34,867 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:01:35,248 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:01:35,918 - __main__ - INFO - Available modules:
2025-05-19 23:01:35,918 - __main__ - INFO -   pytrends: Available
2025-05-19 23:01:35,918 - __main__ - INFO -   ollama: Not available
2025-05-19 23:01:35,918 - __main__ - INFO -   gtts: Available
2025-05-19 23:01:35,918 - __main__ - INFO -   PIL: Available
2025-05-19 23:01:35,918 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:01:36,236 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:01:36,239 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:01:36,239 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:01:41,027 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:01:41,910 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:01:41,955 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:01:41,956 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:01:41,956 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:01:41,956 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:02:42,007 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:02:42,341 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:02:43,076 - __main__ - INFO - Available modules:
2025-05-19 23:02:43,076 - __main__ - INFO -   pytrends: Available
2025-05-19 23:02:43,076 - __main__ - INFO -   ollama: Not available
2025-05-19 23:02:43,076 - __main__ - INFO -   gtts: Available
2025-05-19 23:02:43,076 - __main__ - INFO -   PIL: Available
2025-05-19 23:02:43,076 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:02:43,398 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:02:43,403 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:02:43,403 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:02:43,403 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:02:48,425 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:02:50,605 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:02:50,668 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:02:50,668 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:02:50,668 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:02:50,668 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:03:50,673 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:03:50,675 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:03:55,927 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 23:03:57,174 - __main__ - INFO - Created video for 'economic trends'
2025-05-19 23:03:57,210 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 23:03:57,210 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:03:57,210 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 23:03:57,210 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:04:57,216 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:04:57,217 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:05:02,504 - __main__ - INFO - Generated audio for 'current events'
2025-05-19 23:05:05,140 - __main__ - INFO - Created video for 'current events'
2025-05-19 23:05:05,182 - __main__ - INFO - Created thumbnail for 'current events'
2025-05-19 23:05:05,183 - __main__ - ERROR - Error processing keyword 'current events': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:05:05,183 - __main__ - WARNING - Failed to process 'current events'
2025-05-19 23:05:05,183 - __main__ - INFO - Completed processing all keywords.
2025-05-19 23:07:04,757 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:07:05,096 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:07:05,906 - __main__ - INFO - Available modules:
2025-05-19 23:07:05,907 - __main__ - INFO -   pytrends: Available
2025-05-19 23:07:05,907 - __main__ - INFO -   ollama: Not available
2025-05-19 23:07:05,907 - __main__ - INFO -   gtts: Available
2025-05-19 23:07:05,907 - __main__ - INFO -   PIL: Available
2025-05-19 23:07:05,907 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:07:06,222 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:07:06,228 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:07:06,229 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:07:06,229 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:07:10,882 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:07:15,174 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:07:15,247 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:07:15,247 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:07:15,247 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:07:15,247 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:09:02,681 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:09:03,046 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:09:03,833 - __main__ - INFO - Available modules:
2025-05-19 23:09:03,833 - __main__ - INFO -   pytrends: Available
2025-05-19 23:09:03,833 - __main__ - INFO -   ollama: Not available
2025-05-19 23:09:03,833 - __main__ - INFO -   gtts: Available
2025-05-19 23:09:03,833 - __main__ - INFO -   PIL: Available
2025-05-19 23:09:03,833 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:09:04,144 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:09:04,146 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:09:04,146 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:09:04,146 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:09:04,146 - __main__ - WARNING - Ollama not available. Using fallback script generation.
2025-05-19 23:09:08,741 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:09:10,301 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:09:10,366 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:09:10,367 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:09:10,367 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:09:10,367 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:09:57,955 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:09:58,315 - __main__ - WARNING - ollama module not found. Will try direct HTTP API instead.
2025-05-19 23:09:59,089 - __main__ - INFO - Available modules:
2025-05-19 23:09:59,089 - __main__ - INFO -   pytrends: Available
2025-05-19 23:09:59,089 - __main__ - INFO -   ollama: Available
2025-05-19 23:09:59,089 - __main__ - INFO -   gtts: Available
2025-05-19 23:09:59,090 - __main__ - INFO -   PIL: Available
2025-05-19 23:09:59,090 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:09:59,406 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:09:59,409 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:09:59,409 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:09:59,409 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:09:59,409 - __main__ - INFO - Generating script with Ollama model 'phi3'
2025-05-19 23:09:59,409 - __main__ - INFO - Using direct HTTP API call to Ollama
2025-05-19 23:10:10,407 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:10:10,437 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:10:10,441 - __main__ - WARNING - Ollama not available. Using fallback script generation.
2025-05-19 23:10:29,648 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 23:10:47,883 - __main__ - INFO - Created video for 'economic trends'
2025-05-19 23:10:48,014 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 23:10:48,020 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:10:48,028 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 23:10:48,028 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:11:22,378 - __main__ - ERROR - Ollama API error: Extra data: line 2 column 1 (char 120)
2025-05-19 23:11:22,384 - __main__ - ERROR - Error generating script for 'history facts': Extra data: line 2 column 1 (char 120)
2025-05-19 23:11:27,724 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:11:29,463 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:11:29,564 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:11:29,564 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:11:29,564 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:11:29,564 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:11:48,031 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:11:48,034 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:11:48,034 - __main__ - WARNING - Ollama not available. Using fallback script generation.
2025-05-19 23:11:53,376 - __main__ - INFO - Generated audio for 'current events'
2025-05-19 23:11:55,246 - __main__ - INFO - Created video for 'current events'
2025-05-19 23:11:55,283 - __main__ - INFO - Created thumbnail for 'current events'
2025-05-19 23:11:55,284 - __main__ - ERROR - Error processing keyword 'current events': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:11:55,284 - __main__ - WARNING - Failed to process 'current events'
2025-05-19 23:11:55,284 - __main__ - INFO - Completed processing all keywords.
