"""
Interfaces for the video creation system.

This module defines abstract base classes that establish contracts for different
components of the system, enabling dependency injection and testability.
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any, AsyncIterator
from pathlib import Path

from .models import (
    VideoRequest, VideoResponse, ScriptData, 
    AudioConfig, VideoConfig
)


class ITTSProvider(ABC):
    """Interface for Text-to-Speech providers."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Provider name."""
        pass
    
    @property
    @abstractmethod
    def is_available(self) -> bool:
        """Check if provider is available."""
        pass
    
    @abstractmethod
    async def synthesize(
        self, 
        text: str, 
        config: AudioConfig,
        output_path: Path
    ) -> bool:
        """Synthesize text to speech."""
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        pass
    
    @abstractmethod
    def estimate_duration(self, text: str) -> float:
        """Estimate audio duration in seconds."""
        pass


class IVideoProvider(ABC):
    """Interface for video content providers."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Provider name."""
        pass
    
    @property
    @abstractmethod
    def is_available(self) -> bool:
        """Check if provider is available."""
        pass
    
    @abstractmethod
    async def search_videos(
        self, 
        keyword: str, 
        category: Optional[str] = None,
        count: int = 5
    ) -> List[Dict[str, Any]]:
        """Search for videos."""
        pass
    
    @abstractmethod
    async def download_video(
        self, 
        video_info: Dict[str, Any], 
        output_path: Path
    ) -> bool:
        """Download a video."""
        pass
    
    @abstractmethod
    def get_supported_categories(self) -> List[str]:
        """Get list of supported categories."""
        pass


class IScriptGenerator(ABC):
    """Interface for script generators."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Generator name."""
        pass
    
    @property
    @abstractmethod
    def is_available(self) -> bool:
        """Check if generator is available."""
        pass
    
    @abstractmethod
    async def generate_script(
        self, 
        keyword: str, 
        target_duration: Optional[float] = None
    ) -> ScriptData:
        """Generate a script for the given keyword."""
        pass
    
    @abstractmethod
    def get_supported_models(self) -> List[str]:
        """Get list of supported models."""
        pass


class IAudioProcessor(ABC):
    """Interface for audio processing."""
    
    @abstractmethod
    async def enhance_audio(
        self, 
        input_path: Path, 
        output_path: Path,
        config: AudioConfig
    ) -> bool:
        """Enhance audio quality."""
        pass
    
    @abstractmethod
    async def add_background_music(
        self, 
        speech_path: Path, 
        music_path: Path,
        output_path: Path,
        music_volume: float = 0.3
    ) -> bool:
        """Add background music to speech."""
        pass
    
    @abstractmethod
    async def normalize_audio(
        self, 
        input_path: Path, 
        output_path: Path
    ) -> bool:
        """Normalize audio levels."""
        pass


class IVideoProcessor(ABC):
    """Interface for video processing."""
    
    @abstractmethod
    async def create_video(
        self, 
        video_clips: List[Path],
        audio_path: Path,
        output_path: Path,
        config: VideoConfig
    ) -> bool:
        """Create video from clips and audio."""
        pass
    
    @abstractmethod
    async def add_transitions(
        self, 
        video_path: Path, 
        output_path: Path,
        transition_type: str = "fade"
    ) -> bool:
        """Add transitions between video segments."""
        pass
    
    @abstractmethod
    async def create_thumbnail(
        self, 
        video_path: Path, 
        output_path: Path,
        timestamp: float = 0.0
    ) -> bool:
        """Create thumbnail from video."""
        pass


class IMusicGenerator(ABC):
    """Interface for music generation."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Generator name."""
        pass
    
    @abstractmethod
    async def generate_music(
        self, 
        style: str,
        duration: float,
        with_vocals: bool = False,
        output_path: Optional[Path] = None
    ) -> Optional[Path]:
        """Generate background music."""
        pass
    
    @abstractmethod
    def get_supported_styles(self) -> List[str]:
        """Get list of supported music styles."""
        pass


class ICache(ABC):
    """Interface for caching system."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """Set value in cache."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache."""
        pass


class IMetrics(ABC):
    """Interface for metrics collection."""
    
    @abstractmethod
    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None):
        """Increment a counter metric."""
        pass
    
    @abstractmethod
    def record_histogram(
        self, 
        name: str, 
        value: float, 
        tags: Optional[Dict[str, str]] = None
    ):
        """Record a histogram metric."""
        pass
    
    @abstractmethod
    def record_gauge(
        self, 
        name: str, 
        value: float, 
        tags: Optional[Dict[str, str]] = None
    ):
        """Record a gauge metric."""
        pass


class ILogger(ABC):
    """Interface for structured logging."""
    
    @abstractmethod
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        pass
    
    @abstractmethod
    def info(self, message: str, **kwargs):
        """Log info message."""
        pass
    
    @abstractmethod
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        pass
    
    @abstractmethod
    def error(self, message: str, **kwargs):
        """Log error message."""
        pass
    
    @abstractmethod
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        pass


class ICircuitBreaker(ABC):
    """Interface for circuit breaker pattern."""
    
    @abstractmethod
    async def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        pass
    
    @property
    @abstractmethod
    def state(self) -> str:
        """Get current circuit breaker state."""
        pass
    
    @abstractmethod
    def reset(self):
        """Reset circuit breaker."""
        pass


class IRateLimiter(ABC):
    """Interface for rate limiting."""
    
    @abstractmethod
    async def acquire(self, key: str) -> bool:
        """Acquire rate limit token."""
        pass
    
    @abstractmethod
    async def get_remaining(self, key: str) -> int:
        """Get remaining tokens."""
        pass
    
    @abstractmethod
    async def reset_limit(self, key: str):
        """Reset rate limit for key."""
        pass


class IVideoCreationService(ABC):
    """Main interface for video creation service."""
    
    @abstractmethod
    async def create_video(self, request: VideoRequest) -> VideoResponse:
        """Create video from request."""
        pass
    
    @abstractmethod
    async def get_video_status(self, video_id: str) -> Dict[str, Any]:
        """Get video creation status."""
        pass
    
    @abstractmethod
    async def cancel_video_creation(self, video_id: str) -> bool:
        """Cancel video creation."""
        pass
