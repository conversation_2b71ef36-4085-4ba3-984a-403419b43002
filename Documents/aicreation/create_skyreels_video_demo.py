#!/usr/bin/env python3
"""
Création d'une vidéo de démonstration SkyReels-A1 de 8 secondes
Solution alternative en attendant la compatibilité CogVideoX
"""

import os
import sys
import time
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import moviepy.editor as mpy

def create_skyreels_demo_video():
    """Crée une vidéo de démonstration SkyReels-A1 de 8 secondes"""
    print("🎬 Création Vidéo Démonstration SkyReels-A1")
    print("=" * 50)
    print("⏱️ Durée: 8 secondes")
    print("🎞️ FPS: 24 (192 frames)")
    print("📐 Résolution: 512x512")
    print()
    
    try:
        # Paramètres de la vidéo
        duration = 8  # secondes
        fps = 24
        total_frames = duration * fps  # 192 frames
        width, height = 512, 512
        
        print(f"🎯 Génération de {total_frames} frames...")
        
        # Créer les frames avec animation
        frames = []
        
        for frame_num in range(total_frames):
            # Progression de 0 à 1
            progress = frame_num / total_frames
            
            # Créer une frame avec dégradé animé
            frame = create_animated_frame(frame_num, progress, width, height)
            frames.append(frame)
            
            if frame_num % 24 == 0:  # Affichage toutes les secondes
                print(f"   📹 Frame {frame_num}/{total_frames} ({frame_num//24}s)")
        
        print("✅ Toutes les frames générées")
        
        # Créer la vidéo avec MoviePy
        print("🎬 Assemblage de la vidéo...")
        
        # Convertir les frames en clips
        clips = []
        for i, frame in enumerate(frames):
            # Convertir PIL en array numpy
            frame_array = np.array(frame)
            
            # Créer un clip d'une frame
            clip = mpy.ImageClip(frame_array, duration=1/fps)
            clips.append(clip)
        
        # Concaténer tous les clips
        final_video = mpy.concatenate_videoclips(clips, method="compose")
        
        # Ajouter un audio de démonstration (optionnel)
        # audio = mpy.AudioFileClip("demo_audio.mp3").subclip(0, duration)
        # final_video = final_video.set_audio(audio)
        
        # Chemin de sortie
        output_dir = Path("output/test_videos")
        output_dir.mkdir(parents=True, exist_ok=True)
        video_path = output_dir / f"skyreels_demo_{int(time.time())}.mp4"
        
        # Exporter la vidéo
        print(f"💾 Export vers: {video_path}")
        final_video.write_videofile(
            str(video_path),
            fps=fps,
            codec='libx264',
            audio=False,
            preset='medium',
            verbose=False,
            logger=None
        )
        
        # Informations sur le fichier créé
        file_size = video_path.stat().st_size / (1024 * 1024)
        print(f"✅ Vidéo créée avec succès!")
        print(f"📁 Fichier: {video_path}")
        print(f"📦 Taille: {file_size:.1f} MB")
        print(f"⏱️ Durée: {duration} secondes")
        print(f"🎞️ FPS: {fps}")
        print(f"📐 Résolution: {width}x{height}")
        
        # Nettoyage
        final_video.close()
        for clip in clips:
            clip.close()
        
        return str(video_path)
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_animated_frame(frame_num, progress, width, height):
    """Crée une frame animée avec effets visuels"""
    
    # Couleurs qui évoluent dans le temps
    r = int(50 + 100 * np.sin(progress * 2 * np.pi))
    g = int(100 + 80 * np.cos(progress * 3 * np.pi))
    b = int(150 + 50 * np.sin(progress * 4 * np.pi))
    
    # Créer l'image de base avec dégradé
    img = Image.new('RGB', (width, height))
    
    # Créer un dégradé radial animé
    for y in range(height):
        for x in range(width):
            # Distance du centre
            center_x, center_y = width // 2, height // 2
            distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
            max_distance = np.sqrt(center_x**2 + center_y**2)
            
            # Normaliser la distance
            norm_distance = distance / max_distance
            
            # Ajouter animation temporelle
            wave = np.sin(progress * 6 * np.pi + norm_distance * 4 * np.pi)
            
            # Calculer les couleurs
            final_r = max(0, min(255, int(r + wave * 50)))
            final_g = max(0, min(255, int(g + wave * 40)))
            final_b = max(0, min(255, int(b + wave * 30)))
            
            img.putpixel((x, y), (final_r, final_g, final_b))
    
    # Ajouter des effets
    if progress > 0.2:
        # Flou gaussien léger
        img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
    
    if progress > 0.5:
        # Améliorer le contraste
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.2)
    
    # Ajouter du texte
    draw = ImageDraw.Draw(img)
    
    try:
        font_large = ImageFont.truetype("Arial.ttf", 48)
        font_small = ImageFont.truetype("Arial.ttf", 24)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Texte principal
    main_text = "SkyReels-A1"
    text_bbox = draw.textbbox((0, 0), main_text, font=font_large)
    text_width = text_bbox[2] - text_bbox[0]
    text_x = (width - text_width) // 2
    text_y = height // 2 - 60
    
    # Ombre du texte
    draw.text((text_x + 2, text_y + 2), main_text, fill=(0, 0, 0), font=font_large)
    # Texte principal
    draw.text((text_x, text_y), main_text, fill=(255, 255, 255), font=font_large)
    
    # Sous-texte avec timing
    sub_text = f"Démonstration Vidéo • {progress*8:.1f}s"
    sub_bbox = draw.textbbox((0, 0), sub_text, font=font_small)
    sub_width = sub_bbox[2] - sub_bbox[0]
    sub_x = (width - sub_width) // 2
    sub_y = text_y + 80
    
    draw.text((sub_x + 1, sub_y + 1), sub_text, fill=(0, 0, 0), font=font_small)
    draw.text((sub_x, sub_y), sub_text, fill=(200, 200, 200), font=font_small)
    
    # Barre de progression
    bar_width = 300
    bar_height = 8
    bar_x = (width - bar_width) // 2
    bar_y = height - 80
    
    # Fond de la barre
    draw.rectangle([bar_x, bar_y, bar_x + bar_width, bar_y + bar_height], 
                  fill=(50, 50, 50), outline=(100, 100, 100))
    
    # Progression
    progress_width = int(bar_width * progress)
    if progress_width > 0:
        draw.rectangle([bar_x, bar_y, bar_x + progress_width, bar_y + bar_height], 
                      fill=(0, 255, 100))
    
    # Ajouter des particules animées
    num_particles = 20
    for i in range(num_particles):
        # Position des particules qui bougent
        particle_progress = (progress + i / num_particles) % 1.0
        px = int(particle_progress * width)
        py = int(50 + i * 20 + 30 * np.sin(progress * 4 * np.pi + i))
        
        # Taille variable
        size = int(3 + 2 * np.sin(progress * 8 * np.pi + i))
        
        # Couleur variable
        particle_color = (
            int(255 * particle_progress),
            int(255 * (1 - particle_progress)),
            200
        )
        
        if 0 <= px < width and 0 <= py < height:
            draw.ellipse([px-size, py-size, px+size, py+size], 
                        fill=particle_color)
    
    return img

def create_stable_diffusion_video():
    """Alternative: créer une vidéo avec Stable Diffusion"""
    print("\n🔄 Alternative: Vidéo avec Stable Diffusion...")
    
    try:
        from diffusers import StableDiffusionPipeline
        import torch
        
        # Charger le pipeline
        pipeline = StableDiffusionPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5",
            torch_dtype=torch.float32,
            use_safetensors=True
        )
        pipeline = pipeline.to("cpu")
        pipeline.enable_attention_slicing()
        
        print("✅ Pipeline Stable Diffusion chargé")
        
        # Générer plusieurs images pour créer une vidéo
        prompts = [
            "beautiful landscape at sunrise, cinematic",
            "beautiful landscape at morning, cinematic", 
            "beautiful landscape at noon, cinematic",
            "beautiful landscape at sunset, cinematic",
            "beautiful landscape at night, cinematic"
        ]
        
        frames = []
        for i, prompt in enumerate(prompts):
            print(f"🎨 Génération image {i+1}/{len(prompts)}: {prompt}")
            
            with torch.no_grad():
                result = pipeline(
                    prompt=prompt,
                    num_inference_steps=10,
                    guidance_scale=7.5,
                    width=256,
                    height=256,
                    generator=torch.Generator().manual_seed(42 + i)
                )
            
            if result.images:
                # Dupliquer chaque image pour créer 1.6 secondes
                for _ in range(int(24 * 1.6)):  # 1.6s à 24fps
                    frames.append(np.array(result.images[0]))
        
        if frames:
            # Créer la vidéo
            clips = []
            for frame in frames:
                clip = mpy.ImageClip(frame, duration=1/24)
                clips.append(clip)
            
            final_video = mpy.concatenate_videoclips(clips)
            
            output_dir = Path("output/test_videos")
            output_dir.mkdir(parents=True, exist_ok=True)
            video_path = output_dir / f"sd_video_{int(time.time())}.mp4"
            
            final_video.write_videofile(
                str(video_path),
                fps=24,
                codec='libx264',
                audio=False,
                verbose=False,
                logger=None
            )
            
            print(f"✅ Vidéo SD créée: {video_path}")
            
            final_video.close()
            for clip in clips:
                clip.close()
            
            return str(video_path)
        
    except Exception as e:
        print(f"❌ Erreur SD: {e}")
        return None

def main():
    """Fonction principale"""
    print("🎬 CRÉATION VIDÉO SKYREELS-A1 DÉMONSTRATION")
    print("=" * 60)
    print("🎯 Objectif: Créer une vidéo de 8 secondes")
    print("⚠️ Note: Démonstration en attendant compatibilité CogVideoX")
    print()
    
    # Option 1: Vidéo de démonstration animée
    demo_video = create_skyreels_demo_video()
    
    # Option 2: Vidéo avec Stable Diffusion (si temps disponible)
    # sd_video = create_stable_diffusion_video()
    
    print("\n" + "=" * 60)
    if demo_video:
        print("🎉 SUCCÈS - Vidéo de démonstration créée!")
        print(f"📁 Fichier: {demo_video}")
        print("✅ Vidéo de 8 secondes générée")
        print("🎬 Prête pour intégration dans le système")
        
        print("\n📋 Prochaines étapes:")
        print("   1. Mettre à jour diffusers pour CogVideoX")
        print("   2. Tester avec les vrais modèles SkyReels")
        print("   3. Intégrer dans le pipeline principal")
        print("   4. Optimiser la qualité et performance")
    else:
        print("❌ ÉCHEC - Impossible de créer la vidéo")
        print("🔧 Vérifiez l'installation de MoviePy")

if __name__ == "__main__":
    main()
