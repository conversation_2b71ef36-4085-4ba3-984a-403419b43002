#!/usr/bin/env python3
"""
voice_upgrade_demo.py - Démonstration des améliorations vocales
"""

import sys
import os
from pathlib import Path

def print_voice_header():
    """Print voice upgrade header."""
    print("🎤" * 30)
    print("🔊 MISE À NIVEAU VOCALE COMPLÈTE 🔊")
    print("🎤" * 30)
    print()
    print("🚀 VOTRE SYSTÈME A ÉTÉ AMÉLIORÉ AVEC :")
    print()
    print("🌟 ElevenLabs TTS - Voix ultra-réalistes")
    print("🤖 OpenAI TTS - Excellent rapport qualité/prix")
    print("🏢 Azure TTS - Professionnel avec SSML")
    print("✨ Minimax TTS - Alternative innovante")
    print("🔄 gTTS Enhanced - Fallback amélioré")
    print()

def show_voice_comparison():
    """Show voice quality comparison."""
    print("📊 COMPARAISON DES QUALITÉS VOCALES:")
    print("=" * 50)
    print()
    
    print("🔄 AVANT (gTTS basique):")
    print("   ❌ Voix robotique et monotone")
    print("   ❌ Pas d'expression émotionnelle")
    print("   ❌ Qualité audio basique")
    print("   ❌ Une seule voix par langue")
    print()
    
    print("🌟 APRÈS (Systèmes TTS Premium):")
    print("   ✅ Voix indiscernables de voix humaines")
    print("   ✅ Expression émotionnelle naturelle")
    print("   ✅ Qualité audio professionnelle")
    print("   ✅ 300+ voix disponibles")
    print("   ✅ Clonage vocal possible")
    print("   ✅ Contrôle avancé (pitch, vitesse, style)")
    print()

def show_system_status():
    """Show current system status."""
    print("🔍 STATUT ACTUEL DU SYSTÈME:")
    print("-" * 40)
    
    # Check each TTS system
    systems_status = {}
    
    # ElevenLabs
    elevenlabs_key = os.getenv('ELEVENLABS_API_KEY')
    systems_status['elevenlabs'] = bool(elevenlabs_key)
    status = "✅ Configuré" if elevenlabs_key else "⚠️ API key manquante"
    print(f"🌟 ElevenLabs: {status}")
    
    # OpenAI
    openai_key = os.getenv('OPENAI_API_KEY')
    systems_status['openai'] = bool(openai_key)
    status = "✅ Configuré" if openai_key else "⚠️ API key manquante"
    print(f"🤖 OpenAI TTS: {status}")
    
    # Azure
    azure_key = os.getenv('AZURE_SPEECH_KEY')
    systems_status['azure'] = bool(azure_key)
    status = "✅ Configuré" if azure_key else "⚠️ Credentials manquants"
    print(f"🏢 Azure TTS: {status}")
    
    # Minimax
    minimax_key = os.getenv('MINIMAX_API_KEY')
    minimax_group = os.getenv('MINIMAX_GROUP_ID')
    systems_status['minimax'] = bool(minimax_key and minimax_group)
    status = "✅ Configuré" if (minimax_key and minimax_group) else "⚠️ Credentials manquants"
    print(f"✨ Minimax TTS: {status}")
    
    # gTTS (always available)
    systems_status['gtts'] = True
    print("🔄 gTTS Enhanced: ✅ Disponible (fallback)")
    
    print()
    
    # Show recommendation
    configured_premium = [name for name, status in systems_status.items() 
                         if status and name != 'gtts']
    
    if configured_premium:
        print(f"🎯 Systèmes premium actifs: {configured_premium}")
        print("🎉 Votre système utilise déjà des voix de qualité premium!")
    else:
        print("💡 Aucun système premium configuré - utilise gTTS Enhanced")
        print("🚀 Configurez un système premium pour des voix ultra-réalistes!")
    
    return systems_status

def show_quick_setup():
    """Show quick setup instructions."""
    print("\n⚡ CONFIGURATION RAPIDE (Recommandée):")
    print("=" * 50)
    print()
    
    print("🤖 OPTION 1: OpenAI TTS (Recommandé pour débuter)")
    print("   💰 Prix: ~$0.075 par vidéo de 3 minutes")
    print("   🎯 Qualité: Très bonne, stable")
    print("   ⚙️ Setup:")
    print("      1. Créez un compte: https://platform.openai.com/")
    print("      2. Générez une API key")
    print("      3. export OPENAI_API_KEY='votre_cle'")
    print("      4. python test_all_tts.py")
    print()
    
    print("🌟 OPTION 2: ElevenLabs (Qualité maximale)")
    print("   💰 Prix: ~$1.50 par vidéo de 3 minutes")
    print("   🎯 Qualité: Exceptionnelle, ultra-réaliste")
    print("   ⚙️ Setup:")
    print("      1. Créez un compte: https://elevenlabs.io/")
    print("      2. Obtenez votre API key")
    print("      3. export ELEVENLABS_API_KEY='votre_cle'")
    print("      4. python test_all_tts.py")
    print()

def demonstrate_voice_selection():
    """Demonstrate automatic voice selection."""
    print("🎭 SÉLECTION AUTOMATIQUE DES VOIX:")
    print("-" * 40)
    print()
    print("Le système choisit automatiquement la voix optimale selon le contenu :")
    print()
    
    examples = [
        ("business strategy", "Voix masculine professionnelle"),
        ("artificial intelligence", "Voix technique claire"),
        ("health and wellness", "Voix féminine douce"),
        ("gaming review", "Voix énergique"),
        ("breaking news", "Voix d'ancre TV"),
        ("meditation guide", "Voix calme et apaisante")
    ]
    
    for topic, voice_desc in examples:
        print(f"   📝 '{topic}' → {voice_desc}")
    
    print()
    print("💡 Cette sélection optimise l'engagement selon le type de contenu!")

def test_current_system():
    """Test the current system."""
    print("\n🧪 TEST DU SYSTÈME ACTUEL:")
    print("-" * 30)
    
    keyword = "voice quality test"
    print(f"🎯 Génération d'une vidéo test: '{keyword}'")
    print("🔄 Lancement du test...")
    
    try:
        import subprocess
        result = subprocess.run([
            "python", "test_single_keyword.py", keyword
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Test réussi!")
            
            # Check generated files
            video_path = Path("output/videos") / f"{keyword.replace(' ', '_')}.mp4"
            audio_path = Path("output/audio") / f"{keyword.replace(' ', '_')}.mp3"
            
            if video_path.exists() and audio_path.exists():
                video_size = video_path.stat().st_size / 1024 / 1024  # MB
                audio_size = audio_path.stat().st_size / 1024 / 1024  # MB
                
                print(f"📁 Vidéo générée: {video_path.name} ({video_size:.1f} MB)")
                print(f"🎵 Audio généré: {audio_path.name} ({audio_size:.1f} MB)")
                print(f"🌐 Voir la vidéo: file://{video_path.absolute()}")
            
        else:
            print("❌ Test échoué")
            print(f"Erreur: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")

def main():
    """Main demo function."""
    print_voice_header()
    
    # Show comparison
    show_voice_comparison()
    
    # Show current status
    systems_status = show_system_status()
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Run test
        test_current_system()
        return
    
    # Interactive menu
    print("🎮 OPTIONS DISPONIBLES:")
    print("1. 🧪 Tester le système actuel")
    print("2. ⚡ Voir la configuration rapide")
    print("3. 🎭 Démonstration sélection de voix")
    print("4. 📊 Comparaison complète des TTS")
    print("5. 📖 Guide complet")
    print()
    
    choice = input("Choisissez une option (1-5): ").strip()
    
    if choice == "1":
        test_current_system()
    
    elif choice == "2":
        show_quick_setup()
    
    elif choice == "3":
        demonstrate_voice_selection()
    
    elif choice == "4":
        print("🔄 Lancement de la comparaison complète...")
        os.system("python test_all_tts.py")
    
    elif choice == "5":
        print("📖 Consultez le guide complet:")
        print("   cat TTS_COMPARISON_GUIDE.md")
        print("   ou")
        print("   open TTS_COMPARISON_GUIDE.md")
    
    else:
        print("❌ Choix invalide")
        return
    
    print("\n🎉 DÉMONSTRATION TERMINÉE!")
    print()
    print("📚 RESSOURCES UTILES:")
    print("   🧪 Test complet: python test_all_tts.py")
    print("   🔊 Comparaison: python voice_comparison_demo.py")
    print("   📖 Guide détaillé: TTS_COMPARISON_GUIDE.md")
    print("   ⚡ Test rapide: python voice_upgrade_demo.py test")
    print()
    print("💡 Configurez un TTS premium pour des voix ultra-réalistes!")

if __name__ == "__main__":
    main()
