#!/usr/bin/env python3
"""
simple_nature_composer.py - Compositeur simplifié pour vidéos nature avec Pixabay
"""

import os
import logging
import subprocess
import random
from pathlib import Path
from typing import Optional, List

from config import get_config
from pixabay_videos import PixabayVideoManager
from music_manager import MusicManager

logger = logging.getLogger(__name__)

class SimpleNatureComposer:
    """Compositeur simplifié pour créer des vidéos avec arrière-plan nature."""
    
    def __init__(self):
        """Initialize simple nature composer."""
        self.config = get_config()
        self.pixabay_manager = PixabayVideoManager()
        self.music_manager = MusicManager()
        
        logger.info("Simple Nature Composer initialized")
    
    def create_nature_video(self, audio_path: str, keyword: str, add_music: bool = True) -> Optional[str]:
        """Create video with nature background and audio."""
        try:
            logger.info(f"Creating nature video for '{keyword}'")
            
            # Get nature videos
            nature_videos = []
            if self.pixabay_manager.available:
                nature_videos = self.pixabay_manager.get_nature_videos_for_keyword(keyword, 3)
            
            if not nature_videos:
                logger.warning("No nature videos available")
                return None
            
            # Select best nature video
            nature_video = self._select_best_video(nature_videos)
            logger.info(f"Selected nature video: {nature_video}")
            
            # Get audio duration
            audio_duration = self._get_audio_duration(audio_path)
            if not audio_duration:
                audio_duration = 180  # Default 3 minutes
            
            # Create output path
            output_path = self.config["directories"]["videos"] / f"{keyword.replace(' ', '_')}.mp4"
            
            # Create video with nature background
            video_path = self._create_simple_nature_video(nature_video, audio_path, audio_duration, output_path)
            
            if not video_path:
                return None
            
            # Add music if requested
            if add_music and self.music_manager:
                logger.info("🎵 Adding soft nature music")
                music_style = self.music_manager.get_music_style_for_keyword(keyword)
                video_path = self.music_manager.add_music_to_video(video_path, music_style)
            
            return video_path
            
        except Exception as e:
            logger.error(f"Error creating nature video: {e}")
            return None
    
    def _select_best_video(self, nature_videos: List[str]) -> str:
        """Select the best nature video from the list."""
        if not nature_videos:
            return None
        
        # For now, just select the first one
        # Could add logic to select based on duration, quality, etc.
        return nature_videos[0]
    
    def _create_simple_nature_video(self, nature_video: str, audio_path: str, 
                                  duration: float, output_path: Path) -> Optional[str]:
        """Create simple video with nature background."""
        try:
            # Simple approach: loop nature video and add audio
            cmd = [
                "ffmpeg", "-y",
                "-stream_loop", "-1",  # Loop nature video
                "-i", nature_video,
                "-i", audio_path,
                "-t", str(duration),  # Duration from audio
                "-c:v", "libx264",
                "-c:a", "aac",
                "-b:a", "192k",
                "-shortest",  # Stop when shortest input ends
                str(output_path)
            ]
            
            logger.info(f"Creating simple nature video: {output_path}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"✅ Nature video created: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"FFmpeg failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating simple nature video: {e}")
            return None
    
    def _get_audio_duration(self, audio_path: str) -> Optional[float]:
        """Get audio duration in seconds."""
        try:
            cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        
        return None

# Test function
def test_simple_composer():
    """Test the simple nature composer."""
    print("🌿 TEST DU COMPOSITEUR NATURE SIMPLIFIÉ")
    print("=" * 50)
    
    try:
        composer = SimpleNatureComposer()
        
        # Check if we have audio from previous generation
        audio_path = "output/audio/nature.mp3"
        
        if not Path(audio_path).exists():
            print(f"❌ Audio non trouvé: {audio_path}")
            print("💡 Créez d'abord l'audio avec: python create_nature_video.py")
            return
        
        print(f"✅ Audio trouvé: {audio_path}")
        
        # Test creation
        print("🎬 Création de la vidéo nature avec arrière-plan Pixabay...")
        
        video_path = composer.create_nature_video(audio_path, "nature_test", add_music=True)
        
        if video_path and Path(video_path).exists():
            file_size = Path(video_path).stat().st_size / (1024 * 1024)
            print(f"✅ Vidéo créée: {video_path} ({file_size:.1f} MB)")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
        else:
            print("❌ Échec de création")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_simple_composer()
