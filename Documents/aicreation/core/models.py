"""
Data models for the video creation system.

This module defines Pydantic models that ensure type safety and data validation
throughout the application.
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum
from pathlib import Path

try:
    from pydantic import BaseModel, Field, validator, root_validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    # Fallback to basic dataclasses if Pydantic not available
    from dataclasses import dataclass, field
    PYDANTIC_AVAILABLE = False


# Enums for type safety
class VideoQuality(str, Enum):
    """Video quality options."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ULTRA = "ultra"


class AudioFormat(str, Enum):
    """Audio format options."""
    MP3 = "mp3"
    WAV = "wav"
    AAC = "aac"


class VideoFormat(str, Enum):
    """Video format options."""
    MP4 = "mp4"
    AVI = "avi"
    MOV = "mov"


class TTSProvider(str, Enum):
    """TTS provider options."""
    PIPER = "piper"
    GTTS = "gtts"
    ELEVENLABS = "elevenlabs"
    OPENAI = "openai"
    AZURE = "azure"


class VideoCategory(str, Enum):
    """Video category options."""
    NATURE = "nature"
    TECHNOLOGY = "technology"
    BUSINESS = "business"
    HEALTH = "health"
    TRAVEL = "travel"
    FOOD = "food"
    SPORTS = "sports"
    EDUCATION = "education"


class MusicType(str, Enum):
    """Music type options."""
    INSTRUMENTAL = "instrumental"
    VOCAL = "vocal"
    NATURE_SOUNDS = "nature_sounds"
    NONE = "none"


if PYDANTIC_AVAILABLE:
    # Pydantic models for full validation
    class BaseConfig:
        """Base configuration for all models."""
        validate_assignment = True
        use_enum_values = True
        arbitrary_types_allowed = True

    class AudioConfig(BaseModel):
        """Audio configuration model."""
        language: str = Field(default="en", description="Audio language")
        format: AudioFormat = Field(default=AudioFormat.MP3)
        quality: VideoQuality = Field(default=VideoQuality.HIGH)
        bitrate: str = Field(default="192k", regex=r"^\d+k$")
        sample_rate: int = Field(default=44100, ge=8000, le=96000)
        channels: int = Field(default=2, ge=1, le=2)
        normalize: bool = Field(default=True)
        fade_in_ms: int = Field(default=1000, ge=0)
        fade_out_ms: int = Field(default=1000, ge=0)
        
        class Config(BaseConfig):
            pass

    class VideoConfig(BaseModel):
        """Video configuration model."""
        width: int = Field(default=1920, ge=640)
        height: int = Field(default=1080, ge=480)
        fps: int = Field(default=30, ge=15, le=60)
        format: VideoFormat = Field(default=VideoFormat.MP4)
        quality: VideoQuality = Field(default=VideoQuality.HIGH)
        codec: str = Field(default="libx264")
        preset: str = Field(default="medium")
        crf: int = Field(default=23, ge=0, le=51)
        
        @validator('height')
        def validate_aspect_ratio(cls, v, values):
            """Validate common aspect ratios."""
            if 'width' in values:
                ratio = values['width'] / v
                if not (1.5 <= ratio <= 2.0):  # Common video ratios
                    raise ValueError(f"Unusual aspect ratio: {values['width']}x{v}")
            return v
        
        class Config(BaseConfig):
            pass

    class ScriptData(BaseModel):
        """Script data model."""
        title: str = Field(..., min_length=1, max_length=200)
        introduction: str = Field(..., min_length=10)
        key_points: List[str] = Field(default_factory=list, max_items=10)
        conclusion: str = Field(..., min_length=10)
        metadata: Dict[str, Any] = Field(default_factory=dict)
        estimated_duration: Optional[float] = Field(None, ge=0)
        word_count: Optional[int] = Field(None, ge=0)
        
        @validator('key_points')
        def validate_key_points(cls, v):
            """Validate key points."""
            if len(v) < 1:
                raise ValueError("At least one key point is required")
            return v
        
        @root_validator
        def calculate_metrics(cls, values):
            """Calculate script metrics."""
            text_parts = [
                values.get('title', ''),
                values.get('introduction', ''),
                values.get('conclusion', '')
            ] + values.get('key_points', [])
            
            full_text = ' '.join(text_parts)
            values['word_count'] = len(full_text.split())
            # Estimate 150 words per minute for speech
            values['estimated_duration'] = values['word_count'] / 150 * 60
            
            return values
        
        class Config(BaseConfig):
            pass

    class VideoRequest(BaseModel):
        """Video creation request model."""
        keyword: str = Field(..., min_length=1, max_length=100)
        category: Optional[VideoCategory] = None
        music_type: MusicType = Field(default=MusicType.INSTRUMENTAL)
        with_vocals: bool = Field(default=False)
        preferred_tts: Optional[TTSProvider] = None
        audio_config: Optional[AudioConfig] = None
        video_config: Optional[VideoConfig] = None
        custom_script: Optional[ScriptData] = None
        output_directory: Optional[Path] = None
        
        @validator('keyword')
        def validate_keyword(cls, v):
            """Validate keyword format."""
            if not v.strip():
                raise ValueError("Keyword cannot be empty")
            return v.strip().lower()
        
        class Config(BaseConfig):
            pass

    class VideoResponse(BaseModel):
        """Video creation response model."""
        success: bool
        video_path: Optional[Path] = None
        audio_path: Optional[Path] = None
        thumbnail_path: Optional[Path] = None
        script: Optional[ScriptData] = None
        duration: Optional[float] = None
        file_size: Optional[int] = None
        processing_time: Optional[float] = None
        error_message: Optional[str] = None
        metadata: Dict[str, Any] = Field(default_factory=dict)
        created_at: datetime = Field(default_factory=datetime.utcnow)
        
        class Config(BaseConfig):
            pass

else:
    # Fallback dataclass models
    @dataclass
    class AudioConfig:
        language: str = "en"
        format: str = "mp3"
        quality: str = "high"
        bitrate: str = "192k"
        sample_rate: int = 44100
        channels: int = 2
        normalize: bool = True
        fade_in_ms: int = 1000
        fade_out_ms: int = 1000

    @dataclass
    class VideoConfig:
        width: int = 1920
        height: int = 1080
        fps: int = 30
        format: str = "mp4"
        quality: str = "high"
        codec: str = "libx264"
        preset: str = "medium"
        crf: int = 23

    @dataclass
    class ScriptData:
        title: str
        introduction: str
        key_points: List[str] = field(default_factory=list)
        conclusion: str = ""
        metadata: Dict[str, Any] = field(default_factory=dict)
        estimated_duration: Optional[float] = None
        word_count: Optional[int] = None

    @dataclass
    class VideoRequest:
        keyword: str
        category: Optional[str] = None
        music_type: str = "instrumental"
        with_vocals: bool = False
        preferred_tts: Optional[str] = None
        audio_config: Optional[AudioConfig] = None
        video_config: Optional[VideoConfig] = None
        custom_script: Optional[ScriptData] = None
        output_directory: Optional[Path] = None

    @dataclass
    class VideoResponse:
        success: bool
        video_path: Optional[Path] = None
        audio_path: Optional[Path] = None
        thumbnail_path: Optional[Path] = None
        script: Optional[ScriptData] = None
        duration: Optional[float] = None
        file_size: Optional[int] = None
        processing_time: Optional[float] = None
        error_message: Optional[str] = None
        metadata: Dict[str, Any] = field(default_factory=dict)
        created_at: datetime = field(default_factory=datetime.utcnow)


# Factory functions for creating default configurations
def create_default_audio_config() -> AudioConfig:
    """Create default audio configuration."""
    return AudioConfig()


def create_default_video_config() -> VideoConfig:
    """Create default video configuration."""
    return VideoConfig()


def create_hd_video_config() -> VideoConfig:
    """Create HD video configuration."""
    return VideoConfig(width=1280, height=720)


def create_4k_video_config() -> VideoConfig:
    """Create 4K video configuration."""
    if PYDANTIC_AVAILABLE:
        return VideoConfig(width=3840, height=2160, quality=VideoQuality.ULTRA)
    else:
        return VideoConfig(width=3840, height=2160, quality="ultra")
