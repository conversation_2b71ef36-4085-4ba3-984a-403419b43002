#!/usr/bin/env python3
"""
SkyReels-A1 Direct Implementation
Copie exacte du code inference_audio.py officiel avec adaptations pour macOS
"""

import torch
import os
import sys
import numpy as np
from PIL import Image
import cv2
import subprocess
import argparse
from pathlib import Path

# Ajouter le répertoire courant au PYTHONPATH
sys.path.insert(0, os.getcwd())

def check_cuda():
    """Vérifie la disponibilité CUDA"""
    if torch.cuda.is_available():
        print(f"✅ CUDA disponible: {torch.cuda.get_device_name(0)}")
        return "cuda"
    else:
        print("⚠️ CUDA non disponible, utilisation du CPU")
        return "cpu"

def install_missing_dependencies():
    """Installe les dépendances manquantes"""
    missing_packages = []
    
    try:
        import decord
    except ImportError:
        missing_packages.append("decord")
    
    try:
        from moviepy.editor import ImageSequenceClip
    except ImportError:
        missing_packages.append("moviepy")
    
    if missing_packages:
        print(f"📦 Installation des dépendances manquantes: {missing_packages}")
        for package in missing_packages:
            if package == "decord":
                # Decord n'est pas disponible sur macOS ARM, on va utiliser cv2
                print("⚠️ Decord non disponible sur macOS ARM, utilisation de cv2")
                continue
            else:
                subprocess.run([sys.executable, "-m", "pip", "install", package], check=True)

def crop_and_resize(image, height, width):
    """Fonction exacte de SkyReels-A1"""
    image = np.array(image)
    image_height, image_width, _ = image.shape
    if image_height / image_width < height / width:
        croped_width = int(image_height / height * width)
        left = (image_width - croped_width) // 2
        image = image[:, left: left+croped_width]
        image = Image.fromarray(image).resize((width, height))
    else:
        pad = int((((width / height) * image_height) - image_width) / 2.)
        padded_image = np.zeros((image_height, image_width + pad * 2, 3), dtype=np.uint8)
        padded_image[:, pad:pad+image_width] = image
        image = Image.fromarray(padded_image).resize((width, height))
    return image

def write_mp4_ffmpeg(video_path, frames, fps=12):
    """Écrit une vidéo MP4 avec FFmpeg directement"""
    # Créer un dossier temporaire pour les frames
    temp_dir = Path("temp_frames")
    temp_dir.mkdir(exist_ok=True)
    
    # Sauvegarder les frames
    for i, frame in enumerate(frames):
        if isinstance(frame, np.ndarray):
            frame = Image.fromarray(frame)
        frame.save(temp_dir / f"frame_{i:04d}.png")
    
    # Créer la vidéo avec FFmpeg
    cmd = [
        "ffmpeg", "-y",
        "-framerate", str(fps),
        "-i", str(temp_dir / "frame_%04d.png"),
        "-c:v", "libx264",
        "-pix_fmt", "yuv420p",
        "-crf", "18",
        "-preset", "slow",
        video_path
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"✅ Vidéo créée: {video_path}")
        
        # Nettoyer les frames temporaires
        for frame_file in temp_dir.glob("*.png"):
            frame_file.unlink()
        temp_dir.rmdir()
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur FFmpeg: {e}")
        return False

def parse_video(driving_frames, max_frame_num, fps=25):
    """Fonction exacte de SkyReels-A1"""
    video_length = len(driving_frames)
    duration = video_length / fps
    target_times = np.arange(0, duration, 1/12)
    frame_indices = (target_times * fps).astype(np.int32)
    frame_indices = frame_indices[frame_indices < video_length]
    
    new_driving_frames = []
    for idx in frame_indices:
        new_driving_frames.append(driving_frames[idx])
        if len(new_driving_frames) >= max_frame_num - 1:
            break
    
    video_lenght_add = max_frame_num - len(new_driving_frames) - 1
    new_driving_frames = [new_driving_frames[0]]*2 + new_driving_frames[1:len(new_driving_frames)-1] + [new_driving_frames[-1]] * video_lenght_add
    return new_driving_frames

def add_audio_to_video(silent_video_path, audio_path, output_video_path):
    """Fonction exacte de SkyReels-A1"""
    cmd = [
        'ffmpeg', '-y',
        '-i', silent_video_path,
        '-i', audio_path,
        '-map', '0:v', '-map', '1:a',
        '-c:v', 'copy', '-shortest',
        output_video_path
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"✅ Audio ajouté: {output_video_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur ajout audio: {e}")
        return False

def run_skyreels_inference():
    """Lance l'inférence SkyReels-A1 directement"""
    
    print("🎭 SkyReels-A1 Direct Implementation")
    print("=" * 50)
    
    # Vérifier CUDA
    device = check_cuda()
    
    # Installer les dépendances manquantes
    install_missing_dependencies()
    
    # Paramètres (exactement comme dans inference_audio.py)
    guidance_scale = 3.0
    seed = 43
    num_inference_steps = 10
    sample_size = [480, 720]
    max_frame_num = 49
    target_fps = 12
    weight_dtype = torch.bfloat16 if device == "cuda" else torch.float32
    
    # Chemins
    image_path = "/Users/<USER>/Documents/aicreation/assets/music/le-journaliste-parle-du-segment-d-actualite.jpg"
    audio_path = "assets/driving_audio/journaliste_test.wav"
    save_path = "outputs_skyreels_direct"
    
    # Vérifier les fichiers
    if not Path(image_path).exists():
        print(f"❌ Image non trouvée: {image_path}")
        return
    
    if not Path(audio_path).exists():
        print(f"❌ Audio non trouvé: {audio_path}")
        return
    
    print(f"📸 Image: {Path(image_path).name}")
    print(f"🎤 Audio: {Path(audio_path).name}")
    print(f"💻 Device: {device}")
    print(f"🔢 Dtype: {weight_dtype}")
    
    try:
        # Importer les modules SkyReels-A1
        print("📦 Import des modules SkyReels-A1...")
        
        from diffusers.models import AutoencoderKLCogVideoX
        from diffusers.utils import export_to_video, load_image
        from transformers import SiglipImageProcessor, SiglipVisionModel
        print("✅ Diffusers et Transformers importés")
        
        from skyreels_a1.models.transformer3d import CogVideoXTransformer3DModel
        from skyreels_a1.skyreels_a1_i2v_pipeline import SkyReelsA1ImagePoseToVideoPipeline
        from skyreels_a1.pre_process_lmk3d import FaceAnimationProcessor
        from skyreels_a1.src.media_pipe.mp_utils import LMKExtractor
        from skyreels_a1.src.media_pipe.draw_util_2d import FaceMeshVisualizer2d
        print("✅ Modules SkyReels-A1 importés")
        
        from facexlib.utils.face_restoration_helper import FaceRestoreHelper
        print("✅ FaceXLib importé")
        
        # Essayer d'importer DiffPoseTalk
        try:
            from diffposetalk.diffposetalk import DiffPoseTalk
            print("✅ DiffPoseTalk importé")
            diffposetalk_available = True
        except ImportError:
            print("⚠️ DiffPoseTalk non disponible - utilisation d'un fallback")
            diffposetalk_available = False
        
        # Générateur
        generator = torch.Generator(device=device).manual_seed(seed)
        
        # Chemins des modèles
        model_name = "pretrained_models/SkyReels-A1-5B/"
        siglip_name = "pretrained_models/SkyReels-A1-5B/siglip-so400m-patch14-384"
        
        # Vérifier les modèles
        if not Path(model_name).exists():
            print(f"❌ Modèle non trouvé: {model_name}")
            print("📥 Téléchargez avec: huggingface-cli download Skywork/SkyReels-A1 --local-dir pretrained_models/SkyReels-A1-5B")
            return
        
        print("🔧 Initialisation des composants...")
        
        # Initialiser les composants (exactement comme dans inference_audio.py)
        lmk_extractor = LMKExtractor()
        processor = FaceAnimationProcessor(checkpoint='pretrained_models/smirk/SMIRK_em1.pt')
        vis = FaceMeshVisualizer2d(forehead_edge=False, draw_head=False, draw_iris=False)
        
        face_helper = FaceRestoreHelper(
            upscale_factor=1, face_size=512, crop_ratio=(1, 1),
            det_model='retinaface_resnet50', save_ext='png', device=device
        )
        
        # SigLIP visual encoder
        siglip = SiglipVisionModel.from_pretrained(siglip_name)
        siglip_normalize = SiglipImageProcessor.from_pretrained(siglip_name)
        
        # DiffPoseTalk
        if diffposetalk_available:
            diffposetalk = DiffPoseTalk()
        else:
            print("❌ DiffPoseTalk requis pour l'audio - arrêt")
            return
        
        print("🔧 Chargement des modèles SkyReels-A1...")
        
        # Modèles SkyReels-A1 (exactement comme dans inference_audio.py)
        transformer = CogVideoXTransformer3DModel.from_pretrained(
            model_name, subfolder="transformer"
        ).to(weight_dtype)
        
        vae = AutoencoderKLCogVideoX.from_pretrained(
            model_name, subfolder="vae"
        ).to(weight_dtype)
        
        lmk_encoder = AutoencoderKLCogVideoX.from_pretrained(
            model_name, subfolder="pose_guider"
        ).to(weight_dtype)
        
        # Pipeline
        pipe = SkyReelsA1ImagePoseToVideoPipeline.from_pretrained(
            model_name,
            transformer=transformer,
            vae=vae,
            lmk_encoder=lmk_encoder,
            image_encoder=siglip,
            feature_extractor=siglip_normalize,
            torch_dtype=weight_dtype
        )
        
        pipe.to(device)
        pipe.enable_model_cpu_offload()
        pipe.vae.enable_tiling()
        
        print("✅ Pipeline SkyReels-A1 initialisé")
        
        # Traitement (exactement comme dans inference_audio.py)
        print("📸 Traitement de l'image...")
        
        image = load_image(image=image_path)
        image = processor.crop_and_resize(image, sample_size[0], sample_size[1])
        
        # ref image crop face
        ref_image, x1, y1 = processor.face_crop(np.array(image))
        face_h, face_w, _ = ref_image.shape
        source_image = ref_image
        
        source_outputs, source_tform, image_original = processor.process_source_image(source_image)
        
        print("🎤 Génération des coefficients audio...")
        driving_outputs = diffposetalk.infer_from_file(
            audio_path, 
            source_outputs["shape_params"].view(-1)[:100].detach().cpu().numpy()
        )
        
        out_frames = processor.preprocess_lmk3d_from_coef(
            source_outputs, source_tform, image_original.shape, driving_outputs
        )
        out_frames = parse_video(out_frames, max_frame_num)
        
        # Préparation des motions (exactement comme dans inference_audio.py)
        rescale_motions = np.zeros_like(image)[np.newaxis, :].repeat(48, axis=0)
        for ii in range(rescale_motions.shape[0]):
            rescale_motions[ii][y1:y1+face_h, x1:x1+face_w] = out_frames[ii]
        
        ref_image = cv2.resize(ref_image, (512, 512))
        ref_lmk = lmk_extractor(ref_image[:, :, ::-1])
        ref_img = vis.draw_landmarks_v3(
            (512, 512), (face_w, face_h), 
            ref_lmk['lmks'].astype(np.float32), normed=True
        )
        
        first_motion = np.zeros_like(np.array(image))
        first_motion[y1:y1+face_h, x1:x1+face_w] = ref_img
        first_motion = first_motion[np.newaxis, :]
        motions = np.concatenate([first_motion, rescale_motions])
        
        input_video = motions[:max_frame_num]
        
        face_helper.clean_all()
        face_helper.read_image(np.array(image)[:, :, ::-1])
        face_helper.get_face_landmarks_5(only_center_face=True)
        face_helper.align_warp_face()
        align_face = face_helper.cropped_faces[0]
        image_face = align_face[:, :, ::-1]
        
        input_video = input_video[:max_frame_num]
        motions = np.array(input_video)
        input_video = torch.from_numpy(np.array(input_video)).permute([3, 0, 1, 2]).unsqueeze(0)
        input_video = input_video / 255
        
        print("🎬 Génération de la vidéo SkyReels-A1...")
        
        # Génération (exactement comme dans inference_audio.py)
        out_samples = []
        with torch.no_grad():
            sample = pipe(
                image=image,
                image_face=image_face,
                control_video=input_video,
                prompt="",
                negative_prompt="",
                height=sample_size[0],
                width=sample_size[1],
                num_frames=49,
                generator=generator,
                guidance_scale=guidance_scale,
                num_inference_steps=num_inference_steps,
            )
        
        out_samples.extend(sample.frames[0])
        out_samples = out_samples[2:]  # Enlever les 2 premières frames
        
        # Sauvegarde
        save_path_name = os.path.basename(image_path).split(".")[0] + "-" + os.path.basename(audio_path).split(".")[0] + ".mp4"
        
        if not os.path.exists(save_path):
            os.makedirs(save_path, exist_ok=True)
        
        video_path = os.path.join(save_path, save_path_name.split(".")[0] + "_output.mp4")
        
        # Export de la vidéo
        export_to_video(out_samples, video_path, fps=target_fps)
        print(f"✅ Vidéo générée: {video_path}")
        
        # Ajout de l'audio
        audio_video_path = video_path.split(".")[0] + "_audio.mp4"
        add_audio_to_video(video_path, audio_path, audio_video_path)
        
        print("\n🎉 AVATAR SKYREELS-A1 CRÉÉ AVEC SUCCÈS!")
        print("=" * 50)
        print(f"📁 Vidéo finale: {audio_video_path}")
        print(f"🎭 Avatar: Journaliste avec mouvements de lèvres réalistes")
        print(f"🎤 Audio: Synchronisé avec SkyReels-A1 + DiffPoseTalk")
        print(f"📐 Format: 480x720, 12 FPS")
        print(f"🤖 IA: Vraie génération SkyReels-A1")
        
        return audio_video_path
        
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        print("📦 Installez les dépendances avec:")
        print("pip install -r requirements.txt")
        return None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    run_skyreels_inference()
