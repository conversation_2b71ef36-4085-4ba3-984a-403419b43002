#!/usr/bin/env python3
"""
Gestionnaire de configuration avancé avec validation et variables d'environnement.
Support YAML, JSON et variables d'environnement avec validation de schéma.
"""

import os
import re
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from enum import Enum
import string
from loguru import logger

try:
    from pydantic import BaseModel, Field, validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    logger.warning("Pydantic non disponible - validation basique utilisée")

class ConfigFormat(Enum):
    """Formats de configuration supportés"""
    YAML = "yaml"
    JSON = "json"
    ENV = "env"

@dataclass
class ValidationRule:
    """Règle de validation pour un paramètre"""
    required: bool = False
    type_check: Optional[type] = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    allowed_values: Optional[List[Any]] = None
    pattern: Optional[str] = None
    custom_validator: Optional[callable] = None

class ConfigValidator:
    """Validateur de configuration avec règles personnalisées"""
    
    def __init__(self):
        self.rules: Dict[str, ValidationRule] = {}
        self._setup_default_rules()
    
    def _setup_default_rules(self):
        """Configure les règles de validation par défaut"""
        self.rules.update({
            # Performance
            "performance.memory_limit": ValidationRule(
                required=True, type_check=int, min_value=1024, max_value=32768
            ),
            "performance.max_workers": ValidationRule(
                required=True, type_check=int, min_value=1, max_value=16
            ),
            
            # Formats vidéo
            "video_formats.youtube.width": ValidationRule(
                required=True, type_check=int, min_value=640, max_value=3840
            ),
            "video_formats.youtube.height": ValidationRule(
                required=True, type_check=int, min_value=480, max_value=2160
            ),
            "video_formats.youtube.fps": ValidationRule(
                required=True, type_check=int, allowed_values=[24, 25, 30, 50, 60]
            ),
            
            # Audio
            "audio.tts.speed": ValidationRule(
                required=False, type_check=float, min_value=0.5, max_value=2.0
            ),
            "audio.tts.volume": ValidationRule(
                required=False, type_check=float, min_value=0.0, max_value=1.0
            ),
            "audio.music.volume": ValidationRule(
                required=False, type_check=float, min_value=0.0, max_value=1.0
            ),
            
            # Sécurité
            "security.input_validation.max_keyword_length": ValidationRule(
                required=True, type_check=int, min_value=10, max_value=500
            ),
            "security.resource_limits.max_video_duration": ValidationRule(
                required=True, type_check=int, min_value=10, max_value=3600
            ),
            
            # APIs
            "apis.pixabay.timeout": ValidationRule(
                required=False, type_check=int, min_value=5, max_value=120
            ),
        })
    
    def add_rule(self, path: str, rule: ValidationRule):
        """Ajoute une règle de validation personnalisée"""
        self.rules[path] = rule
    
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """Valide une configuration et retourne les erreurs"""
        errors = []
        
        for path, rule in self.rules.items():
            try:
                value = self._get_nested_value(config, path)
                error = self._validate_value(path, value, rule)
                if error:
                    errors.append(error)
            except KeyError:
                if rule.required:
                    errors.append(f"Paramètre requis manquant: {path}")
        
        return errors
    
    def _get_nested_value(self, config: Dict[str, Any], path: str) -> Any:
        """Récupère une valeur imbriquée dans la configuration"""
        keys = path.split('.')
        value = config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                raise KeyError(f"Clé non trouvée: {path}")
        
        return value
    
    def _validate_value(self, path: str, value: Any, rule: ValidationRule) -> Optional[str]:
        """Valide une valeur selon une règle"""
        # Vérification du type
        if rule.type_check and not isinstance(value, rule.type_check):
            return f"{path}: type attendu {rule.type_check.__name__}, reçu {type(value).__name__}"
        
        # Vérification des valeurs min/max
        if rule.min_value is not None and value < rule.min_value:
            return f"{path}: valeur {value} inférieure au minimum {rule.min_value}"
        
        if rule.max_value is not None and value > rule.max_value:
            return f"{path}: valeur {value} supérieure au maximum {rule.max_value}"
        
        # Vérification des valeurs autorisées
        if rule.allowed_values and value not in rule.allowed_values:
            return f"{path}: valeur {value} non autorisée. Valeurs autorisées: {rule.allowed_values}"
        
        # Vérification du pattern
        if rule.pattern and isinstance(value, str):
            if not re.match(rule.pattern, value):
                return f"{path}: valeur '{value}' ne correspond pas au pattern {rule.pattern}"
        
        # Validation personnalisée
        if rule.custom_validator:
            try:
                if not rule.custom_validator(value):
                    return f"{path}: échec de la validation personnalisée"
            except Exception as e:
                return f"{path}: erreur dans la validation personnalisée: {e}"
        
        return None

class ConfigManager:
    """Gestionnaire de configuration avec support multi-format et validation"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.validator = ConfigValidator()
        self.config: Dict[str, Any] = {}
        self.env_vars: Dict[str, str] = {}
        
        # Chargement de la configuration
        self._load_environment_variables()
        self._load_config_file()
        self._resolve_variables()
        self._validate_configuration()
    
    def _load_environment_variables(self):
        """Charge les variables d'environnement pertinentes"""
        env_prefixes = ["PIXABAY_", "PEXELS_", "OPENAI_", "OLLAMA_", "VIDEO_"]
        
        for key, value in os.environ.items():
            if any(key.startswith(prefix) for prefix in env_prefixes):
                self.env_vars[key] = value
        
        logger.info(f"Variables d'environnement chargées: {len(self.env_vars)}")
    
    def _load_config_file(self):
        """Charge le fichier de configuration"""
        if not self.config_path.exists():
            logger.warning(f"Fichier de configuration non trouvé: {self.config_path}")
            self.config = self._get_default_config()
            return
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.suffix.lower() in ['.yaml', '.yml']:
                    self.config = yaml.safe_load(f)
                elif self.config_path.suffix.lower() == '.json':
                    self.config = json.load(f)
                else:
                    raise ValueError(f"Format de fichier non supporté: {self.config_path.suffix}")
            
            logger.info(f"Configuration chargée depuis: {self.config_path}")
            
        except Exception as e:
            logger.error(f"Erreur chargement configuration: {e}")
            self.config = self._get_default_config()
    
    def _resolve_variables(self):
        """Résout les variables d'environnement dans la configuration"""
        self.config = self._resolve_variables_recursive(self.config)
    
    def _resolve_variables_recursive(self, obj: Any) -> Any:
        """Résout récursivement les variables dans un objet"""
        if isinstance(obj, dict):
            return {key: self._resolve_variables_recursive(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._resolve_variables_recursive(item) for item in obj]
        elif isinstance(obj, str):
            return self._resolve_string_variables(obj)
        else:
            return obj
    
    def _resolve_string_variables(self, text: str) -> str:
        """Résout les variables dans une chaîne de caractères"""
        # Variables d'environnement: ${VAR_NAME}
        env_pattern = r'\$\{([^}]+)\}'
        
        def replace_env_var(match):
            var_name = match.group(1)
            if var_name in self.env_vars:
                return self.env_vars[var_name]
            elif var_name in os.environ:
                return os.environ[var_name]
            else:
                logger.warning(f"Variable d'environnement non trouvée: {var_name}")
                return match.group(0)  # Retourne la variable non résolue
        
        text = re.sub(env_pattern, replace_env_var, text)
        
        # Variables de configuration: ${config.path}
        config_pattern = r'\$\{([^}]+)\}'
        
        def replace_config_var(match):
            var_path = match.group(1)
            try:
                value = self.validator._get_nested_value(self.config, var_path)
                return str(value)
            except KeyError:
                logger.warning(f"Variable de configuration non trouvée: {var_path}")
                return match.group(0)
        
        text = re.sub(config_pattern, replace_config_var, text)
        
        return text
    
    def _validate_configuration(self):
        """Valide la configuration chargée"""
        errors = self.validator.validate_config(self.config)
        
        if errors:
            logger.error("Erreurs de validation de la configuration:")
            for error in errors:
                logger.error(f"  - {error}")
            
            # En mode strict, on pourrait lever une exception
            # raise ValueError("Configuration invalide")
        else:
            logger.info("Configuration validée avec succès")
    
    def get(self, path: str, default: Any = None) -> Any:
        """Récupère une valeur de configuration"""
        try:
            return self.validator._get_nested_value(self.config, path)
        except KeyError:
            return default
    
    def set(self, path: str, value: Any):
        """Définit une valeur de configuration"""
        keys = path.split('.')
        current = self.config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def save(self, path: Optional[str] = None):
        """Sauvegarde la configuration"""
        save_path = Path(path) if path else self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                if save_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                elif save_path.suffix.lower() == '.json':
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration sauvegardée: {save_path}")
            
        except Exception as e:
            logger.error(f"Erreur sauvegarde configuration: {e}")
    
    def reload(self):
        """Recharge la configuration"""
        self._load_config_file()
        self._resolve_variables()
        self._validate_configuration()
        logger.info("Configuration rechargée")
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """Récupère une section complète de la configuration"""
        return self.get(section, {})
    
    def merge_config(self, other_config: Dict[str, Any]):
        """Fusionne une autre configuration"""
        self.config = self._deep_merge(self.config, other_config)
        self._validate_configuration()
    
    def _deep_merge(self, base: Dict, overlay: Dict) -> Dict:
        """Fusion profonde de deux dictionnaires"""
        result = base.copy()
        
        for key, value in overlay.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Configuration par défaut en cas d'erreur"""
        return {
            "performance": {
                "use_gpu": False,
                "max_workers": 2,
                "memory_limit": 4096
            },
            "video_formats": {
                "youtube": {
                    "width": 1920,
                    "height": 1080,
                    "fps": 30
                }
            },
            "audio": {
                "tts": {
                    "primary_provider": "gtts",
                    "volume": 0.8
                },
                "music": {
                    "enabled": True,
                    "volume": 0.3
                }
            },
            "output": {
                "directories": {
                    "base": "./output",
                    "videos": "./output/videos",
                    "audio": "./output/audio"
                }
            },
            "logging": {
                "level": "INFO",
                "files": {
                    "main": "./logs/video_creator.log"
                }
            }
        }
    
    def create_directories(self):
        """Crée les répertoires de sortie"""
        directories = self.get("output.directories", {})
        
        for name, path in directories.items():
            dir_path = Path(path)
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Répertoire créé: {dir_path}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Retourne des statistiques sur la configuration"""
        return {
            "config_file": str(self.config_path),
            "config_exists": self.config_path.exists(),
            "env_vars_loaded": len(self.env_vars),
            "validation_rules": len(self.validator.rules),
            "config_sections": len(self.config)
        }

# Instance globale du gestionnaire de configuration
config_manager = ConfigManager()

# Fonctions utilitaires
def get_config(path: str = None, default: Any = None) -> Any:
    """Fonction utilitaire pour récupérer une configuration"""
    if path is None:
        return config_manager.config
    return config_manager.get(path, default)

def reload_config():
    """Recharge la configuration globale"""
    config_manager.reload()

def create_output_directories():
    """Crée les répertoires de sortie"""
    config_manager.create_directories()

# Exemple d'utilisation
if __name__ == "__main__":
    # Test du gestionnaire de configuration
    manager = ConfigManager("config.yaml")
    
    # Affichage des statistiques
    stats = manager.get_stats()
    print("Statistiques de configuration:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Test de récupération de valeurs
    print(f"\nMemory limit: {manager.get('performance.memory_limit')}")
    print(f"Video width: {manager.get('video_formats.youtube.width')}")
    print(f"TTS provider: {manager.get('audio.tts.primary_provider')}")
    
    # Création des répertoires
    manager.create_directories()
    print("Répertoires de sortie créés")
