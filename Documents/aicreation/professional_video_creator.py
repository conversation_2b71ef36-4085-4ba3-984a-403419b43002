#!/usr/bin/env python3
"""
Professional Video Creator v2.0 - Système de création vidéo de classe mondiale
Intègre toutes les améliorations demandées : multi-format, retry, sous-titres, etc.

🎯 Fonctionnalités:
- Multi-format (YouTube 16:9, TikTok 9:16, Instagram 1:1)
- Retry/backoff intelligent pour APIs
- Sous-titres synchronisés automatiques
- Transitions dynamiques et effets
- Configuration centralisée YAML
- Logging structuré avec loguru
- 100% Open Source
"""

import asyncio
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import time
from loguru import logger

# Imports des modules améliorés
from utils.config_manager import ConfigManager, get_config
from utils.retry_manager import retry_manager, retry_pixabay, retry_ollama, retry_tts
from utils.subtitle_generator import SubtitleGenerator, SubtitleStyle
from utils.format_manager import FormatManager, Platform, Quality

# Imports des modules existants (améliorés)
from enhanced_video_creator import EnhancedVideoCreator, VideoConfig, VideoFormat, VoiceGender
from skyreels_manager import SkyReelsManager
from audio_manager import AudioManager
from ollama_manager import OllamaManager
from pixabay_videos import PixabayVideoManager
from music_manager import MusicManager

class CreationMode(Enum):
    """Modes de création disponibles"""
    INTERACTIVE = "interactive"
    CLI = "cli"
    BATCH = "batch"
    API = "api"

@dataclass
class CreationRequest:
    """Requête de création vidéo complète"""
    keyword: str
    platform: Platform
    quality: Quality = Quality.MEDIUM
    duration: Optional[int] = None
    voice_gender: VoiceGender = VoiceGender.FEMALE
    add_music: bool = True
    add_subtitles: bool = True
    use_skyreels: bool = True
    use_pixabay: bool = True
    style: str = "engaging"
    output_name: Optional[str] = None

class ProfessionalVideoCreator:
    """Créateur de vidéos professionnel avec toutes les améliorations v2.0"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialise le créateur professionnel"""
        self.setup_logging()
        logger.info("🚀 Professional Video Creator v2.0 - Initialisation")
        
        # Gestionnaires principaux
        self.config_manager = ConfigManager(config_path)
        self.format_manager = FormatManager()
        self.subtitle_generator = SubtitleGenerator()
        
        # Créateur vidéo amélioré
        self.enhanced_creator = EnhancedVideoCreator(config_path)
        
        # Statistiques
        self.stats = {
            "videos_created": 0,
            "total_processing_time": 0.0,
            "errors": 0,
            "platforms_used": set(),
            "start_time": time.time()
        }
        
        logger.info("✅ Initialisation terminée")
    
    def setup_logging(self):
        """Configure le système de logging avancé"""
        logger.remove()
        
        # Console avec format coloré
        logger.add(
            sys.stderr,
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>",
            level="INFO",
            colorize=True
        )
        
        # Fichier principal avec rotation
        logger.add(
            "logs/professional_video_creator.log",
            rotation="50 MB",
            retention="30 days",
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            compression="zip"
        )
        
        # Fichier d'erreurs séparé
        logger.add(
            "logs/errors.log",
            level="ERROR",
            rotation="10 MB",
            retention="90 days",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}\n{exception}",
            backtrace=True,
            diagnose=True
        )
    
    async def create_professional_video(self, request: CreationRequest) -> Dict[str, Any]:
        """
        Crée une vidéo professionnelle avec toutes les améliorations
        
        Args:
            request: Requête de création complète
            
        Returns:
            Dictionnaire avec les résultats et métadonnées
        """
        start_time = time.time()
        video_id = f"{request.keyword}_{request.platform.value}_{int(start_time)}"
        
        logger.info(f"🎬 Création vidéo professionnelle: {video_id}")
        logger.info(f"📱 Plateforme: {request.platform.value}")
        logger.info(f"🎯 Mot-clé: {request.keyword}")
        
        try:
            # Étape 1: Validation et préparation
            await self._validate_request(request)
            
            # Étape 2: Configuration optimisée pour la plateforme
            video_config = self._create_optimized_config(request)
            
            # Étape 3: Génération du script amélioré
            script_data = await self._generate_enhanced_script(request, video_id)
            
            # Étape 4: Création audio avec retry
            audio_path = await self._create_robust_audio(script_data, request, video_id)
            
            # Étape 5: Acquisition contenu visuel avec retry
            visual_content = await self._acquire_visual_content(request, script_data, video_id)
            
            # Étape 6: Assemblage vidéo avec transitions
            video_path = await self._assemble_professional_video(
                visual_content, audio_path, script_data, video_config, video_id
            )
            
            # Étape 7: Génération sous-titres synchronisés
            subtitle_path = None
            if request.add_subtitles:
                subtitle_path = await self._generate_synchronized_subtitles(
                    script_data, audio_path, video_path, video_config, video_id
                )
            
            # Étape 8: Génération thumbnail optimisée
            thumbnail_path = await self._generate_platform_thumbnail(
                video_path, script_data, request, video_id
            )
            
            # Étape 9: Validation finale et métadonnées
            result = await self._finalize_video(
                video_path, audio_path, subtitle_path, thumbnail_path,
                script_data, request, video_id, start_time
            )
            
            # Mise à jour des statistiques
            self._update_stats(request, time.time() - start_time, success=True)
            
            logger.success(f"✅ Vidéo créée avec succès: {video_path}")
            return result
            
        except Exception as e:
            self._update_stats(request, time.time() - start_time, success=False)
            logger.error(f"❌ Erreur création vidéo {video_id}: {e}")
            raise
    
    async def _validate_request(self, request: CreationRequest):
        """Valide la requête de création"""
        logger.debug("🔍 Validation de la requête")
        
        # Validation du mot-clé
        if not request.keyword or len(request.keyword.strip()) < 3:
            raise ValueError("Le mot-clé doit contenir au moins 3 caractères")
        
        if len(request.keyword) > 100:
            raise ValueError("Le mot-clé ne peut pas dépasser 100 caractères")
        
        # Validation de la plateforme
        if request.platform not in self.format_manager.get_supported_platforms():
            raise ValueError(f"Plateforme non supportée: {request.platform}")
        
        # Validation de la durée
        if request.duration:
            if not self.format_manager.validate_duration(request.platform, request.duration):
                spec = self.format_manager.get_format_spec(request.platform, request.quality)
                raise ValueError(
                    f"Durée {request.duration}s invalide pour {request.platform.value}. "
                    f"Plage autorisée: {spec.min_duration}-{spec.max_duration}s"
                )
        
        logger.debug("✅ Requête validée")
    
    def _create_optimized_config(self, request: CreationRequest) -> VideoConfig:
        """Crée une configuration optimisée pour la plateforme"""
        logger.debug(f"⚙️ Configuration pour {request.platform.value}")
        
        # Mapping des plateformes vers les formats
        platform_to_format = {
            Platform.YOUTUBE: VideoFormat.YOUTUBE,
            Platform.TIKTOK: VideoFormat.TIKTOK,
            Platform.INSTAGRAM: VideoFormat.INSTAGRAM,
            Platform.YOUTUBE_SHORT: VideoFormat.YOUTUBE_SHORT
        }
        
        # Durée optimale si non spécifiée
        if not request.duration:
            request.duration = int(self.format_manager.suggest_optimal_duration(
                request.platform, request.style
            ))
        
        # Configuration vidéo optimisée
        config = VideoConfig(
            format=platform_to_format.get(request.platform, VideoFormat.YOUTUBE),
            duration=request.duration,
            voice_gender=request.voice_gender,
            add_music=request.add_music,
            add_subtitles=request.add_subtitles,
            use_skyreels=request.use_skyreels,
            use_pixabay=request.use_pixabay,
            script_style=request.style,
            add_transitions=True,
            transition_type="fade",
            generate_thumbnail=True,
            generate_transcript=True
        )
        
        logger.debug(f"✅ Configuration créée: {config.format.value}, {config.duration}s")
        return config
    
    @retry_ollama
    async def _generate_enhanced_script(self, request: CreationRequest, video_id: str) -> Dict[str, Any]:
        """Génère un script amélioré avec retry"""
        logger.info("🧠 Génération du script avec IA")
        
        # Utilisation du créateur amélioré
        script_data = await self.enhanced_creator._generate_enhanced_script(
            request.keyword, self._create_optimized_config(request)
        )
        
        logger.info(f"✅ Script généré: {script_data.get('TITRE', 'Sans titre')}")
        return script_data
    
    @retry_tts
    async def _create_robust_audio(self, script_data: Dict, request: CreationRequest, video_id: str) -> str:
        """Crée l'audio avec retry et fallback"""
        logger.info("🎤 Création audio avec retry")
        
        # Configuration optimisée
        config = self._create_optimized_config(request)
        
        # Utilisation du créateur amélioré avec retry
        audio_path = await self.enhanced_creator._generate_enhanced_audio(script_data, config)
        
        logger.info(f"✅ Audio créé: {audio_path}")
        return audio_path
    
    @retry_pixabay
    async def _acquire_visual_content(self, request: CreationRequest, script_data: Dict, video_id: str) -> Dict:
        """Acquiert le contenu visuel avec retry"""
        logger.info("🎨 Acquisition contenu visuel avec retry")
        
        config = self._create_optimized_config(request)
        
        # Utilisation du créateur amélioré avec retry
        visual_content = await self.enhanced_creator._generate_visual_content(
            request.keyword, script_data, config
        )
        
        logger.info(f"✅ Contenu visuel acquis: {len(visual_content.get('background_videos', []))} vidéos")
        return visual_content
    
    async def _assemble_professional_video(
        self, visual_content: Dict, audio_path: str, script_data: Dict, 
        config: VideoConfig, video_id: str
    ) -> str:
        """Assemble la vidéo avec transitions professionnelles"""
        logger.info("🎬 Assemblage vidéo professionnel")
        
        # Utilisation du créateur amélioré
        video_path = await self.enhanced_creator._assemble_enhanced_video(
            visual_content, audio_path, script_data, config
        )
        
        logger.info(f"✅ Vidéo assemblée: {video_path}")
        return video_path
    
    async def _generate_synchronized_subtitles(
        self, script_data: Dict, audio_path: str, video_path: str, 
        config: VideoConfig, video_id: str
    ) -> str:
        """Génère des sous-titres synchronisés"""
        logger.info("📝 Génération sous-titres synchronisés")
        
        try:
            # Configuration des sous-titres selon la plateforme
            subtitle_style = SubtitleStyle.MODERN
            
            # Chemin de sortie
            subtitle_path = Path(video_path).parent / f"{video_id}_subtitles.srt"
            
            # Génération avec le générateur de sous-titres
            result_path = self.subtitle_generator.generate_srt(
                script_data, audio_path, str(subtitle_path), config.duration
            )
            
            logger.info(f"✅ Sous-titres générés: {result_path}")
            return result_path
            
        except Exception as e:
            logger.warning(f"Erreur génération sous-titres: {e}")
            return None
    
    async def _generate_platform_thumbnail(
        self, video_path: str, script_data: Dict, request: CreationRequest, video_id: str
    ) -> str:
        """Génère une thumbnail optimisée pour la plateforme"""
        logger.info("🎨 Génération thumbnail optimisée")
        
        try:
            # Utilisation du créateur amélioré
            config = self._create_optimized_config(request)
            thumbnail_path = await self.enhanced_creator._generate_thumbnail(
                video_path, script_data, config
            )
            
            logger.info(f"✅ Thumbnail générée: {thumbnail_path}")
            return thumbnail_path
            
        except Exception as e:
            logger.warning(f"Erreur génération thumbnail: {e}")
            return None
    
    async def _finalize_video(
        self, video_path: str, audio_path: str, subtitle_path: Optional[str],
        thumbnail_path: Optional[str], script_data: Dict, request: CreationRequest,
        video_id: str, start_time: float
    ) -> Dict[str, Any]:
        """Finalise la vidéo et crée les métadonnées"""
        logger.info("📦 Finalisation de la vidéo")
        
        processing_time = time.time() - start_time
        video_file = Path(video_path)
        
        # Métadonnées complètes
        metadata = {
            "video_id": video_id,
            "keyword": request.keyword,
            "platform": request.platform.value,
            "quality": request.quality.value,
            "duration": request.duration,
            "processing_time": round(processing_time, 2),
            "file_size_mb": round(video_file.stat().st_size / (1024 * 1024), 2),
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "script_data": script_data,
            "format_info": self.format_manager.get_platform_info(request.platform)
        }
        
        # Résultat final
        result = {
            "success": True,
            "video_path": video_path,
            "audio_path": audio_path,
            "subtitle_path": subtitle_path,
            "thumbnail_path": thumbnail_path,
            "metadata": metadata
        }
        
        logger.info(f"✅ Vidéo finalisée en {processing_time:.1f}s")
        return result
    
    def _update_stats(self, request: CreationRequest, processing_time: float, success: bool):
        """Met à jour les statistiques"""
        if success:
            self.stats["videos_created"] += 1
            self.stats["platforms_used"].add(request.platform.value)
        else:
            self.stats["errors"] += 1
        
        self.stats["total_processing_time"] += processing_time
    
    async def interactive_mode(self):
        """Mode interactif avec interface utilisateur améliorée"""
        print("\n🎬 PROFESSIONAL VIDEO CREATOR v2.0")
        print("=" * 60)
        print("🚀 Système de création vidéo de classe mondiale")
        print("📱 Multi-format • 🔄 Retry intelligent • 📝 Sous-titres auto")
        print("=" * 60)
        
        try:
            # Sélection du mot-clé
            keyword = input("\n🎯 Entrez votre mot-clé ou sujet: ").strip()
            if not keyword:
                print("❌ Le mot-clé ne peut pas être vide")
                return
            
            # Sélection de la plateforme
            print("\n📱 PLATEFORMES DISPONIBLES:")
            platforms = self.format_manager.get_supported_platforms()
            for i, platform in enumerate(platforms, 1):
                info = self.format_manager.get_platform_info(platform)
                print(f"   {i}. {platform.value.upper()} ({info['aspect_ratio']}) - {info['max_duration']}s max")
            
            platform_choice = input(f"Choisissez une plateforme (1-{len(platforms)}): ").strip()
            try:
                selected_platform = platforms[int(platform_choice) - 1]
            except (ValueError, IndexError):
                selected_platform = Platform.YOUTUBE
                print(f"⚠️ Choix invalide, utilisation de {selected_platform.value}")
            
            # Sélection de la qualité
            print(f"\n🎥 QUALITÉ VIDÉO:")
            qualities = [Quality.MEDIUM, Quality.HIGH]
            for i, quality in enumerate(qualities, 1):
                print(f"   {i}. {quality.value.upper()}")
            
            quality_choice = input("Choisissez la qualité (1-2): ").strip()
            try:
                selected_quality = qualities[int(quality_choice) - 1]
            except (ValueError, IndexError):
                selected_quality = Quality.MEDIUM
            
            # Options avancées
            print(f"\n⚙️ OPTIONS AVANCÉES:")
            voice_gender = VoiceGender.FEMALE
            add_music = True
            add_subtitles = True
            
            voice_choice = input("Voix féminine (f) ou masculine (m)? [f]: ").strip().lower()
            if voice_choice == 'm':
                voice_gender = VoiceGender.MALE
            
            music_choice = input("Ajouter de la musique? (o/n) [o]: ").strip().lower()
            if music_choice == 'n':
                add_music = False
            
            subtitle_choice = input("Ajouter des sous-titres? (o/n) [o]: ").strip().lower()
            if subtitle_choice == 'n':
                add_subtitles = False
            
            # Création de la requête
            request = CreationRequest(
                keyword=keyword,
                platform=selected_platform,
                quality=selected_quality,
                voice_gender=voice_gender,
                add_music=add_music,
                add_subtitles=add_subtitles,
                style="engaging"
            )
            
            print(f"\n🚀 CRÉATION EN COURS...")
            print(f"📱 Plateforme: {request.platform.value}")
            print(f"🎯 Mot-clé: {request.keyword}")
            print(f"🎥 Qualité: {request.quality.value}")
            print()
            
            # Création de la vidéo
            result = await self.create_professional_video(request)
            
            # Affichage des résultats
            self._display_results(result)
            
        except KeyboardInterrupt:
            print("\n\n👋 Création annulée par l'utilisateur")
        except Exception as e:
            logger.error(f"Erreur en mode interactif: {e}")
            print(f"\n❌ Erreur: {e}")
    
    def _display_results(self, result: Dict[str, Any]):
        """Affiche les résultats de création"""
        print("\n🎉 VIDÉO CRÉÉE AVEC SUCCÈS!")
        print("=" * 50)
        
        metadata = result["metadata"]
        
        print(f"📁 Vidéo: {result['video_path']}")
        print(f"🎤 Audio: {result['audio_path']}")
        
        if result.get('subtitle_path'):
            print(f"📝 Sous-titres: {result['subtitle_path']}")
        
        if result.get('thumbnail_path'):
            print(f"🎨 Thumbnail: {result['thumbnail_path']}")
        
        print(f"\n📊 MÉTADONNÉES:")
        print(f"   ⏱️ Durée: {metadata['duration']}s")
        print(f"   📦 Taille: {metadata['file_size_mb']} MB")
        print(f"   🕐 Temps de traitement: {metadata['processing_time']}s")
        print(f"   📱 Plateforme: {metadata['platform']}")
        print(f"   🎯 Titre: {metadata['script_data'].get('TITRE', 'N/A')}")
        
        # Statistiques globales
        print(f"\n📈 STATISTIQUES GLOBALES:")
        print(f"   🎬 Vidéos créées: {self.stats['videos_created']}")
        print(f"   ⏱️ Temps total: {self.stats['total_processing_time']:.1f}s")
        print(f"   📱 Plateformes utilisées: {', '.join(self.stats['platforms_used'])}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Retourne le statut du système"""
        return {
            "version": "2.0",
            "uptime": time.time() - self.stats["start_time"],
            "stats": self.stats,
            "retry_stats": retry_manager.get_stats(),
            "config_stats": self.config_manager.get_stats(),
            "supported_platforms": [p.value for p in self.format_manager.get_supported_platforms()]
        }

async def main():
    """Point d'entrée principal"""
    parser = argparse.ArgumentParser(description="Professional Video Creator v2.0")
    parser.add_argument("--mode", choices=["interactive", "cli"], default="interactive",
                       help="Mode d'exécution")
    parser.add_argument("--keyword", help="Mot-clé pour la vidéo")
    parser.add_argument("--platform", choices=["youtube", "tiktok", "instagram", "youtube_short"],
                       help="Plateforme cible")
    parser.add_argument("--quality", choices=["medium", "high"], default="medium",
                       help="Qualité vidéo")
    parser.add_argument("--config", default="config.yaml", help="Fichier de configuration")
    parser.add_argument("--status", action="store_true", help="Affiche le statut du système")
    
    args = parser.parse_args()
    
    try:
        creator = ProfessionalVideoCreator(args.config)
        
        if args.status:
            status = creator.get_system_status()
            print("🔧 STATUT DU SYSTÈME")
            print("=" * 30)
            for key, value in status.items():
                print(f"{key}: {value}")
            return
        
        if args.mode == "interactive":
            await creator.interactive_mode()
        elif args.mode == "cli" and args.keyword and args.platform:
            request = CreationRequest(
                keyword=args.keyword,
                platform=Platform(args.platform),
                quality=Quality(args.quality)
            )
            result = await creator.create_professional_video(request)
            creator._display_results(result)
        else:
            print("Usage: python professional_video_creator.py --help")
    
    except KeyboardInterrupt:
        print("\n👋 Au revoir!")
    except Exception as e:
        logger.critical(f"Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
