#!/usr/bin/env python3
"""
test_all_tts.py - Test et comparaison de tous les systèmes TTS disponibles
"""

import os
import sys
import logging
import time
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def print_header():
    """Print test header."""
    print("🎤" * 25)
    print("🔊 TEST COMPLET DES SYSTÈMES TTS RÉALISTES 🔊")
    print("🎤" * 25)
    print()
    print("Ce script teste tous les systèmes TTS disponibles :")
    print("🌟 ElevenLabs - Ultra-réaliste avec clonage vocal")
    print("🤖 OpenAI TTS - Haute qualité, bon rapport qualité/prix")
    print("🏢 Azure TTS - Professionnel avec SSML")
    print("✨ Minimax TTS - Alternative premium")
    print("🔄 gTTS - Fallback gratuit")
    print()

def check_tts_availability():
    """Check availability of all TTS systems."""
    print("🔍 VÉRIFICATION DES SYSTÈMES TTS:")
    print("-" * 50)
    
    systems = {}
    
    # ElevenLabs
    try:
        from elevenlabs_tts import ElevenLabsTTS
        api_key = os.getenv('ELEVENLABS_API_KEY')
        if api_key:
            tts = ElevenLabsTTS()
            systems['elevenlabs'] = tts.available
            status = "✅ Configuré" if tts.available else "⚠️ Erreur API"
        else:
            systems['elevenlabs'] = False
            status = "⚠️ API key manquante"
        print(f"🌟 ElevenLabs: {status}")
    except ImportError:
        systems['elevenlabs'] = False
        print("🌟 ElevenLabs: ❌ Module non trouvé")
    
    # OpenAI TTS
    try:
        from openai_tts import OpenAITTS
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key:
            tts = OpenAITTS()
            systems['openai'] = tts.available
            status = "✅ Configuré" if tts.available else "⚠️ Erreur API"
        else:
            systems['openai'] = False
            status = "⚠️ API key manquante"
        print(f"🤖 OpenAI TTS: {status}")
    except ImportError:
        systems['openai'] = False
        print("🤖 OpenAI TTS: ❌ Module non trouvé")
    
    # Azure TTS
    try:
        from azure_tts import AzureTTS
        api_key = os.getenv('AZURE_SPEECH_KEY')
        if api_key:
            tts = AzureTTS()
            systems['azure'] = tts.available
            status = "✅ Configuré" if tts.available else "⚠️ Erreur API"
        else:
            systems['azure'] = False
            status = "⚠️ Credentials manquants"
        print(f"🏢 Azure TTS: {status}")
    except ImportError:
        systems['azure'] = False
        print("🏢 Azure TTS: ❌ Module non trouvé")
    
    # Minimax TTS
    try:
        from minimax_tts import MinimaxTTS
        api_key = os.getenv('MINIMAX_API_KEY')
        group_id = os.getenv('MINIMAX_GROUP_ID')
        if api_key and group_id:
            tts = MinimaxTTS()
            systems['minimax'] = tts.available
            status = "✅ Configuré" if tts.available else "⚠️ Erreur API"
        else:
            systems['minimax'] = False
            status = "⚠️ Credentials manquants"
        print(f"✨ Minimax TTS: {status}")
    except ImportError:
        systems['minimax'] = False
        print("✨ Minimax TTS: ❌ Module non trouvé")
    
    # gTTS (always available)
    try:
        from gtts import gTTS
        systems['gtts'] = True
        print("🔄 gTTS: ✅ Disponible (fallback)")
    except ImportError:
        systems['gtts'] = False
        print("🔄 gTTS: ❌ Non disponible")
    
    print()
    return systems

def test_tts_system(tts_name, tts_system, test_text, keyword):
    """Test a specific TTS system."""
    print(f"🧪 Test de {tts_name.title()}...")
    
    try:
        start_time = time.time()
        
        # Create test script
        test_script = {
            "title": f"Test {tts_name.title()}",
            "introduction": test_text,
            "key_point_1": "Premier point de test avec expression naturelle.",
            "key_point_2": "Deuxième point démontrant la qualité vocale.",
            "key_point_3": "Troisième point pour évaluer la fluidité.",
            "conclusion": f"Fin du test {tts_name.title()}. Merci d'avoir écouté!"
        }
        
        # Generate audio
        audio_path = tts_system.generate_enhanced_speech(test_script, f"{keyword}_{tts_name}")
        
        generation_time = time.time() - start_time
        
        if audio_path and Path(audio_path).exists():
            file_size = Path(audio_path).stat().st_size / 1024  # KB
            print(f"   ✅ Succès en {generation_time:.1f}s - {file_size:.1f} KB")
            return {
                'success': True,
                'time': generation_time,
                'size': file_size,
                'path': audio_path
            }
        else:
            print(f"   ❌ Échec après {generation_time:.1f}s")
            return {'success': False, 'time': generation_time}
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return {'success': False, 'error': str(e)}

def test_audio_manager_integration():
    """Test the integrated AudioManager."""
    print("🔗 TEST D'INTÉGRATION AUDIOMANAGER:")
    print("-" * 40)
    
    try:
        from audio_manager import AudioManager
        
        audio_manager = AudioManager()
        
        # Show available systems
        if hasattr(audio_manager, 'tts_systems'):
            available = list(audio_manager.tts_systems.keys())
            print(f"Systèmes intégrés: {available}")
        else:
            print("Aucun système TTS premium intégré")
        
        # Test generation
        test_script = {
            "title": "Test AudioManager Intégré",
            "introduction": "Test du gestionnaire audio avec sélection automatique du meilleur TTS.",
            "key_point_1": "Le système choisit automatiquement le TTS de meilleure qualité.",
            "key_point_2": "En cas d'échec, il passe au système suivant.",
            "key_point_3": "gTTS est utilisé en dernier recours.",
            "conclusion": "Test d'intégration terminé avec succès!"
        }
        
        print("🔄 Génération avec AudioManager...")
        start_time = time.time()
        
        audio_path = audio_manager.generate_audio(test_script, "integration_test")
        
        generation_time = time.time() - start_time
        
        if audio_path:
            file_size = Path(audio_path).stat().st_size / 1024
            print(f"✅ AudioManager: Succès en {generation_time:.1f}s - {file_size:.1f} KB")
            print(f"📁 Fichier: {audio_path}")
            return True
        else:
            print(f"❌ AudioManager: Échec après {generation_time:.1f}s")
            return False
            
    except Exception as e:
        print(f"❌ Erreur AudioManager: {e}")
        return False

def show_setup_instructions():
    """Show setup instructions for all TTS systems."""
    print("\n📋 INSTRUCTIONS DE CONFIGURATION:")
    print("=" * 50)
    
    print("\n🌟 ElevenLabs (Recommandé - Ultra-réaliste):")
    print("   1. Créez un compte sur https://elevenlabs.io/")
    print("   2. Obtenez votre API key")
    print("   3. export ELEVENLABS_API_KEY='votre_cle'")
    print("   💰 Prix: ~$0.30 par 1000 caractères")
    print("   🎯 Idéal pour: Contenus premium, voix émotionnelles")
    
    print("\n🤖 OpenAI TTS (Excellent rapport qualité/prix):")
    print("   1. Compte OpenAI sur https://platform.openai.com/")
    print("   2. Générez une API key")
    print("   3. export OPENAI_API_KEY='votre_cle'")
    print("   💰 Prix: ~$0.015 par 1000 caractères")
    print("   🎯 Idéal pour: Production régulière, qualité constante")
    
    print("\n🏢 Azure TTS (Professionnel):")
    print("   1. Compte Azure sur https://portal.azure.com/")
    print("   2. Créez un service Speech")
    print("   3. export AZURE_SPEECH_KEY='votre_cle'")
    print("   4. export AZURE_SPEECH_REGION='votre_region'")
    print("   💰 Prix: ~$4 par million de caractères")
    print("   🎯 Idéal pour: Entreprises, contrôle SSML avancé")
    
    print("\n✨ Minimax TTS (Alternative):")
    print("   1. Compte sur https://www.minimaxi.com/")
    print("   2. Obtenez API key et Group ID")
    print("   3. export MINIMAX_API_KEY='votre_cle'")
    print("   4. export MINIMAX_GROUP_ID='votre_group_id'")
    print("   💰 Prix: Variable selon utilisation")
    print("   🎯 Idéal pour: Voix chinoises, clonage vocal")

def main():
    """Main test function."""
    print_header()
    
    # Check availability
    systems = check_tts_availability()
    
    available_premium = [name for name, available in systems.items() 
                        if available and name != 'gtts']
    
    if not available_premium:
        print("⚠️ Aucun système TTS premium configuré!")
        show_setup_instructions()
        return
    
    print(f"🎯 Systèmes premium disponibles: {available_premium}")
    print()
    
    if len(sys.argv) > 1:
        # Test with specific text
        test_text = " ".join(sys.argv[1:])
    else:
        test_text = "Bonjour et bienvenue dans ce test de qualité vocale. Cette phrase permet d'évaluer le réalisme et l'expression naturelle de la synthèse vocale."
    
    print(f"📝 Texte de test: {test_text}")
    print()
    
    # Test each available system
    results = {}
    
    for system_name in ['elevenlabs', 'openai', 'azure', 'minimax']:
        if systems.get(system_name):
            try:
                if system_name == 'elevenlabs':
                    from elevenlabs_tts import ElevenLabsTTS
                    tts_system = ElevenLabsTTS()
                elif system_name == 'openai':
                    from openai_tts import OpenAITTS
                    tts_system = OpenAITTS()
                elif system_name == 'azure':
                    from azure_tts import AzureTTS
                    tts_system = AzureTTS()
                elif system_name == 'minimax':
                    from minimax_tts import MinimaxTTS
                    tts_system = MinimaxTTS()
                
                if tts_system.available:
                    results[system_name] = test_tts_system(
                        system_name, tts_system, test_text, "tts_comparison"
                    )
                
            except Exception as e:
                print(f"❌ Erreur avec {system_name}: {e}")
    
    # Test AudioManager integration
    print()
    integration_success = test_audio_manager_integration()
    
    # Show results summary
    print("\n📊 RÉSUMÉ DES TESTS:")
    print("-" * 30)
    
    successful_tests = [name for name, result in results.items() 
                       if result.get('success')]
    
    if successful_tests:
        print("✅ Systèmes fonctionnels:")
        for name in successful_tests:
            result = results[name]
            print(f"   • {name.title()}: {result['time']:.1f}s, {result['size']:.1f} KB")
        
        # Recommend best option
        fastest = min(successful_tests, key=lambda x: results[x]['time'])
        print(f"\n🏆 Plus rapide: {fastest.title()}")
        
        if 'elevenlabs' in successful_tests:
            print("🌟 Recommandé: ElevenLabs (meilleure qualité)")
        elif 'openai' in successful_tests:
            print("🤖 Recommandé: OpenAI (bon rapport qualité/prix)")
    
    print(f"\n🔗 AudioManager: {'✅ Fonctionnel' if integration_success else '❌ Problème'}")
    
    print("\n🎉 Tests terminés!")
    print("💡 Utilisez le système recommandé pour vos vidéos YouTube")

if __name__ == "__main__":
    main()
