"""
Advanced configuration system with validation and environment support.

This module provides a robust configuration system that supports:
- Environment variable overrides
- Configuration validation
- Type safety
- Default values
- Configuration profiles (dev, prod, test)
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from enum import Enum

try:
    from pydantic import BaseSettings, Field, validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False


class Environment(str, Enum):
    """Environment types."""
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"


class LogLevel(str, Enum):
    """Log levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


if PYDANTIC_AVAILABLE:
    class DatabaseConfig(BaseSettings):
        """Database configuration."""
        host: str = Field(default="localhost", env="DB_HOST")
        port: int = Field(default=5432, env="DB_PORT")
        name: str = Field(default="video_creation", env="DB_NAME")
        user: str = Field(default="postgres", env="DB_USER")
        password: str = Field(default="", env="DB_PASSWORD")
        pool_size: int = Field(default=10, env="DB_POOL_SIZE")
        
        class Config:
            env_prefix = "DB_"

    class RedisConfig(BaseSettings):
        """Redis configuration."""
        host: str = Field(default="localhost", env="REDIS_HOST")
        port: int = Field(default=6379, env="REDIS_PORT")
        db: int = Field(default=0, env="REDIS_DB")
        password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
        max_connections: int = Field(default=20, env="REDIS_MAX_CONNECTIONS")
        
        class Config:
            env_prefix = "REDIS_"

    class APIConfig(BaseSettings):
        """API configuration."""
        pixabay_api_key: Optional[str] = Field(default=None, env="PIXABAY_API_KEY")
        openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
        elevenlabs_api_key: Optional[str] = Field(default=None, env="ELEVENLABS_API_KEY")
        azure_speech_key: Optional[str] = Field(default=None, env="AZURE_SPEECH_KEY")
        azure_speech_region: Optional[str] = Field(default=None, env="AZURE_SPEECH_REGION")
        
        class Config:
            env_prefix = "API_"

    class PerformanceConfig(BaseSettings):
        """Performance configuration."""
        max_concurrent_videos: int = Field(default=3, env="MAX_CONCURRENT_VIDEOS")
        max_video_duration: int = Field(default=600, env="MAX_VIDEO_DURATION")  # seconds
        cache_ttl: int = Field(default=3600, env="CACHE_TTL")  # seconds
        request_timeout: int = Field(default=300, env="REQUEST_TIMEOUT")  # seconds
        max_retries: int = Field(default=3, env="MAX_RETRIES")
        
        class Config:
            env_prefix = "PERF_"

    class SecurityConfig(BaseSettings):
        """Security configuration."""
        secret_key: str = Field(default="dev-secret-key", env="SECRET_KEY")
        api_rate_limit: int = Field(default=100, env="API_RATE_LIMIT")  # requests per hour
        max_file_size: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
        allowed_file_types: List[str] = Field(
            default=["mp4", "mp3", "wav", "jpg", "png"], 
            env="ALLOWED_FILE_TYPES"
        )
        
        class Config:
            env_prefix = "SECURITY_"

    class ApplicationConfig(BaseSettings):
        """Main application configuration."""
        environment: Environment = Field(default=Environment.DEVELOPMENT, env="ENVIRONMENT")
        debug: bool = Field(default=True, env="DEBUG")
        log_level: LogLevel = Field(default=LogLevel.INFO, env="LOG_LEVEL")
        base_dir: Path = Field(default_factory=lambda: Path(__file__).parent.parent)
        output_dir: Path = Field(default_factory=lambda: Path(__file__).parent.parent / "output")
        
        # Sub-configurations
        database: DatabaseConfig = Field(default_factory=DatabaseConfig)
        redis: RedisConfig = Field(default_factory=RedisConfig)
        apis: APIConfig = Field(default_factory=APIConfig)
        performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
        security: SecurityConfig = Field(default_factory=SecurityConfig)
        
        @validator('output_dir')
        def create_output_dir(cls, v):
            """Ensure output directory exists."""
            v.mkdir(parents=True, exist_ok=True)
            return v
        
        class Config:
            env_file = ".env"
            env_file_encoding = "utf-8"
            case_sensitive = False

else:
    # Fallback configuration without Pydantic
    @dataclass
    class DatabaseConfig:
        host: str = "localhost"
        port: int = 5432
        name: str = "video_creation"
        user: str = "postgres"
        password: str = ""
        pool_size: int = 10

    @dataclass
    class RedisConfig:
        host: str = "localhost"
        port: int = 6379
        db: int = 0
        password: Optional[str] = None
        max_connections: int = 20

    @dataclass
    class APIConfig:
        pixabay_api_key: Optional[str] = None
        openai_api_key: Optional[str] = None
        elevenlabs_api_key: Optional[str] = None
        azure_speech_key: Optional[str] = None
        azure_speech_region: Optional[str] = None

    @dataclass
    class PerformanceConfig:
        max_concurrent_videos: int = 3
        max_video_duration: int = 600
        cache_ttl: int = 3600
        request_timeout: int = 300
        max_retries: int = 3

    @dataclass
    class SecurityConfig:
        secret_key: str = "dev-secret-key"
        api_rate_limit: int = 100
        max_file_size: int = 100 * 1024 * 1024
        allowed_file_types: List[str] = field(
            default_factory=lambda: ["mp4", "mp3", "wav", "jpg", "png"]
        )

    @dataclass
    class ApplicationConfig:
        environment: str = "development"
        debug: bool = True
        log_level: str = "INFO"
        base_dir: Path = field(default_factory=lambda: Path(__file__).parent.parent)
        output_dir: Path = field(default_factory=lambda: Path(__file__).parent.parent / "output")
        
        database: DatabaseConfig = field(default_factory=DatabaseConfig)
        redis: RedisConfig = field(default_factory=RedisConfig)
        apis: APIConfig = field(default_factory=APIConfig)
        performance: PerformanceConfig = field(default_factory=PerformanceConfig)
        security: SecurityConfig = field(default_factory=SecurityConfig)


class ConfigManager:
    """Configuration manager with environment support."""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_path = config_path
        self._config: Optional[ApplicationConfig] = None
        self._load_config()
    
    def _load_config(self):
        """Load configuration from file and environment."""
        if PYDANTIC_AVAILABLE:
            self._config = ApplicationConfig()
        else:
            self._config = ApplicationConfig()
            self._load_from_env()
    
    def _load_from_env(self):
        """Load configuration from environment variables (fallback)."""
        if not self._config:
            return
        
        # Load environment variables manually for fallback
        env_mappings = {
            'ENVIRONMENT': 'environment',
            'DEBUG': 'debug',
            'LOG_LEVEL': 'log_level',
            'PIXABAY_API_KEY': ('apis', 'pixabay_api_key'),
            'OPENAI_API_KEY': ('apis', 'openai_api_key'),
            'MAX_CONCURRENT_VIDEOS': ('performance', 'max_concurrent_videos'),
            'CACHE_TTL': ('performance', 'cache_ttl'),
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                if isinstance(config_path, tuple):
                    # Nested configuration
                    obj = getattr(self._config, config_path[0])
                    setattr(obj, config_path[1], self._convert_value(value))
                else:
                    # Top-level configuration
                    setattr(self._config, config_path, self._convert_value(value))
    
    def _convert_value(self, value: str) -> Union[str, int, bool]:
        """Convert string value to appropriate type."""
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        try:
            return int(value)
        except ValueError:
            return value
    
    @property
    def config(self) -> ApplicationConfig:
        """Get current configuration."""
        if self._config is None:
            raise RuntimeError("Configuration not loaded")
        return self._config
    
    def reload(self):
        """Reload configuration."""
        self._load_config()
    
    def get_directories(self) -> Dict[str, Path]:
        """Get all output directories."""
        base_output = self.config.output_dir
        directories = {
            "videos": base_output / "videos",
            "audio": base_output / "audio",
            "thumbnails": base_output / "thumbnails",
            "scripts": base_output / "scripts",
            "transcripts": base_output / "transcripts",
            "temp": base_output / "temp",
            "cache": base_output / "cache",
            "logs": base_output / "logs"
        }
        
        # Create directories if they don't exist
        for directory in directories.values():
            directory.mkdir(parents=True, exist_ok=True)
        
        return directories
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        # Check required API keys for production
        if self.config.environment == Environment.PRODUCTION:
            if not self.config.apis.pixabay_api_key:
                issues.append("Pixabay API key is required for production")
        
        # Check directory permissions
        try:
            test_file = self.config.output_dir / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
        except Exception as e:
            issues.append(f"Cannot write to output directory: {e}")
        
        return issues


# Global configuration instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get global configuration manager."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config() -> ApplicationConfig:
    """Get current configuration."""
    return get_config_manager().config


def reload_config():
    """Reload configuration."""
    get_config_manager().reload()
