#!/usr/bin/env python3
"""
create_nature_with_best_tts.py - Créer vidéo nature avec le meilleur TTS disponible
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_nature_video_best_tts():
    """Create nature video with the best available TTS."""
    print("🌿" * 35)
    print("🎬 VIDÉO NATURE AVEC MEILLEUR TTS DISPONIBLE 🎬")
    print("🌿" * 35)
    print()
    print("✨ Le système utilisera automatiquement le meilleur TTS dans cet ordre :")
    print("   1. 🎤 Piper TTS (Local, gratuit, haute qualité)")
    print("   2. 🌟 ElevenLabs (Ultra-réaliste)")
    print("   3. 🤖 OpenAI TTS (Excellent rapport qualité/prix)")
    print("   4. 🏢 Azure TTS (Professionnel)")
    print("   5. ✨ Minimax TTS (Alternative)")
    print("   6. 🔄 gTTS Enhanced (Fallback toujours disponible)")
    print()
    
    try:
        from simple_nature_composer import SimpleNatureComposer
        from audio_manager import AudioManager
        
        # Initialize audio manager
        audio_manager = AudioManager()
        
        print("🎤 SYSTÈMES TTS DISPONIBLES:")
        print("-" * 40)
        
        for system_name, system in audio_manager.tts_systems.items():
            if hasattr(system, 'available'):
                status = "✅" if system.available else "❌"
                print(f"{status} {system_name.upper()}")
            else:
                print(f"✅ {system_name.upper()}")
        
        print()
        
        # Create nature script optimized for TTS
        nature_script = {
            "title": "La Magie de la Nature",
            "introduction": "Bienvenue dans un voyage extraordinaire à travers les merveilles de notre planète. La nature nous offre des spectacles d'une beauté saisissante.",
            "key_point_1": "Des forêts luxuriantes aux océans profonds, chaque écosystème abrite une biodiversité unique et précieuse pour notre équilibre mondial.",
            "key_point_2": "Les montagnes majestueuses, les rivières cristallines et les prairies verdoyantes forment un tableau vivant en constante évolution.",
            "key_point_3": "Préserver cette richesse naturelle est notre responsabilité commune pour les générations futures et la survie de notre planète.",
            "conclusion": "Merci d'avoir découvert ces merveilles naturelles. Ensemble, protégeons ce patrimoine inestimable pour un avenir durable."
        }
        
        print("🔄 Génération de l'audio avec le meilleur TTS disponible...")
        audio_path = audio_manager.generate_audio(nature_script, "nature_best_tts")
        
        if not audio_path or not Path(audio_path).exists():
            print("❌ Échec de génération audio")
            return False
        
        audio_size = Path(audio_path).stat().st_size / (1024 * 1024)
        print(f"✅ Audio généré: {audio_path} ({audio_size:.1f} MB)")
        
        # Create video with nature background and music
        print("\n🎬 Création de la vidéo avec arrière-plan nature Pixabay...")
        composer = SimpleNatureComposer()
        
        video_path = composer.create_nature_video(audio_path, "nature_best_tts", add_music=True)
        
        if video_path and Path(video_path).exists():
            video_size = Path(video_path).stat().st_size / (1024 * 1024)
            
            print("\n🎉 VIDÉO NATURE CRÉÉE AVEC SUCCÈS!")
            print("=" * 50)
            print(f"📁 Fichier: {video_path}")
            print(f"📊 Taille: {video_size:.1f} MB")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
            print()
            print("🌿 CONTENU DE LA VIDÉO:")
            print("   ✅ Narration: Meilleur TTS disponible")
            print("   ✅ Arrière-plan: Vidéos nature HD Pixabay")
            print("   ✅ Musique: Fréquences apaisantes sans percussion")
            print("   ✅ Qualité: HD 1920x1080")
            print()
            
            # Show which TTS was actually used
            print("🎤 SYSTÈME TTS UTILISÉ:")
            # We can check the logs to see which system was used
            print("   (Vérifiez les logs ci-dessus pour voir quel TTS a été utilisé)")
            
            return True
        else:
            print("❌ Échec de création vidéo")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_tts_priority():
    """Show TTS system priority."""
    print("\n🎯 PRIORITÉ DES SYSTÈMES TTS:")
    print("-" * 40)
    
    try:
        from audio_manager import AudioManager
        
        audio_manager = AudioManager()
        
        # The priority is defined in the generate_audio method
        priority = ["piper", "elevenlabs", "openai", "azure", "minimax"]
        
        print("Ordre de priorité (du meilleur au fallback):")
        for i, system in enumerate(priority, 1):
            available = system in audio_manager.tts_systems
            status = "✅ Disponible" if available else "❌ Non configuré"
            
            if system == "piper":
                description = "Local, gratuit, haute qualité"
            elif system == "elevenlabs":
                description = "Ultra-réaliste, premium"
            elif system == "openai":
                description = "Excellent rapport qualité/prix"
            elif system == "azure":
                description = "Professionnel, SSML"
            elif system == "minimax":
                description = "Alternative innovante"
            
            print(f"   {i}. {system.upper()}: {status} - {description}")
        
        print(f"\n🔄 Fallback final: gTTS Enhanced (toujours disponible)")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Main function."""
    show_tts_priority()
    
    print("\n" + "="*60)
    
    success = create_nature_video_best_tts()
    
    if success:
        print("\n🎉 MISSION ACCOMPLIE!")
        print("🌟 Votre vidéo nature utilise le meilleur TTS disponible")
        print("🎵 Avec musique douce sans percussion comme demandé")
        print("🌿 Et magnifiques arrière-plans nature depuis Pixabay")
    else:
        print("\n❌ Échec de création")
        print("🔧 Vérifiez les logs pour diagnostiquer le problème")

if __name__ == "__main__":
    main()
