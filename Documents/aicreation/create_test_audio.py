#!/usr/bin/env python3
"""
Créateur d'audio de test pour SkyReels-A1
"""

import os
from pathlib import Path
from gtts import gTTS

def create_test_audio():
    """Crée un fichier audio de test pour l'avatar journaliste"""
    
    # Texte de test pour le journaliste
    text = """
    Bonjour et bienvenue dans cette édition spéciale d'actualités.
    Je suis votre présentateur virtuel, créé grâce à la technologie SkyReels-A1.
    Aujourd'hui, nous explorons les dernières avancées en intelligence artificielle.
    Cette démonstration illustre parfaitement les capacités d'animation de portraits en temps réel.
    Merci de votre attention et à très bientôt pour de nouvelles actualités technologiques.
    """
    
    # Créer le dossier assets/driving_audio s'il n'existe pas
    audio_dir = Path("assets/driving_audio")
    audio_dir.mkdir(parents=True, exist_ok=True)
    
    # Générer l'audio avec gTTS
    tts = gTTS(text=text.strip(), lang='fr', slow=False)
    
    # Sauvegarder
    audio_path = audio_dir / "journaliste_test.wav"
    temp_mp3 = audio_dir / "temp.mp3"
    
    # Sauvegarder en MP3 puis convertir en WAV
    tts.save(str(temp_mp3))
    
    # Convertir MP3 vers WAV avec ffmpeg
    import subprocess
    cmd = [
        'ffmpeg', '-y',
        '-i', str(temp_mp3),
        '-acodec', 'pcm_s16le',
        '-ar', '16000',
        str(audio_path)
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        temp_mp3.unlink()  # Supprimer le fichier temporaire
        print(f"✅ Audio créé: {audio_path}")
        return str(audio_path)
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur conversion: {e}")
        # Garder le MP3 si la conversion échoue
        mp3_path = audio_dir / "journaliste_test.mp3"
        temp_mp3.rename(mp3_path)
        print(f"✅ Audio MP3 créé: {mp3_path}")
        return str(mp3_path)

if __name__ == "__main__":
    create_test_audio()
