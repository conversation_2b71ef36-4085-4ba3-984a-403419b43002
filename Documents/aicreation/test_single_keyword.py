#!/usr/bin/env python3
"""
test_single_keyword.py - Test script for a single keyword
"""

import sys
import logging
from enhanced_auto_youtube import EnhancedAutoYouTube

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Test with a single keyword."""
    keyword = sys.argv[1] if len(sys.argv) > 1 else "renewable energy"
    
    print(f"Testing with keyword: {keyword}")
    
    # Initialize system
    auto_youtube = EnhancedAutoYouTube()
    
    # Process single keyword
    success = auto_youtube.process_keyword(keyword)
    
    if success:
        print(f"✅ Successfully processed '{keyword}'")
        print(f"📁 Check output/ directory for generated files")
    else:
        print(f"❌ Failed to process '{keyword}'")
    
    return success

if __name__ == "__main__":
    main()
