#!/usr/bin/env python3
"""
add_music_to_video.py - Ajouter de la musique douce à une vidéo existante
"""

import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def add_music_to_nature_video():
    """Add soft music to the nature video."""
    print("🎵" * 30)
    print("🎶 AJOUT DE MUSIQUE DOUCE À LA VIDÉO NATURE 🎶")
    print("🎵" * 30)
    print()
    
    try:
        from music_manager import MusicManager
        
        # Initialize music manager
        music_manager = MusicManager()
        
        # Check if nature video exists
        video_path = "output/videos/nature.mp4"
        
        if not Path(video_path).exists():
            print(f"❌ Vidéo non trouvée: {video_path}")
            print("💡 Créez d'abord une vidéo avec: python create_nature_video.py")
            return
        
        print(f"📁 Vidéo trouvée: {video_path}")
        
        # Get video info
        file_size = Path(video_path).stat().st_size / (1024 * 1024)
        print(f"📊 Taille actuelle: {file_size:.1f} MB")
        
        print()
        print("🎼 Ajout de musique zen douce (fréquences 285Hz, 417Hz, 639Hz)...")
        print("⏳ Cela peut prendre 1-2 minutes...")
        
        # Add zen music (perfect for nature)
        result_path = music_manager.add_music_to_video(video_path, "zen")
        
        if result_path and Path(result_path).exists():
            new_file_size = Path(result_path).stat().st_size / (1024 * 1024)
            print()
            print("✅ MUSIQUE AJOUTÉE AVEC SUCCÈS!")
            print(f"📁 Vidéo avec musique: {result_path}")
            print(f"📊 Nouvelle taille: {new_file_size:.1f} MB")
            print(f"🌐 Voir: file://{Path(result_path).absolute()}")
            print()
            print("🎵 CARACTÉRISTIQUES DE LA MUSIQUE:")
            print("   • Style: Méditation zen")
            print("   • Fréquences: 285Hz (régénération), 417Hz (changement), 639Hz (connexion)")
            print("   • Volume: Doux et apaisant")
            print("   • Aucune percussion - Pure harmonie")
            print()
            print("🌿 Votre vidéo nature est maintenant enrichie d'une ambiance sonore apaisante!")
            
        else:
            print("❌ Erreur lors de l'ajout de la musique")
            
    except ImportError:
        print("❌ Module music_manager non trouvé")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_different_music_styles():
    """Test different music styles on the nature video."""
    print("🎼 TEST DE DIFFÉRENTS STYLES MUSICAUX:")
    print("-" * 50)
    
    try:
        from music_manager import MusicManager
        
        music_manager = MusicManager()
        
        video_path = "output/videos/nature.mp4"
        
        if not Path(video_path).exists():
            print(f"❌ Vidéo non trouvée: {video_path}")
            return
        
        styles = ["forest", "ocean", "mountain", "zen"]
        
        for style in styles:
            print(f"\n🎵 Test style: {style}")
            config = music_manager.nature_music_configs[style]
            print(f"   📝 {config['description']}")
            print(f"   🎶 Fréquences: {config['harmonics']}")
            
            # Create a copy for testing
            test_video = f"output/videos/nature_{style}_test.mp4"
            
            # Copy original
            import shutil
            shutil.copy2(video_path, test_video)
            
            # Add music
            result = music_manager.add_music_to_video(test_video, style)
            
            if result and Path(result).exists():
                file_size = Path(result).stat().st_size / (1024 * 1024)
                print(f"   ✅ Créé: {result} ({file_size:.1f} MB)")
            else:
                print(f"   ❌ Échec pour {style}")
        
        print(f"\n🎯 Tous les styles testés! Vérifiez le dossier output/videos/")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] == "test-styles":
        test_different_music_styles()
    else:
        add_music_to_nature_video()

if __name__ == "__main__":
    main()
