"""
Custom exceptions for the video creation system.

This module defines a hierarchy of exceptions that provide clear error handling
and debugging capabilities throughout the application.
"""

from typing import Optional, Dict, Any
import traceback
from datetime import datetime


class VideoCreationError(Exception):
    """Base exception for all video creation errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.context = context or {}
        self.original_exception = original_exception
        self.timestamp = datetime.utcnow()
        self.traceback = traceback.format_exc() if original_exception else None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging/serialization."""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback
        }


class ConfigurationError(VideoCreationError):
    """Raised when there's a configuration issue."""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)
        self.config_key = config_key


class TTSError(VideoCreationError):
    """Raised when TTS processing fails."""
    
    def __init__(self, message: str, provider: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="TTS_ERROR", **kwargs)
        self.provider = provider


class VideoProcessingError(VideoCreationError):
    """Raised when video processing fails."""
    
    def __init__(self, message: str, stage: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="VIDEO_ERROR", **kwargs)
        self.stage = stage


class AudioProcessingError(VideoCreationError):
    """Raised when audio processing fails."""
    
    def __init__(self, message: str, stage: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="AUDIO_ERROR", **kwargs)
        self.stage = stage


class APIError(VideoCreationError):
    """Raised when external API calls fail."""
    
    def __init__(
        self, 
        message: str, 
        api_name: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, error_code="API_ERROR", **kwargs)
        self.api_name = api_name
        self.status_code = status_code


class ScriptGenerationError(VideoCreationError):
    """Raised when script generation fails."""
    
    def __init__(self, message: str, model: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="SCRIPT_ERROR", **kwargs)
        self.model = model


class ResourceNotFoundError(VideoCreationError):
    """Raised when required resources are not found."""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="RESOURCE_NOT_FOUND", **kwargs)
        self.resource_type = resource_type


class ValidationError(VideoCreationError):
    """Raised when data validation fails."""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        self.field = field


class TimeoutError(VideoCreationError):
    """Raised when operations timeout."""
    
    def __init__(self, message: str, timeout_seconds: Optional[float] = None, **kwargs):
        super().__init__(message, error_code="TIMEOUT_ERROR", **kwargs)
        self.timeout_seconds = timeout_seconds


class RateLimitError(VideoCreationError):
    """Raised when rate limits are exceeded."""
    
    def __init__(
        self, 
        message: str, 
        retry_after: Optional[int] = None,
        api_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="RATE_LIMIT_ERROR", **kwargs)
        self.retry_after = retry_after
        self.api_name = api_name


class CircuitBreakerError(VideoCreationError):
    """Raised when circuit breaker is open."""
    
    def __init__(self, message: str, service_name: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="CIRCUIT_BREAKER_ERROR", **kwargs)
        self.service_name = service_name


# Exception handler decorator
def handle_exceptions(
    default_return=None,
    log_errors: bool = True,
    reraise: bool = False
):
    """Decorator for handling exceptions in methods."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except VideoCreationError:
                if reraise:
                    raise
                if log_errors:
                    import logging
                    logging.exception(f"Error in {func.__name__}")
                return default_return
            except Exception as e:
                error = VideoCreationError(
                    f"Unexpected error in {func.__name__}: {str(e)}",
                    original_exception=e
                )
                if reraise:
                    raise error
                if log_errors:
                    import logging
                    logging.exception(f"Unexpected error in {func.__name__}")
                return default_return
        return wrapper
    return decorator
