"""
Caching and resilience patterns implementation.

This module provides:
- In-memory and Redis caching
- Circuit breaker pattern
- Rate limiting
- Retry mechanisms
"""

import asyncio
import time
import json
import hashlib
from typing import Any, Optional, Dict, Callable, Union
from datetime import datetime, timedelta
from enum import Enum
from functools import wraps

from .interfaces import ICache, ICircuitBreaker, IRateLimiter
from .logging import get_logger, get_metrics
from .exceptions import CircuitBreakerError, RateLimitError, TimeoutError


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class MemoryCache(ICache):
    """In-memory cache implementation."""
    
    def __init__(self, default_ttl: int = 3600):
        self.default_ttl = default_ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self.logger = get_logger("cache")
        self.metrics = get_metrics()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if key not in self._cache:
            self.metrics.increment_counter("cache_miss", {"cache_type": "memory"})
            return None
        
        entry = self._cache[key]
        if entry["expires_at"] < time.time():
            del self._cache[key]
            self.metrics.increment_counter("cache_expired", {"cache_type": "memory"})
            return None
        
        self.metrics.increment_counter("cache_hit", {"cache_type": "memory"})
        return entry["value"]
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            ttl = ttl or self.default_ttl
            expires_at = time.time() + ttl
            
            self._cache[key] = {
                "value": value,
                "expires_at": expires_at,
                "created_at": time.time()
            }
            
            self.metrics.increment_counter("cache_set", {"cache_type": "memory"})
            return True
        except Exception as e:
            self.logger.error(f"Failed to set cache key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        if key in self._cache:
            del self._cache[key]
            self.metrics.increment_counter("cache_delete", {"cache_type": "memory"})
            return True
        return False
    
    async def clear(self) -> bool:
        """Clear all cache."""
        self._cache.clear()
        self.metrics.increment_counter("cache_clear", {"cache_type": "memory"})
        return True
    
    def cleanup_expired(self):
        """Remove expired entries."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._cache.items()
            if entry["expires_at"] < current_time
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            self.logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")


class CircuitBreaker(ICircuitBreaker):
    """Circuit breaker implementation."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self._failure_count = 0
        self._last_failure_time: Optional[float] = None
        self._state = CircuitBreakerState.CLOSED
        
        self.logger = get_logger("circuit_breaker")
        self.metrics = get_metrics()
    
    @property
    def state(self) -> str:
        """Get current circuit breaker state."""
        return self._state.value
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        if self._state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self._state = CircuitBreakerState.HALF_OPEN
                self.logger.info("Circuit breaker moved to HALF_OPEN state")
            else:
                self.metrics.increment_counter("circuit_breaker_blocked")
                raise CircuitBreakerError("Circuit breaker is OPEN")
        
        try:
            result = await self._execute_function(func, *args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    async def _execute_function(self, func: Callable, *args, **kwargs) -> Any:
        """Execute the function (async or sync)."""
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    
    def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit breaker."""
        return (
            self._last_failure_time is not None and
            time.time() - self._last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self):
        """Handle successful execution."""
        if self._state == CircuitBreakerState.HALF_OPEN:
            self._state = CircuitBreakerState.CLOSED
            self.logger.info("Circuit breaker moved to CLOSED state")
        
        self._failure_count = 0
        self.metrics.increment_counter("circuit_breaker_success")
    
    def _on_failure(self):
        """Handle failed execution."""
        self._failure_count += 1
        self._last_failure_time = time.time()
        
        if self._failure_count >= self.failure_threshold:
            self._state = CircuitBreakerState.OPEN
            self.logger.warning(
                f"Circuit breaker moved to OPEN state after {self._failure_count} failures"
            )
        
        self.metrics.increment_counter("circuit_breaker_failure")
    
    def reset(self):
        """Reset circuit breaker."""
        self._state = CircuitBreakerState.CLOSED
        self._failure_count = 0
        self._last_failure_time = None
        self.logger.info("Circuit breaker manually reset")


class RateLimiter(IRateLimiter):
    """Rate limiter implementation using token bucket algorithm."""
    
    def __init__(self, max_tokens: int = 100, refill_rate: float = 1.0):
        self.max_tokens = max_tokens
        self.refill_rate = refill_rate  # tokens per second
        
        self._buckets: Dict[str, Dict[str, float]] = {}
        self.logger = get_logger("rate_limiter")
        self.metrics = get_metrics()
    
    async def acquire(self, key: str) -> bool:
        """Acquire rate limit token."""
        current_time = time.time()
        
        if key not in self._buckets:
            self._buckets[key] = {
                "tokens": self.max_tokens - 1,
                "last_refill": current_time
            }
            self.metrics.increment_counter("rate_limit_acquired", {"key": key})
            return True
        
        bucket = self._buckets[key]
        
        # Refill tokens
        time_passed = current_time - bucket["last_refill"]
        tokens_to_add = time_passed * self.refill_rate
        bucket["tokens"] = min(self.max_tokens, bucket["tokens"] + tokens_to_add)
        bucket["last_refill"] = current_time
        
        # Check if token available
        if bucket["tokens"] >= 1:
            bucket["tokens"] -= 1
            self.metrics.increment_counter("rate_limit_acquired", {"key": key})
            return True
        else:
            self.metrics.increment_counter("rate_limit_rejected", {"key": key})
            return False
    
    async def get_remaining(self, key: str) -> int:
        """Get remaining tokens."""
        if key not in self._buckets:
            return self.max_tokens
        
        current_time = time.time()
        bucket = self._buckets[key]
        
        # Calculate current tokens
        time_passed = current_time - bucket["last_refill"]
        tokens_to_add = time_passed * self.refill_rate
        current_tokens = min(self.max_tokens, bucket["tokens"] + tokens_to_add)
        
        return int(current_tokens)
    
    async def reset_limit(self, key: str):
        """Reset rate limit for key."""
        if key in self._buckets:
            del self._buckets[key]
            self.logger.info(f"Rate limit reset for key: {key}")


class CacheManager:
    """High-level cache manager with multiple backends."""
    
    def __init__(self):
        self.memory_cache = MemoryCache()
        self.logger = get_logger("cache_manager")
    
    def cache_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        key_data = {
            "args": args,
            "kwargs": sorted(kwargs.items())
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def cached(self, ttl: int = 3600, key_prefix: str = ""):
        """Decorator for caching function results."""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Generate cache key
                cache_key = f"{key_prefix}:{func.__name__}:{self.cache_key(*args, **kwargs)}"
                
                # Try to get from cache
                cached_result = await self.memory_cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Store in cache
                await self.memory_cache.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator


class RetryManager:
    """Retry mechanism with exponential backoff."""
    
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        
        self.logger = get_logger("retry_manager")
        self.metrics = get_metrics()
    
    async def retry(
        self,
        func: Callable,
        *args,
        exceptions: tuple = (Exception,),
        **kwargs
    ) -> Any:
        """Retry function with exponential backoff."""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except exceptions as e:
                last_exception = e
                
                if attempt == self.max_retries:
                    self.metrics.increment_counter("retry_exhausted")
                    break
                
                delay = min(
                    self.base_delay * (self.exponential_base ** attempt),
                    self.max_delay
                )
                
                self.logger.warning(
                    f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}",
                    attempt=attempt + 1,
                    delay=delay,
                    error=str(e)
                )
                
                self.metrics.increment_counter("retry_attempt")
                await asyncio.sleep(delay)
        
        if last_exception:
            raise last_exception


# Global instances
_cache_manager: Optional[CacheManager] = None
_retry_manager: Optional[RetryManager] = None


def get_cache_manager() -> CacheManager:
    """Get global cache manager."""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager


def get_retry_manager() -> RetryManager:
    """Get global retry manager."""
    global _retry_manager
    if _retry_manager is None:
        _retry_manager = RetryManager()
    return _retry_manager
