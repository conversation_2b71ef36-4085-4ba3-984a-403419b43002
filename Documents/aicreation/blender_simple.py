#!/usr/bin/env python3
"""
blender_simple.py - Simplified and optimized Blender script for video generation
This script creates professional-looking videos with minimal rendering time.

Usage:
blender -b -P blender_simple.py -- --title "Title" --script "script.json" --audio "audio.mp3" --output "video.mp4"
"""

import bpy
import json
import sys
import math
import argparse
from pathlib import Path

def get_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser()
    
    # Get all arguments after "--"
    if "--" not in sys.argv:
        args = []
    else:
        args = sys.argv[sys.argv.index("--") + 1:]
    
    parser.add_argument("--title", type=str, required=True, help="Video title")
    parser.add_argument("--script", type=str, required=True, help="Path to script JSON file")
    parser.add_argument("--audio", type=str, required=True, help="Path to audio MP3 file")
    parser.add_argument("--output", type=str, required=True, help="Output video path")
    
    return parser.parse_args(args)

def clean_scene():
    """Clean the default scene."""
    # Delete all objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete()
    
    # Remove all materials
    for material in bpy.data.materials:
        bpy.data.materials.remove(material)

def setup_render_settings():
    """Setup optimized render settings for fast rendering."""
    scene = bpy.context.scene
    
    # Render settings
    scene.render.engine = 'EEVEE'  # Use EEVEE for faster rendering
    scene.render.resolution_x = 1920
    scene.render.resolution_y = 1080
    scene.render.resolution_percentage = 100
    scene.render.fps = 30
    
    # Set frame range (3 minutes = 180 seconds)
    scene.frame_start = 1
    scene.frame_end = 30 * 180  # 30 fps * 180 seconds
    
    # EEVEE settings for quality vs speed
    scene.eevee.taa_render_samples = 32  # Lower samples for speed
    scene.eevee.use_bloom = True
    scene.eevee.use_ssr = False  # Disable screen space reflections for speed
    scene.eevee.use_motion_blur = False  # Disable motion blur for speed

def create_camera():
    """Create and position camera."""
    bpy.ops.object.camera_add(location=(0, -8, 3))
    camera = bpy.context.active_object
    camera.rotation_euler = (math.radians(70), 0, 0)
    bpy.context.scene.camera = camera
    return camera

def create_lighting():
    """Create optimized lighting setup."""
    # Main light
    bpy.ops.object.light_add(type='SUN', location=(5, -5, 10))
    sun = bpy.context.active_object
    sun.data.energy = 3.0
    sun.data.color = (1.0, 0.95, 0.8)  # Warm white
    
    # Fill light
    bpy.ops.object.light_add(type='AREA', location=(-3, -3, 5))
    area = bpy.context.active_object
    area.data.energy = 1.5
    area.data.color = (0.8, 0.9, 1.0)  # Cool blue

def create_background():
    """Create animated background."""
    # Create background plane
    bpy.ops.mesh.primitive_plane_add(size=20, location=(0, 5, 0))
    bg = bpy.context.active_object
    bg.rotation_euler = (math.radians(90), 0, 0)
    
    # Create gradient material
    mat = bpy.data.materials.new(name="Background")
    mat.use_nodes = True
    nodes = mat.node_tree.nodes
    links = mat.node_tree.links
    
    # Clear existing nodes
    nodes.clear()
    
    # Add nodes
    output = nodes.new(type='ShaderNodeOutputMaterial')
    emission = nodes.new(type='ShaderNodeEmission')
    gradient = nodes.new(type='ShaderNodeTexGradient')
    mapping = nodes.new(type='ShaderNodeMapping')
    coord = nodes.new(type='ShaderNodeTexCoord')
    
    # Position nodes
    output.location = (400, 0)
    emission.location = (200, 0)
    gradient.location = (0, 0)
    mapping.location = (-200, 0)
    coord.location = (-400, 0)
    
    # Connect nodes
    links.new(coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], gradient.inputs['Vector'])
    links.new(gradient.outputs['Color'], emission.inputs['Color'])
    links.new(emission.outputs['Emission'], output.inputs['Surface'])
    
    # Set gradient colors (dark blue to purple)
    gradient.gradient_type = 'LINEAR'
    emission.inputs['Strength'].default_value = 0.8
    
    # Assign material
    bg.data.materials.append(mat)
    
    return bg

def create_title_text(title):
    """Create animated title text."""
    bpy.ops.object.text_add(location=(0, 0, 1))
    text_obj = bpy.context.active_object
    text_obj.data.body = title
    text_obj.data.align_x = 'CENTER'
    text_obj.data.align_y = 'CENTER'
    text_obj.data.size = 0.8
    text_obj.data.extrude = 0.1  # Give it some depth
    
    # Create glowing material
    mat = bpy.data.materials.new(name="TitleMaterial")
    mat.use_nodes = True
    nodes = mat.node_tree.nodes
    links = mat.node_tree.links
    
    # Clear existing nodes
    nodes.clear()
    
    # Add nodes
    output = nodes.new(type='ShaderNodeOutputMaterial')
    emission = nodes.new(type='ShaderNodeEmission')
    
    # Position nodes
    output.location = (200, 0)
    emission.location = (0, 0)
    
    # Connect nodes
    links.new(emission.outputs['Emission'], output.inputs['Surface'])
    
    # Set emission properties
    emission.inputs['Color'].default_value = (1.0, 0.8, 0.2, 1.0)  # Gold color
    emission.inputs['Strength'].default_value = 2.0
    
    # Assign material
    text_obj.data.materials.append(mat)
    
    # Animate title (fade in and out)
    text_obj.scale = (0, 0, 0)
    text_obj.keyframe_insert(data_path="scale", frame=1)
    
    text_obj.scale = (1, 1, 1)
    text_obj.keyframe_insert(data_path="scale", frame=60)  # 2 seconds
    
    text_obj.scale = (1, 1, 1)
    text_obj.keyframe_insert(data_path="scale", frame=120)  # 4 seconds
    
    text_obj.scale = (0, 0, 0)
    text_obj.keyframe_insert(data_path="scale", frame=180)  # 6 seconds
    
    return text_obj

def add_audio(audio_path):
    """Add audio track to the video."""
    # Ensure we have a sequence editor
    if not bpy.context.scene.sequence_editor:
        bpy.context.scene.sequence_editor_create()
    
    # Add audio strip
    audio_strip = bpy.context.scene.sequence_editor.sequences.new_sound(
        name="Audio",
        filepath=audio_path,
        channel=1,
        frame_start=1
    )
    
    # Adjust scene length to match audio if needed
    audio_length = audio_strip.frame_final_duration
    if audio_length > bpy.context.scene.frame_end:
        bpy.context.scene.frame_end = audio_length

def setup_output(output_path):
    """Setup video output settings."""
    scene = bpy.context.scene
    
    # Set output path
    scene.render.filepath = output_path
    
    # Video format settings
    scene.render.image_settings.file_format = 'FFMPEG'
    scene.render.ffmpeg.format = 'MPEG4'
    scene.render.ffmpeg.codec = 'H264'
    scene.render.ffmpeg.constant_rate_factor = 'MEDIUM'
    scene.render.ffmpeg.ffmpeg_preset = 'GOOD'
    
    # Audio settings
    scene.render.ffmpeg.audio_codec = 'AAC'
    scene.render.ffmpeg.audio_bitrate = 192

def main():
    """Main function to create the video."""
    try:
        # Parse arguments
        args = get_args()
        
        print(f"Creating video: {args.title}")
        print(f"Output: {args.output}")
        
        # Load script data
        with open(args.script, 'r') as f:
            script_data = json.load(f)
        
        # Setup scene
        clean_scene()
        setup_render_settings()
        
        # Create scene elements
        camera = create_camera()
        create_lighting()
        background = create_background()
        title_text = create_title_text(script_data.get('title', args.title))
        
        # Add audio
        add_audio(args.audio)
        
        # Setup output
        setup_output(args.output)
        
        print("Starting render...")
        
        # Render animation
        bpy.ops.render.render(animation=True)
        
        print(f"Video created successfully: {args.output}")
        
    except Exception as e:
        print(f"Error creating video: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
