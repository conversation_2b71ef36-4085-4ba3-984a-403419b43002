#!/usr/bin/env python3
"""
piper_direct_test.py - Test direct de Piper sans subprocess complexe
"""

import os
import sys
from pathlib import Path

def check_piper_files():
    """Check if Piper files exist."""
    print("🔍 VÉRIFICATION DES FICHIERS PIPER")
    print("-" * 40)
    
    piper_dir = Path("./piper")
    
    if not piper_dir.exists():
        print("❌ Dossier ./piper non trouvé")
        return False
    
    piper_exe = piper_dir / "piper"
    
    if not piper_exe.exists():
        print("❌ Exécutable ./piper/piper non trouvé")
        return False
    
    print(f"✅ Dossier Piper: {piper_dir}")
    print(f"✅ Exécutable: {piper_exe}")
    
    # Check permissions
    if not os.access(piper_exe, os.X_OK):
        print("⚠️ Pas de permissions d'exécution")
        print("💡 Exécutez: chmod +x ./piper/piper")
        return False
    
    print("✅ Permissions d'exécution OK")
    
    # Check other files
    files_to_check = [
        "espeak-ng-data",
        "libtashkeel_model.ort"
    ]
    
    for file_name in files_to_check:
        file_path = piper_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}: présent")
        else:
            print(f"⚠️ {file_name}: manquant")
    
    return True

def check_models():
    """Check for voice models."""
    print("\n🎵 VÉRIFICATION DES MODÈLES VOCAUX")
    print("-" * 40)
    
    models_dir = Path.home() / ".local" / "share" / "piper" / "models"
    
    if not models_dir.exists():
        print(f"❌ Dossier modèles non trouvé: {models_dir}")
        print("💡 Créez-le avec: mkdir -p ~/.local/share/piper/models")
        return False
    
    print(f"✅ Dossier modèles: {models_dir}")
    
    # Look for models
    onnx_models = list(models_dir.glob("*.onnx"))
    json_configs = list(models_dir.glob("*.json"))
    
    print(f"📊 Modèles .onnx trouvés: {len(onnx_models)}")
    print(f"📊 Configurations .json trouvées: {len(json_configs)}")
    
    if onnx_models:
        print("🎵 Modèles disponibles:")
        for model in onnx_models[:5]:  # Show first 5
            size_mb = model.stat().st_size / (1024 * 1024)
            print(f"   • {model.name} ({size_mb:.1f} MB)")
        return True
    else:
        print("❌ Aucun modèle trouvé")
        return False

def simple_piper_test():
    """Simple test using os.system."""
    print("\n🧪 TEST SIMPLE AVEC OS.SYSTEM")
    print("-" * 40)
    
    # Set environment variables
    os.environ['DYLD_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    os.environ['DYLD_FALLBACK_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    os.environ['ESPEAK_DATA_PATH'] = "./piper/espeak-ng-data"
    
    print("🔄 Test de la commande --help...")
    
    # Try simple help command
    result = os.system("./piper/piper --help 2>&1")
    
    if result == 0:
        print("✅ Commande --help réussie!")
        return True
    else:
        print(f"❌ Commande échouée (code: {result})")
        return False

def download_french_model():
    """Download a French model."""
    print("\n📥 TÉLÉCHARGEMENT MODÈLE FRANÇAIS")
    print("-" * 40)
    
    models_dir = Path.home() / ".local" / "share" / "piper" / "models"
    models_dir.mkdir(parents=True, exist_ok=True)
    
    model_file = models_dir / "fr_FR-gilles-low.onnx"
    config_file = models_dir / "fr_FR-gilles-low.onnx.json"
    
    if model_file.exists():
        print(f"✅ Modèle déjà présent: {model_file.name}")
        return True
    
    print("🔄 Téléchargement avec curl...")
    
    # URLs for the French model
    model_url = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/gilles/low/fr_FR-gilles-low.onnx"
    config_url = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/gilles/low/fr_FR-gilles-low.onnx.json"
    
    # Download model
    cmd1 = f"curl -L '{model_url}' -o '{model_file}'"
    cmd2 = f"curl -L '{config_url}' -o '{config_file}'"
    
    print("📥 Téléchargement du modèle...")
    result1 = os.system(cmd1)
    
    print("📥 Téléchargement de la configuration...")
    result2 = os.system(cmd2)
    
    if result1 == 0 and result2 == 0:
        if model_file.exists() and config_file.exists():
            model_size = model_file.stat().st_size / (1024 * 1024)
            print(f"✅ Téléchargement réussi!")
            print(f"📊 Taille: {model_size:.1f} MB")
            return True
    
    print("❌ Téléchargement échoué")
    return False

def test_with_model():
    """Test Piper with a model."""
    print("\n🎵 TEST AVEC MODÈLE VOCAL")
    print("-" * 40)
    
    models_dir = Path.home() / ".local" / "share" / "piper" / "models"
    model_file = models_dir / "fr_FR-gilles-low.onnx"
    
    if not model_file.exists():
        print("❌ Modèle non trouvé")
        return False
    
    # Set environment
    os.environ['DYLD_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    os.environ['DYLD_FALLBACK_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    os.environ['ESPEAK_DATA_PATH'] = "./piper/espeak-ng-data"
    
    test_text = "Bonjour, ceci est un test de Piper TTS."
    output_file = "test_piper.wav"
    
    print(f"🧪 Test avec: {model_file.name}")
    print(f"📝 Texte: {test_text}")
    
    # Create command
    cmd = f"echo '{test_text}' | ./piper/piper --model '{model_file}' --output_file '{output_file}'"
    
    print("🔄 Exécution...")
    result = os.system(cmd)
    
    if result == 0:
        if Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"✅ Synthèse réussie!")
            print(f"📁 Fichier: {output_file} ({file_size} bytes)")
            
            # Cleanup
            os.remove(output_file)
            return True
        else:
            print("❌ Fichier de sortie non créé")
    else:
        print(f"❌ Commande échouée (code: {result})")
    
    return False

def main():
    """Main function."""
    print("🎤 CONFIGURATION PIPER TTS - TEST DIRECT")
    print("=" * 50)
    
    # Step 1: Check files
    if not check_piper_files():
        print("\n❌ Fichiers Piper manquants ou incorrects")
        return
    
    # Step 2: Simple test
    if not simple_piper_test():
        print("\n❌ Test simple échoué")
        print("\n💡 SOLUTIONS:")
        print("1. Vérifiez espeak-ng: brew install espeak-ng")
        print("2. Permissions: chmod +x ./piper/piper")
        print("3. Quarantaine: xattr -d com.apple.quarantine ./piper/piper")
        return
    
    # Step 3: Check models
    has_models = check_models()
    
    # Step 4: Download model if needed
    if not has_models:
        print("\n📥 Aucun modèle trouvé, téléchargement...")
        if download_french_model():
            has_models = True
    
    # Step 5: Test with model
    if has_models:
        if test_with_model():
            print("\n🎉 PIPER TTS FONCTIONNE PARFAITEMENT!")
            print("\n✅ Prêt pour l'intégration dans le système de vidéos")
        else:
            print("\n⚠️ Piper fonctionne mais problème avec la synthèse")
    else:
        print("\n⚠️ Piper fonctionne mais aucun modèle disponible")
    
    print("\n📖 PROCHAINES ÉTAPES:")
    print("1. Testez l'intégration: python piper_tts.py")
    print("2. Créez une vidéo: python create_nature_video_with_piper.py")

if __name__ == "__main__":
    main()
