#!/usr/bin/env python3
"""
Gestionnaire de retry et backoff pour les APIs externes.
Implémente des stratégies de retry intelligentes avec circuit breaker.
"""

import asyncio
import time
import random
from typing import Callable, Any, Optional, Dict, List
from dataclasses import dataclass
from enum import Enum
from functools import wraps
from loguru import logger
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class RetryStrategy(Enum):
    """Stratégies de retry disponibles"""
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    FIXED = "fixed"
    FIBONACCI = "fibonacci"

@dataclass
class RetryConfig:
    """Configuration pour les tentatives de retry"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    backoff_factor: float = 2.0
    jitter: bool = True
    exceptions: tuple = (Exception,)

class CircuitBreakerState(Enum):
    """États du circuit breaker"""
    CLOSED = "closed"      # Fonctionnement normal
    OPEN = "open"          # Circuit ouvert, rejette les requêtes
    HALF_OPEN = "half_open" # Test de récupération

@dataclass
class CircuitBreakerConfig:
    """Configuration du circuit breaker"""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    success_threshold: int = 3  # Pour passer de HALF_OPEN à CLOSED

class CircuitBreaker:
    """Implémentation du pattern Circuit Breaker"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self.call_count = 0
        
    def can_execute(self) -> bool:
        """Vérifie si l'exécution est autorisée"""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if time.time() - self.last_failure_time >= self.config.recovery_timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                self.success_count = 0
                logger.info("Circuit breaker: passage en mode HALF_OPEN")
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Enregistre un succès"""
        self.call_count += 1
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitBreakerState.CLOSED
                self.failure_count = 0
                logger.info("Circuit breaker: retour en mode CLOSED")
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count = max(0, self.failure_count - 1)
    
    def record_failure(self):
        """Enregistre un échec"""
        self.call_count += 1
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            logger.warning(f"Circuit breaker: passage en mode OPEN après {self.failure_count} échecs")

class RetryManager:
    """Gestionnaire principal des tentatives avec circuit breaker"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "retries": 0
        }
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Récupère ou crée un circuit breaker pour un service"""
        if service_name not in self.circuit_breakers:
            config = CircuitBreakerConfig()
            self.circuit_breakers[service_name] = CircuitBreaker(config)
        return self.circuit_breakers[service_name]
    
    def calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """Calcule le délai avant la prochaine tentative"""
        if config.strategy == RetryStrategy.EXPONENTIAL:
            delay = config.base_delay * (config.backoff_factor ** attempt)
        elif config.strategy == RetryStrategy.LINEAR:
            delay = config.base_delay * (attempt + 1)
        elif config.strategy == RetryStrategy.FIBONACCI:
            delay = config.base_delay * self._fibonacci(attempt + 1)
        else:  # FIXED
            delay = config.base_delay
        
        # Limitation du délai maximum
        delay = min(delay, config.max_delay)
        
        # Ajout de jitter pour éviter l'effet thundering herd
        if config.jitter:
            delay += random.uniform(0, delay * 0.1)
        
        return delay
    
    def _fibonacci(self, n: int) -> int:
        """Calcule le n-ième nombre de Fibonacci"""
        if n <= 1:
            return n
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b
    
    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        service_name: str = "default",
        config: Optional[RetryConfig] = None,
        **kwargs
    ) -> Any:
        """Exécute une fonction avec retry et circuit breaker"""
        if config is None:
            config = RetryConfig()
        
        circuit_breaker = self.get_circuit_breaker(service_name)
        self.stats["total_calls"] += 1
        
        # Vérification du circuit breaker
        if not circuit_breaker.can_execute():
            logger.warning(f"Circuit breaker ouvert pour {service_name}")
            raise Exception(f"Circuit breaker ouvert pour {service_name}")
        
        last_exception = None
        
        for attempt in range(config.max_attempts):
            try:
                # Exécution de la fonction
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Succès
                circuit_breaker.record_success()
                self.stats["successful_calls"] += 1
                
                if attempt > 0:
                    logger.info(f"Succès après {attempt + 1} tentatives pour {service_name}")
                
                return result
                
            except config.exceptions as e:
                last_exception = e
                circuit_breaker.record_failure()
                
                if attempt < config.max_attempts - 1:
                    delay = self.calculate_delay(attempt, config)
                    logger.warning(
                        f"Tentative {attempt + 1}/{config.max_attempts} échouée pour {service_name}: {e}. "
                        f"Retry dans {delay:.2f}s"
                    )
                    self.stats["retries"] += 1
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Toutes les tentatives échouées pour {service_name}: {e}")
        
        # Échec final
        self.stats["failed_calls"] += 1
        raise last_exception
    
    def retry(
        self,
        service_name: str = "default",
        config: Optional[RetryConfig] = None
    ):
        """Décorateur pour ajouter retry à une fonction"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                return await self.execute_with_retry(
                    func, *args, service_name=service_name, config=config, **kwargs
                )
            return wrapper
        return decorator
    
    def get_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques d'utilisation"""
        stats = self.stats.copy()
        stats["circuit_breakers"] = {}
        
        for name, cb in self.circuit_breakers.items():
            stats["circuit_breakers"][name] = {
                "state": cb.state.value,
                "failure_count": cb.failure_count,
                "call_count": cb.call_count
            }
        
        return stats

class APIRetrySession:
    """Session HTTP avec retry automatique pour les APIs"""
    
    def __init__(self, retry_config: Optional[RetryConfig] = None):
        self.retry_config = retry_config or RetryConfig()
        self.session = requests.Session()
        
        # Configuration des retries HTTP
        retry_strategy = Retry(
            total=self.retry_config.max_attempts,
            backoff_factor=self.retry_config.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET avec retry automatique"""
        return self.session.get(url, **kwargs)
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST avec retry automatique"""
        return self.session.post(url, **kwargs)
    
    def close(self):
        """Ferme la session"""
        self.session.close()

# Configurations prédéfinies pour différents services
PIXABAY_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=2.0,
    max_delay=30.0,
    strategy=RetryStrategy.EXPONENTIAL,
    exceptions=(requests.RequestException, ConnectionError, TimeoutError)
)

OLLAMA_RETRY_CONFIG = RetryConfig(
    max_attempts=2,
    base_delay=1.0,
    max_delay=10.0,
    strategy=RetryStrategy.LINEAR,
    exceptions=(requests.RequestException, ConnectionError)
)

TTS_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=0.5,
    max_delay=5.0,
    strategy=RetryStrategy.EXPONENTIAL,
    exceptions=(Exception,)
)

# Instance globale du gestionnaire de retry
retry_manager = RetryManager()

# Décorateurs prêts à l'emploi
def retry_pixabay(func):
    """Décorateur pour les appels Pixabay"""
    return retry_manager.retry("pixabay", PIXABAY_RETRY_CONFIG)(func)

def retry_ollama(func):
    """Décorateur pour les appels Ollama"""
    return retry_manager.retry("ollama", OLLAMA_RETRY_CONFIG)(func)

def retry_tts(func):
    """Décorateur pour les appels TTS"""
    return retry_manager.retry("tts", TTS_RETRY_CONFIG)(func)

# Exemple d'utilisation
async def example_usage():
    """Exemple d'utilisation du gestionnaire de retry"""
    
    @retry_pixabay
    async def fetch_pixabay_videos(keyword: str):
        """Exemple de fonction avec retry pour Pixabay"""
        # Simulation d'un appel API qui peut échouer
        if random.random() < 0.7:  # 70% de chance d'échec
            raise requests.RequestException("Erreur réseau simulée")
        return f"Vidéos pour {keyword}"
    
    try:
        result = await fetch_pixabay_videos("nature")
        logger.info(f"Résultat: {result}")
    except Exception as e:
        logger.error(f"Échec final: {e}")
    
    # Affichage des statistiques
    stats = retry_manager.get_stats()
    logger.info(f"Statistiques: {stats}")

if __name__ == "__main__":
    # Test du gestionnaire de retry
    asyncio.run(example_usage())
