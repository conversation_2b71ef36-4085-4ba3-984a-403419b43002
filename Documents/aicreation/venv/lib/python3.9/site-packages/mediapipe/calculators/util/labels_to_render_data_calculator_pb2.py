# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/labels_to_render_data_calculator.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.util import color_pb2 as mediapipe_dot_util_dot_color__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nAmediapipe/calculators/util/labels_to_render_data_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\x1a\x1amediapipe/util/color.proto\"\xda\x04\n#LabelsToRenderDataCalculatorOptions\x12\x1f\n\x05\x63olor\x18\x01 \x03(\x0b\x32\x10.mediapipe.Color\x12\x14\n\tthickness\x18\x02 \x01(\x01:\x01\x32\x12\'\n\routline_color\x18\x0c \x03(\x0b\x32\x10.mediapipe.Color\x12\x19\n\x11outline_thickness\x18\x0b \x01(\x01\x12\x1a\n\x0e\x66ont_height_px\x18\x03 \x01(\x05:\x02\x35\x30\x12\x1f\n\x14horizontal_offset_px\x18\x07 \x01(\x05:\x01\x30\x12\x1d\n\x12vertical_offset_px\x18\x08 \x01(\x05:\x01\x30\x12\x19\n\x0emax_num_labels\x18\x04 \x01(\x05:\x01\x31\x12\x14\n\tfont_face\x18\x05 \x01(\x05:\x01\x30\x12S\n\x08location\x18\x06 \x01(\x0e\x32\x37.mediapipe.LabelsToRenderDataCalculatorOptions.Location:\x08TOP_LEFT\x12\x1f\n\x10use_display_name\x18\t \x01(\x08:\x05\x66\x61lse\x12+\n\x1c\x64isplay_classification_score\x18\n \x01(\x08:\x05\x66\x61lse\")\n\x08Location\x12\x0c\n\x08TOP_LEFT\x10\x00\x12\x0f\n\x0b\x42OTTOM_LEFT\x10\x01\x32]\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xcc\xea\xc4\x81\x01 \x01(\x0b\x32..mediapipe.LabelsToRenderDataCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.labels_to_render_data_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_LABELSTORENDERDATACALCULATOROPTIONS']._serialized_start=147
  _globals['_LABELSTORENDERDATACALCULATOROPTIONS']._serialized_end=749
  _globals['_LABELSTORENDERDATACALCULATOROPTIONS_LOCATION']._serialized_start=613
  _globals['_LABELSTORENDERDATACALCULATOROPTIONS_LOCATION']._serialized_end=654
# @@protoc_insertion_point(module_scope)
