{"description": "Deploy and manage user provided container images that scale automatically based on HTTP traffic.", "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "View and manage your data across Google Cloud Platform services"}}}}, "id": "run:v1beta1", "mtlsRootUrl": "https://run.mtls.googleapis.com/", "version": "v1beta1", "basePath": "", "protocol": "rest", "name": "run", "canonicalName": "Cloud Run", "servicePath": "", "resources": {"namespaces": {"resources": {"customresourcedefinitions": {"methods": {"get": {"description": "Rpc to get information about a CustomResourceDefinition.", "httpMethod": "GET", "id": "run.namespaces.customresourcedefinitions.get", "path": "apis/apiextensions.k8s.io/v1beta1/{+name}", "flatPath": "apis/apiextensions.k8s.io/v1beta1/namespaces/{namespacesId}/customresourcedefinitions/{customresourcedefinitionsId}", "parameters": {"name": {"location": "path", "required": true, "type": "string", "pattern": "^namespaces/[^/]+/customresourcedefinitions/[^/]+$", "description": "The name of the CustomResourceDefinition being retrieved. If needed, replace {namespace_id} with the project ID."}}, "parameterOrder": ["name"], "scopes": ["https://www.googleapis.com/auth/cloud-platform"], "response": {"$ref": "CustomResourceDefinition"}}}}}}, "customresourcedefinitions": {"methods": {"list": {"parameterOrder": [], "path": "apis/apiextensions.k8s.io/v1beta1/customresourcedefinitions", "description": "Rpc to list custom resource definitions.", "id": "run.customresourcedefinitions.list", "httpMethod": "GET", "parameters": {"limit": {"type": "integer", "format": "int32", "location": "query"}, "labelSelector": {"description": "Allows to filter resources based on a label. Supported operations are =, !=, exists, in, and notIn.", "type": "string", "location": "query"}, "watch": {"description": "Flag that indicates that the client expects to watch this resource as well. Not currently used by Cloud Run.", "type": "boolean", "location": "query"}, "continue": {"location": "query", "type": "string", "description": "Optional encoded string to continue paging."}, "parent": {"type": "string", "description": "The project ID or project number from which the storages should be listed.", "location": "query"}, "fieldSelector": {"location": "query", "type": "string", "description": "Allows to filter resources based on a specific value for a field name. Send this in a query string format. i.e. 'metadata.name%3Dlorem'. Not currently used by Cloud Run."}, "includeUninitialized": {"type": "boolean", "description": "Not currently used by Cloud Run.", "location": "query"}, "resourceVersion": {"description": "The baseline resource version from which the list or watch operation should start. Not currently used by Cloud Run.", "type": "string", "location": "query"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"], "response": {"$ref": "ListCustomResourceDefinitionsResponse"}, "flatPath": "apis/apiextensions.k8s.io/v1beta1/customresourcedefinitions"}}}, "projects": {"resources": {"locations": {"resources": {"customresourcedefinitions": {"methods": {"list": {"path": "v1beta1/{+parent}/customresourcedefinitions", "parameterOrder": ["parent"], "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/customresourcedefinitions", "id": "run.projects.locations.customresourcedefinitions.list", "description": "Rpc to list custom resource definitions.", "scopes": ["https://www.googleapis.com/auth/cloud-platform"], "httpMethod": "GET", "response": {"$ref": "ListCustomResourceDefinitionsResponse"}, "parameters": {"continue": {"description": "Optional encoded string to continue paging.", "location": "query", "type": "string"}, "parent": {"description": "The project ID or project number from which the storages should be listed.", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string", "location": "path"}, "limit": {"format": "int32", "type": "integer", "location": "query"}, "includeUninitialized": {"type": "boolean", "description": "Not currently used by Cloud Run.", "location": "query"}, "resourceVersion": {"description": "The baseline resource version from which the list or watch operation should start. Not currently used by Cloud Run.", "location": "query", "type": "string"}, "labelSelector": {"type": "string", "location": "query", "description": "Allows to filter resources based on a label. Supported operations are =, !=, exists, in, and notIn."}, "fieldSelector": {"type": "string", "description": "Allows to filter resources based on a specific value for a field name. Send this in a query string format. i.e. 'metadata.name%3Dlorem'. Not currently used by Cloud Run.", "location": "query"}, "watch": {"description": "Flag that indicates that the client expects to watch this resource as well. Not currently used by Cloud Run.", "location": "query", "type": "boolean"}}}, "get": {"parameters": {"name": {"location": "path", "description": "The name of the CustomResourceDefinition being retrieved. If needed, replace {namespace_id} with the project ID.", "type": "string", "required": true, "pattern": "^projects/[^/]+/locations/[^/]+/customresourcedefinitions/[^/]+$"}}, "path": "v1beta1/{+name}", "parameterOrder": ["name"], "description": "Rpc to get information about a CustomResourceDefinition.", "scopes": ["https://www.googleapis.com/auth/cloud-platform"], "response": {"$ref": "CustomResourceDefinition"}, "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/customresourcedefinitions/{customresourcedefinitionsId}", "id": "run.projects.locations.customresourcedefinitions.get", "httpMethod": "GET"}}}}}}}}, "ownerName": "Google", "ownerDomain": "google.com", "fullyEncodeReservedExpansion": true, "title": "Cloud Run Admin API", "parameters": {"quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"type": "string", "description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query"}, "callback": {"description": "JSONP", "type": "string", "location": "query"}, "prettyPrint": {"description": "Returns response with indentations and line breaks.", "type": "boolean", "default": "true", "location": "query"}, "$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "fields": {"type": "string", "location": "query", "description": "Selector specifying which fields to include in a partial response."}, "key": {"location": "query", "type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token."}, "alt": {"default": "json", "location": "query", "enum": ["json", "media", "proto"], "description": "Data format for response.", "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "type": "string"}, "upload_protocol": {"location": "query", "description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "access_token": {"type": "string", "location": "query", "description": "OAuth access token."}}, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "baseUrl": "https://run.googleapis.com/", "rootUrl": "https://run.googleapis.com/", "schemas": {"CustomResourceDefinitionSpec": {"properties": {"subresources": {"$ref": "CustomResourceSubresources", "description": "Subresources describes the subresources for CustomResources +optional"}, "version": {"description": "Version is the version this resource belongs in Should be always first item in Versions field if provided. Optional, but at least one of Version or Versions must be set. Deprecated: Please use `Versions`. +optional", "type": "string"}, "scope": {"type": "string", "description": "<PERSON><PERSON> indicates whether this resource is cluster or namespace scoped. De<PERSON>ult is namespaced"}, "validation": {"description": "Validation describes the validation methods for CustomResources +optional", "$ref": "CustomResourceValidation"}, "names": {"$ref": "CustomResourceDefinitionNames", "description": "Names are the names used to describe this custom resource"}, "versions": {"type": "array", "items": {"$ref": "CustomResourceDefinitionVersion"}, "description": "Versions is the list of all supported versions for this resource. If Version field is provided, this field is optional. Validation: All versions must use the same validation schema for now. i.e., top level Validation field is applied to all of these versions. Order: The version name will be used to compute the order. If the version string is \"kube-like\", it will sort above non \"kube-like\" version strings, which are ordered lexicographically. \"Kube-like\" versions start with a \"v\", then are followed by a number (the major version), then optionally the string \"alpha\" or \"beta\" and another number (the minor version). These are sorted first by GA > beta > alpha (where GA is a version with no suffix such as beta or alpha), and then by comparing major version, then minor version. An example sorted list of versions: v10, v2, v1, v11beta2, v10beta3, v3beta1, v12alpha1, v11alpha2, foo1, foo10. +optional"}, "additionalPrinterColumns": {"type": "array", "items": {"$ref": "CustomResourceColumnDefinition"}, "description": "AdditionalPrinterColumns are additional columns shown e.g. in kubectl next to the name. Defaults to a created-at column. +optional"}, "group": {"type": "string", "description": "Group is the group this resource belongs in"}}, "type": "object", "id": "CustomResourceDefinitionSpec", "description": "CustomResourceDefinitionSpec describes how a user wants their resource to appear"}, "JSON": {"properties": {"raw": {"format": "byte", "type": "string"}}, "id": "JSON", "type": "object", "description": "JSON represents any valid JSON value. These types are supported: bool, int64, float64, string, []interface{}, map[string]interface{} and nil."}, "JSONSchemaProps": {"id": "JSONSchemaProps", "description": "JSONSchemaProps is a JSON-Schema following Specification Draft 4 (http://json-schema.org/).", "type": "object", "properties": {"minProperties": {"format": "int64", "type": "string"}, "dependencies": {"additionalProperties": {"$ref": "JSONSchemaPropsOrStringArray"}, "type": "object"}, "minItems": {"format": "int64", "type": "string"}, "default": {"$ref": "JSON"}, "required": {"items": {"type": "string"}, "type": "array"}, "maximum": {"format": "double", "type": "number"}, "additionalItems": {"$ref": "JSONSchemaPropsOrBool"}, "ref": {"type": "string"}, "description": {"type": "string"}, "minimum": {"format": "double", "type": "number"}, "schema": {"type": "string"}, "maxProperties": {"format": "int64", "type": "string"}, "items": {"$ref": "JSONSchemaPropsOrArray"}, "additionalProperties": {"$ref": "JSONSchemaPropsOrBool"}, "id": {"type": "string"}, "pattern": {"type": "string"}, "multipleOf": {"type": "number", "format": "double"}, "patternProperties": {"additionalProperties": {"$ref": "JSONSchemaProps"}, "type": "object"}, "enum": {"type": "array", "items": {"type": "string"}}, "maxItems": {"format": "int64", "type": "string"}, "not": {"$ref": "JSONSchemaProps"}, "oneOf": {"items": {"$ref": "JSONSchemaProps"}, "type": "array"}, "title": {"type": "string"}, "uniqueItems": {"type": "boolean"}, "definitions": {"additionalProperties": {"$ref": "JSONSchemaProps"}, "type": "object"}, "properties": {"type": "object", "additionalProperties": {"$ref": "JSONSchemaProps"}}, "minLength": {"type": "string", "format": "int64"}, "type": {"type": "string"}, "externalDocs": {"$ref": "ExternalDocumentation"}, "example": {"$ref": "JSON"}, "anyOf": {"items": {"$ref": "JSONSchemaProps"}, "type": "array"}, "exclusiveMinimum": {"type": "boolean"}, "maxLength": {"type": "string", "format": "int64"}, "format": {"type": "string"}, "exclusiveMaximum": {"type": "boolean"}, "allOf": {"type": "array", "items": {"$ref": "JSONSchemaProps"}}}}, "CustomResourceColumnDefinition": {"type": "object", "id": "CustomResourceColumnDefinition", "description": "CustomResourceColumnDefinition specifies a column for server side printing.", "properties": {"priority": {"description": "priority is an integer defining the relative importance of this column compared to others. Lower numbers are considered higher priority. Columns that may be omitted in limited space scenarios should be given a higher priority. +optional", "format": "int32", "type": "integer"}, "jsonPath": {"description": "JSONPath is a simple JSON path, i.e. with array notation.", "type": "string"}, "name": {"type": "string", "description": "name is a human readable name for the column."}, "format": {"type": "string", "description": "format is an optional OpenAPI type definition for this column. The 'name' format is applied to the primary identifier column to assist in clients identifying column is the resource name. See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#data-types for more. +optional"}, "type": {"description": "type is an OpenAPI type definition for this column. See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#data-types for more.", "type": "string"}, "description": {"description": "description is a human readable description of this column. +optional", "type": "string"}}}, "ListMeta": {"type": "object", "id": "ListMeta", "properties": {"selfLink": {"type": "string", "description": "SelfLink is a URL representing this object. Populated by the system. Read-only. +optional"}, "resourceVersion": {"type": "string", "description": "String that identifies the server's internal version of this object that can be used by clients to determine when objects have changed. Value must be treated as opaque by clients and passed unmodified back to the server. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#concurrency-control-and-consistency +optional"}, "continue": {"description": "continue may be set if the user set a limit on the number of items returned, and indicates that the server has more data available. The value is opaque and may be used to issue another request to the endpoint that served this list to retrieve the next set of available objects. Continuing a list may not be possible if the server configuration has changed or more than a few minutes have passed. The resourceVersion field returned when using this continue value will be identical to the value in the first response.", "type": "string"}}, "description": "ListMeta describes metadata that synthetic resources must have, including lists and various status objects. A resource may have only one of {ObjectMeta, ListMeta}."}, "CustomResourceDefinitionVersion": {"type": "object", "id": "CustomResourceDefinitionVersion", "properties": {"served": {"type": "boolean", "description": "Served is a flag enabling/disabling this version from being served via REST APIs"}, "storage": {"type": "boolean", "description": "Storage flags the version as storage version. There must be exactly one flagged as storage version."}, "name": {"type": "string", "description": "Name is the version name, e.g. “v1”, “v2beta1”, etc."}}}, "JSONSchemaPropsOrStringArray": {"properties": {"schema": {"$ref": "JSONSchemaProps"}, "property": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "description": "JSONSchemaPropsOrStringArray represents a JSONSchemaProps or a string array.", "id": "JSONSchemaPropsOrStringArray"}, "CustomResourceSubresourceStatus": {"properties": {}, "type": "object", "description": "CustomResourceSubresourceStatus defines how to serve the status subresource for CustomResources. Status is represented by the `.status` JSON path inside of a CustomResource. When set, * exposes a /status subresource for the custom resource * PUT requests to the /status subresource take a custom resource object, and ignore changes to anything except the status stanza * PUT/POST/PATCH requests to the custom resource ignore changes to the status stanza", "id": "CustomResourceSubresourceStatus"}, "CustomResourceSubresources": {"description": "CustomResourceSubresources defines the status and scale subresources for CustomResources.", "properties": {"status": {"description": "Status denotes the status subresource for CustomResources +optional", "$ref": "CustomResourceSubresourceStatus"}, "scale": {"$ref": "CustomResourceSubresourceScale", "description": "Scale denotes the scale subresource for CustomResources +optional"}}, "type": "object", "id": "CustomResourceSubresources"}, "CustomResourceSubresourceScale": {"description": "CustomResourceSubresourceScale defines how to serve the scale subresource for CustomResources.", "type": "object", "id": "CustomResourceSubresourceScale", "properties": {"statusReplicasPath": {"type": "string", "description": "StatusReplicasPath defines the JSON path inside of a CustomResource that corresponds to Scale.Status.Replicas. Only JSON paths without the array notation are allowed. Must be a JSON Path under .status. If there is no value under the given path in the CustomResource, the status replica value in the /scale subresource will default to 0."}, "specReplicasPath": {"type": "string", "description": "SpecReplicasPath defines the JSON path inside of a CustomResource that corresponds to Scale.Spec.Replicas. Only JSON paths without the array notation are allowed. Must be a JSON Path under .spec. If there is no value under the given path in the CustomResource, the /scale subresource will return an error on GET."}, "labelSelectorPath": {"description": "LabelSelectorPath defines the JSON path inside of a CustomResource that corresponds to Scale.Status.Selector. Only JSON paths without the array notation are allowed. Must be a JSON Path under .status. Must be set to work with HPA. If there is no value under the given path in the CustomResource, the status label selector value in the /scale subresource will default to the empty string. +optional", "type": "string"}}}, "CustomResourceDefinition": {"description": "CustomResourceDefinition represents a resource that should be exposed on the API server. Its name MUST be in the format <.spec.name>.<.spec.group>.", "properties": {"metadata": {"description": "Metadata associated with this CustomResourceDefinition.", "$ref": "ObjectMeta"}, "spec": {"description": "Spec describes how the user wants the resources to appear", "$ref": "CustomResourceDefinitionSpec"}, "apiVersion": {"description": "The API version for this call such as \"k8s.apiextensions.io/v1beta1\".", "type": "string"}, "kind": {"description": "The kind of resource, in this case always \"CustomResourceDefinition\".", "type": "string"}}, "type": "object", "id": "CustomResourceDefinition"}, "CustomResourceDefinitionNames": {"type": "object", "id": "CustomResourceDefinitionNames", "description": "CustomResourceDefinitionNames indicates the names to serve this CustomResourceDefinition", "properties": {"listKind": {"type": "string", "description": "ListKind is the serialized kind of the list for this resource. Defaults to List. +optional"}, "categories": {"type": "array", "description": "Categories is a list of grouped resources custom resources belong to (e.g. 'all') +optional", "items": {"type": "string"}}, "plural": {"type": "string", "description": "Plural is the plural name of the resource to serve. It must match the name of the CustomResourceDefinition-registration too: plural.group and it must be all lowercase."}, "shortNames": {"type": "array", "items": {"type": "string"}, "description": "ShortNames are short names for the resource. It must be all lowercase. +optional"}, "kind": {"description": "Kind is the serialized kind of the resource. It is normally CamelCase and singular.", "type": "string"}, "singular": {"description": "Singular is the singular name of the resource. It must be all lowercase Defaults to lowercased +optional", "type": "string"}}}, "OwnerReference": {"id": "OwnerReference", "type": "object", "properties": {"blockOwnerDeletion": {"description": "If true, AND if the owner has the \"foregroundDeletion\" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. Defaults to false. To set this field, a user needs \"delete\" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned. +optional", "type": "boolean"}, "uid": {"description": "UID of the referent. More info: http://kubernetes.io/docs/user-guide/identifiers#uids", "type": "string"}, "kind": {"type": "string", "description": "Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds"}, "apiVersion": {"type": "string", "description": "API version of the referent."}, "controller": {"type": "boolean", "description": "If true, this reference points to the managing controller. +optional"}, "name": {"type": "string", "description": "Name of the referent. More info: http://kubernetes.io/docs/user-guide/identifiers#names"}}, "description": "OwnerReference contains enough information to let you identify an owning object. Currently, an owning object must be in the same namespace, so there is no namespace field."}, "ObjectMeta": {"properties": {"generation": {"description": "(Optional) A sequence number representing a specific generation of the desired state. Populated by the system. Read-only.", "type": "integer", "format": "int32"}, "deletionTimestamp": {"type": "string", "format": "google-datetime", "description": "(Optional) Cloud Run fully managed: not supported Cloud Run for Anthos: supported DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested. Populated by the system when a graceful deletion is requested. Read-only. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata"}, "uid": {"description": "(Optional) UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations. Populated by the system. Read-only. More info: http://kubernetes.io/docs/user-guide/identifiers#uids", "type": "string"}, "resourceVersion": {"description": "(Optional) An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources. Populated by the system. Read-only. Value must be treated as opaque by clients. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency", "type": "string"}, "name": {"type": "string", "description": "Name must be unique within a namespace, within a Cloud Run region. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: http://kubernetes.io/docs/user-guide/identifiers#names +optional"}, "generateName": {"type": "string", "description": "(Optional) Cloud Run fully managed: not supported Cloud Run for Anthos: supported GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server. If this field is specified and the generated name exists, the server will NOT return a 409 - instead, it will either return 201 Created or 500 with Reason ServerTimeout indicating a unique name could not be found in the time allotted, and the client should retry (optionally after the time indicated in the Retry-After header). Applied only if Name is not specified. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#idempotency string generateName = 2;"}, "clusterName": {"type": "string", "description": "(Optional) Cloud Run fully managed: not supported Cloud Run for Anthos: supported The name of the cluster which the object belongs to. This is used to distinguish resources with same name and namespace in different clusters. This field is not set anywhere right now and apiserver is going to ignore it if set in create or update request."}, "annotations": {"description": "(Optional) Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: http://kubernetes.io/docs/user-guide/annotations", "type": "object", "additionalProperties": {"type": "string"}}, "namespace": {"type": "string", "description": "Namespace defines the space within each name must be unique, within a Cloud Run region. In Cloud Run the namespace must be equal to either the project ID or project number."}, "labels": {"additionalProperties": {"type": "string"}, "description": "(Optional) Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and routes. More info: http://kubernetes.io/docs/user-guide/labels", "type": "object"}, "selfLink": {"description": "(Optional) SelfLink is a URL representing this object. Populated by the system. Read-only. string selfLink = 4;", "type": "string"}, "deletionGracePeriodSeconds": {"type": "integer", "format": "int32", "description": "(Optional) Cloud Run fully managed: not supported Cloud Run for Anthos: supported Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only."}, "finalizers": {"items": {"type": "string"}, "type": "array", "description": "(Optional) Cloud Run fully managed: not supported Cloud Run for Anthos: supported Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. +patchStrategy=merge"}, "ownerReferences": {"type": "array", "description": "(Optional) Cloud Run fully managed: not supported Cloud Run for Anthos: supported List of objects that own this object. If ALL objects in the list have been deleted, this object will be garbage collected.", "items": {"$ref": "OwnerReference"}}, "creationTimestamp": {"description": "(Optional) CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata", "format": "google-datetime", "type": "string"}}, "type": "object", "id": "ObjectMeta", "description": "k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta is metadata that all persisted resources must have, which includes all objects users must create."}, "CustomResourceValidation": {"properties": {"openAPIV3Schema": {"$ref": "JSONSchemaProps", "description": "OpenAPIV3Schema is the OpenAPI v3 schema to be validated against. +optional"}}, "description": "CustomResourceValidation is a list of validation methods for CustomResources.", "type": "object", "id": "CustomResourceValidation"}, "JSONSchemaPropsOrBool": {"type": "object", "id": "JSONSchemaPropsOrBool", "properties": {"schema": {"$ref": "JSONSchemaProps"}, "allows": {"type": "boolean"}}, "description": "JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value. Defaults to true for the boolean property."}, "ListCustomResourceDefinitionsResponse": {"properties": {"items": {"items": {"$ref": "CustomResourceDefinition"}, "description": "List of CustomResourceDefinitions.", "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "type": "array", "items": {"type": "string"}}, "apiVersion": {"type": "string", "description": "The API version for this call such as \"k8s.apiextensions.io/v1beta1\"."}, "metadata": {"$ref": "ListMeta", "description": "Metadata associated with this CustomResourceDefinition list."}, "kind": {"type": "string", "description": "The kind of this resource, in this case \"CustomResourceDefinitionList\"."}}, "id": "ListCustomResourceDefinitionsResponse", "type": "object"}, "ExternalDocumentation": {"properties": {"description": {"type": "string"}, "url": {"type": "string"}}, "type": "object", "id": "ExternalDocumentation", "description": "ExternalDocumentation allows referencing an external resource for extended documentation."}, "JSONSchemaPropsOrArray": {"type": "object", "id": "JSONSchemaPropsOrArray", "properties": {"schema": {"$ref": "JSONSchemaProps"}, "jsonSchemas": {"type": "array", "items": {"$ref": "JSONSchemaProps"}}}, "description": "JSONSchemaPropsOrArray represents a value that can either be a JSONSchemaProps or an array of JSONSchemaProps. Mainly here for serialization purposes."}}, "kind": "discovery#restDescription", "batchPath": "batch", "version_module": true, "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/run/", "revision": "20200814"}