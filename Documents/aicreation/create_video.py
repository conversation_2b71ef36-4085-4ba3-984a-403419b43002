#!/usr/bin/env python3
"""
create_video.py - Script principal pour créer des vidéos YouTube avec Piper TTS, Pixabay et Ollama
"""

import logging
from pathlib import Path
from typing import Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_video_from_keyword(keyword: str, video_category: Optional[str] = None,
                             music_type: str = "instrumental", with_vocals: bool = False):
    """Create a video from a keyword with specified category and music options."""
    print("🎬" * 30)
    print(f"🎥 CRÉATION VIDÉO: {keyword.upper()} 🎥")
    print("🎬" * 30)
    print(f"📂 Catégorie vidéo: {video_category or 'Auto-détection'}")
    print(f"🎵 Type musique: {music_type} {'(avec voix)' if with_vocals else '(instrumental)'}")
    print()
    
    try:
        # Import essential modules
        from enhanced_auto_youtube import Enhanced<PERSON><PERSON>YouTube
        
        # Initialize the system
        print("🔧 Initialisation du système...")
        auto_youtube = EnhancedAutoYouTube()
        
        print("✅ Système initialisé!")
        print()
        
        # Show available systems
        print("🎤 SYSTÈMES DISPONIBLES:")
        print("-" * 30)
        
        if hasattr(auto_youtube, 'audio_manager') and auto_youtube.audio_manager:
            tts_systems = list(auto_youtube.audio_manager.tts_systems.keys())
            if tts_systems:
                for system in tts_systems:
                    print(f"✅ TTS: {system.upper()}")
            else:
                print("✅ TTS: gTTS Enhanced (fallback)")
        
        if hasattr(auto_youtube, 'video_manager') and auto_youtube.video_manager:
            if hasattr(auto_youtube.video_manager, 'pixabay_manager'):
                print("✅ Vidéos: Pixabay")
            print("✅ Montage: MoviePy")
        
        if hasattr(auto_youtube, 'ollama_manager') and auto_youtube.ollama_manager:
            print("✅ Analyse: Ollama")
        
        print()
        
        # Create the video
        print(f"🎬 Création de la vidéo pour: '{keyword}'")
        print("-" * 40)
        
        video_path = auto_youtube.create_video(keyword)
        
        if video_path and Path(video_path).exists():
            video_size = Path(video_path).stat().st_size / (1024 * 1024)
            
            print("\n🎉 VIDÉO CRÉÉE AVEC SUCCÈS!")
            print("=" * 50)
            print(f"📁 Fichier: {video_path}")
            print(f"📊 Taille: {video_size:.1f} MB")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
            print()
            print("🎬 CARACTÉRISTIQUES:")
            print("   ✅ Narration: Piper TTS (anglais) + gTTS fallback")
            print("   ✅ Arrière-plan: Vidéos HD Pixabay")
            print("   ✅ Script: Analysé par Ollama")
            print("   ✅ Montage: MoviePy professionnel")
            if music_type != "none":
                music_desc = "avec voix" if with_vocals else "instrumentale"
                print(f"   ✅ Musique: {music_desc} zen apaisante")
            print("   ✅ Qualité: HD 1920x1080")
            print("   ✅ Format: MP4 optimisé YouTube")
            
            return video_path
        else:
            print("❌ Échec de création vidéo")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_multiple_videos():
    """Create multiple videos on different topics."""
    print("🎬 CRÉATION MULTIPLE DE VIDÉOS")
    print("=" * 40)
    
    # Suggested topics
    topics = [
        "artificial intelligence",
        "climate change",
        "space exploration", 
        "renewable energy",
        "medical breakthrough",
        "technology innovation",
        "nature conservation",
        "future cities"
    ]
    
    print("📝 Sujets suggérés:")
    for i, topic in enumerate(topics, 1):
        print(f"   {i}. {topic}")
    
    print("\n💡 Vous pouvez aussi utiliser vos propres mots-clés!")
    print("🎯 Chaque vidéo sera unique avec:")
    print("   • Script analysé par Ollama")
    print("   • Voix Piper TTS (anglais)")
    print("   • Vidéos Pixabay correspondantes")
    print("   • Musique zen adaptée")

def show_system_status():
    """Show the status of all system components."""
    print("🔍 STATUT DU SYSTÈME")
    print("=" * 30)
    
    try:
        # Check Piper TTS
        from piper_tts import PiperTTS
        piper = PiperTTS()
        print(f"🎤 Piper TTS: {'✅ Disponible' if piper.available else '❌ Non disponible'}")
        
        # Check Pixabay
        from pixabay_videos import PixabayVideoManager
        pixabay = PixabayVideoManager()
        print(f"🎬 Pixabay: {'✅ Disponible' if pixabay.available else '❌ Non disponible'}")
        
        # Check Ollama
        from ollama_manager import OllamaManager
        ollama = OllamaManager()
        print(f"🧠 Ollama: {'✅ Disponible' if ollama.available else '❌ Non disponible'}")
        
        # Check MoviePy
        try:
            import moviepy
            print("🎞️ MoviePy: ✅ Disponible")
        except ImportError:
            print("🎞️ MoviePy: ❌ Non disponible")
        
        print()
        print("📋 CONFIGURATION REQUISE:")
        print("   • Piper TTS: Voix anglaise locale")
        print("   • Pixabay: API key configurée")
        print("   • Ollama: Modèle local installé")
        print("   • MoviePy: Montage vidéo")
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")

def main():
    """Main function with interactive menu."""
    print("🎬 CRÉATEUR DE VIDÉOS YOUTUBE")
    print("=" * 40)
    print("🎤 Piper TTS (Anglais) + Pixabay + Ollama + MoviePy")
    print()
    
    while True:
        print("MENU PRINCIPAL:")
        print("1. Créer une vidéo")
        print("2. Statut du système")
        print("3. Sujets suggérés")
        print("4. Quitter")
        print()
        
        choice = input("Votre choix (1-4): ").strip()
        
        if choice == "1":
            keyword = input("\n🎯 Entrez un mot-clé ou sujet (en anglais): ").strip()
            if keyword:
                # Choose video category
                print("\n📂 CATÉGORIES DE VIDÉOS DISPONIBLES:")
                categories = ["nature", "technology", "business", "health", "travel", "food", "auto"]
                for i, cat in enumerate(categories, 1):
                    print(f"   {i}. {cat}")

                cat_choice = input("Choisissez une catégorie (1-7, ou Entrée pour auto): ").strip()
                video_category = None
                if cat_choice.isdigit() and 1 <= int(cat_choice) <= len(categories):
                    video_category = categories[int(cat_choice) - 1]

                # Choose music type
                print("\n🎵 TYPE DE MUSIQUE:")
                print("   1. Instrumental (sans paroles)")
                print("   2. Avec voix/chant")
                print("   3. Pas de musique")

                music_choice = input("Choisissez (1-3): ").strip()
                with_vocals = False
                music_type = "instrumental"

                if music_choice == "2":
                    with_vocals = True
                    music_type = "vocal"
                elif music_choice == "3":
                    music_type = "none"

                print()
                video_path = create_video_from_keyword(keyword, video_category, music_type, with_vocals)
                
                if video_path:
                    print(f"\n✅ Vidéo créée: {video_path}")
                else:
                    print("\n❌ Échec de création")
            else:
                print("❌ Mot-clé requis")
        
        elif choice == "2":
            print()
            show_system_status()
        
        elif choice == "3":
            print()
            create_multiple_videos()
        
        elif choice == "4":
            print("\n👋 Au revoir!")
            break
        
        else:
            print("❌ Choix invalide")
        
        print("\n" + "-" * 50 + "\n")

if __name__ == "__main__":
    # Quick test mode
    import sys
    
    if len(sys.argv) > 1:
        # Command line mode
        keyword = " ".join(sys.argv[1:])
        print(f"🚀 Mode rapide: création vidéo pour '{keyword}'")
        video_path = create_video_from_keyword(keyword)
        if video_path:
            print(f"✅ Succès: {video_path}")
        else:
            print("❌ Échec")
    else:
        # Interactive mode
        main()
