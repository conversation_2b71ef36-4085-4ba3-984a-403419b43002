PIL/.dylibs/libXau.6.0.0.dylib,sha256=6vJqkw0mPKii8VdjgBhn2qH5-Z-9mVI7jZVdOw37WNE,70048
PIL/.dylibs/libbrotlicommon.1.1.0.dylib,sha256=609MK2r7BJHWDIeamG8i0czYNkyZFfUdMeZxd4CyBHI,201200
PIL/.dylibs/libbrotlidec.1.1.0.dylib,sha256=3nMokGsKmS_kS48tSs4oemJ3BZhO3qF3MAgCw7lII_E,104576
PIL/.dylibs/libfreetype.6.dylib,sha256=P2s-1qX1QdkA6p16Zxhu1xrByy8FCUIBF4Sln9wQcgQ,1208416
PIL/.dylibs/libharfbuzz.0.dylib,sha256=OoW2a5ET80ktq75IHZj1r8cJtAB8SBVQYMrP9e5ftyk,4433664
PIL/.dylibs/libjpeg.62.4.0.dylib,sha256=slCJ3CoQ_azOUzDRrXd-y_U9ix64EOQabQ-9J2Wo3sI,637072
PIL/.dylibs/liblcms2.2.dylib,sha256=bE7ATflK2iaSe4MFrwswOiSdF1aYQFVar1LSIel-uQk,557344
PIL/.dylibs/liblzma.5.dylib,sha256=FgKewRIisqlrsC-_VFkqB4vg2JM0BNnMjS6JB_CqhMQ,340784
PIL/.dylibs/libopenjp2.2.5.2.dylib,sha256=-yBVoJfXdnZkPwM3vA2sDe8F4Y_3wXwVJqOIfrAIsP4,699376
PIL/.dylibs/libpng16.16.dylib,sha256=-SrN_JXvm--mr2z4V9hoh_gyxiL9gS0GyQ6tFI36UIY,361424
PIL/.dylibs/libsharpyuv.0.dylib,sha256=Gigv1bDTHrJO3sitKiW3ObpxWhwXuhEZtlRqwHlb86s,86256
PIL/.dylibs/libtiff.6.dylib,sha256=Z7BcBF5_nuND0L03DmDQPnqqUy-38Msp8E1jrKKPrbU,802784
PIL/.dylibs/libwebp.7.dylib,sha256=gnxv35xuawKRFsFt6WrHJB6jy1I9aEzvxK35gLSwv4A,515104
PIL/.dylibs/libwebpdemux.2.dylib,sha256=Tm8m4h4hVHLF76Bc0iDZk4f3SoLKRLSJLB-Q65CZ7x4,69904
PIL/.dylibs/libwebpmux.3.dylib,sha256=lJkxPK_sOclMYSgpDwT8jfFLakYrYZudiOhsSMAFRIs,106400
PIL/.dylibs/libxcb.1.1.0.dylib,sha256=4ThZEHpwm_tT52eduMcdkMHLMlf18-N5x1YHTSjWix8,277696
PIL/.dylibs/libz.1.3.1.dylib,sha256=StQmHJlaH5IHbGkIONGrH8dGzxDCNAdXa6zFeVvAuzk,174848
PIL/BdfFontFile.py,sha256=Hnlsd7gTtt7gaY1ilXHP-co5ehkBQ8xml942d06MEno,3477
PIL/BlpImagePlugin.py,sha256=nWYUT5yejxCpxAl9oAUHaA89Et8CqJ7OGgIwcMI-B40,16366
PIL/BmpImagePlugin.py,sha256=RtDGvt7CrktXhjkCvBMDtwopCYwojwG_Z5m706RTxIs,18565
PIL/BufrStubImagePlugin.py,sha256=PcwNafLqxrQ_RTsz7hAqfpLWcQHT-kXKYPLaB46ZtUA,1753
PIL/ContainerIO.py,sha256=0lHPzJmOqWx2XK_oBQeiwBbr8Y6xT182Y72q_yj6wIo,3181
PIL/CurImagePlugin.py,sha256=sBx86STAUlA-_akYanPSYN2fl5ArGHHOpUjQKwfYHSI,1764
PIL/DcxImagePlugin.py,sha256=K70qkwz-e3nkgjftD5pIElMcU2klHWFTKbINlwgeQ24,2034
PIL/DdsImagePlugin.py,sha256=Ij3vOfKqU0wh60SaE1Doqo3y7BM4sHpkQYTbHGdGdlc,16914
PIL/EpsImagePlugin.py,sha256=7E2GwPy9qdqLxhiGiQg5PpIMtRLaJf2sQ3cM_OEVwqU,16136
PIL/ExifTags.py,sha256=bMq60WOnExBhe-5A_in04puhqZq4E3Bgt5e5k5Hre-o,9753
PIL/FitsImagePlugin.py,sha256=K0SDAgyb9AR6qSEsEJPeysuTacCgfaJU8sCcNaiKMtE,4593
PIL/FliImagePlugin.py,sha256=2qnMYLNrLrPO5jCGM48oJCMQs2veXjy5ZTig2UDR5rE,4611
PIL/FontFile.py,sha256=St7MxO5Q-oakCLWn3ZrgrtaT3wSsmAarxm8AU-G8Moc,3577
PIL/FpxImagePlugin.py,sha256=8pIIfMdyIcREPakLaMcDVad99ha-6t3daADwcxGKwok,7060
PIL/FtexImagePlugin.py,sha256=2sF3hZrbjn2Fx5Lb5fV-8YT-06qu6KEoKR8M1QG2P_c,3502
PIL/GbrImagePlugin.py,sha256=0yR3E8-BJL773L05aAK4SRJ2NtV-6VzG4SxmojLrh-0,2968
PIL/GdImageFile.py,sha256=bHdFfO9rqxiBI9hLpJQVRv0rLj1boWc-nea9rFgIBVs,2795
PIL/GifImagePlugin.py,sha256=PYeLvZlA0sx1FKUYL3T7ce-qUf5ZLamYq66UcTLgCWs,39745
PIL/GimpGradientFile.py,sha256=ABNhtD50Gv82Dn1VxbdgfSIz3Q2_nPiv_zDquOYyVAw,3898
PIL/GimpPaletteFile.py,sha256=mK8RqdS7Ae9W7gZ7NB7MkyzuOqhC6Z09_OsLkxCJoak,1427
PIL/GribStubImagePlugin.py,sha256=hzsipSut7wvQ14P5fx7mkGtLj2TWKZk7OwSiFstB194,1747
PIL/Hdf5StubImagePlugin.py,sha256=6bSeB8RJaWqdU3-xwnJIWtGBZjpM0QnpcM8974UWN90,1750
PIL/IcnsImagePlugin.py,sha256=YsmWyyeqIwbRlfM9yHK2NqSgIqezE43cTjXV023NlSM,12006
PIL/IcoImagePlugin.py,sha256=nTE5xojDHCOYgQ0DKFh1Rvsat7qe7Lhbln-FYL6EpTY,11787
PIL/ImImagePlugin.py,sha256=WgT9fO2MOj7NtKiTA3kx7FcD1-ekh0srcokYWm9COnU,11120
PIL/Image.py,sha256=gPJ52HARC4Rx_-beeVYC9H0sJfRbiF5HTeE2i0aNG7Y,142522
PIL/ImageChops.py,sha256=GEjlymcoDtA5OOeIxQVIX96BD-s6AXhb7TmSLYn2tUg,7946
PIL/ImageCms.py,sha256=J6KTaQ3301Vyk35d99I-ZqvtTmCj1RC-Vz1e-BKjVH0,42024
PIL/ImageColor.py,sha256=IGA9C2umeED_EzS2Cvj6KsU0VutC9RstWIYPe8uDsVk,9441
PIL/ImageDraw.py,sha256=0en6U820tXtUfpEvawuVO_2qYZyu-q_W-OOZmgu-ZN8,41322
PIL/ImageDraw2.py,sha256=3pDY-NrZeZuMjUzou8aKCZp1hH-eD-Wa726omwH2JpQ,6023
PIL/ImageEnhance.py,sha256=kU1z5JhgqA4BVsWcLuvdop0oCnSGviONP31csQlR-xY,3383
PIL/ImageFile.py,sha256=5SofGUBtuftZbFP-MgVpr68Wva7exBJ6LNVAx2z6qSg,25037
PIL/ImageFilter.py,sha256=Lp2XmabZOZRg6OBoqIrwukc0xl-Uqa7HxI2EoYWKCbg,18670
PIL/ImageFont.py,sha256=1s8appa__nJyGEzylzHotRqiMC7jBj-iE13Z65vgAYo,62067
PIL/ImageGrab.py,sha256=B0ujDFUOCKusImJtXtMXXqKbs9YKr21ujfIt1hSy8qQ,6358
PIL/ImageMath.py,sha256=6lPulaD5geLyF7w5oxXVvH8nC_-YD9FXQ0SqRxJsJGQ,11478
PIL/ImageMode.py,sha256=5yOxODAZ7jG03DsUFrt7eQayTtIpWPgvfyhlXDWwcv8,2681
PIL/ImageMorph.py,sha256=c2WliTO5k1PAAFet31W4hmTBDXgbN85TflwnOHIZrjA,8555
PIL/ImageOps.py,sha256=-C-zNKyvf2lBNyEYhxeb_dkIyB0F0GJZzQgz-uVGXEg,25012
PIL/ImagePalette.py,sha256=XDRJOGI6hj8pjfgJBGPb4AWKEXaCf92etcFWgpM3VHs,8970
PIL/ImagePath.py,sha256=5yUG5XCUil1KKTTA_8PgGhcmg-mnue-GK0FwTBlhjw4,371
PIL/ImageQt.py,sha256=k7QCXKgiY5IexiJcSfKy6izQyz08rXbGnA2n3k7Pnts,5906
PIL/ImageSequence.py,sha256=j-i3HxuBqkVFPPZSRerCeRlkAYG0orFkWc9mG5IhYSM,2192
PIL/ImageShow.py,sha256=CX0WcFt6gcwMzbt-RjIsj5zJlQzhck0QzoDJcSjclbk,10028
PIL/ImageStat.py,sha256=S43FZ89r_u4hKCj59lVuWpyVJfhbUy3igXkp9DwaMgM,5325
PIL/ImageTk.py,sha256=-YvO5Twy4ExL8laDQZq2Q3iD02O7OfAdxtz_QCfgLmI,8609
PIL/ImageTransform.py,sha256=Ni41_73Wi1p6fXn37qpGFifvmnbqDjgNPpLXhd4i-AI,3859
PIL/ImageWin.py,sha256=elLKj_3cHyUKNAj3x-T3irSynePWJiy-BqqMVX6Z4Mk,7523
PIL/ImtImagePlugin.py,sha256=akFpmHvG8zULUaIzm97hjf4DvHNtwqv743cFivFZ8bA,2658
PIL/IptcImagePlugin.py,sha256=qyOg_ECIuWLBIEwOSxnAsEqIUixOmdjBfh9Y230SKVE,6135
PIL/Jpeg2KImagePlugin.py,sha256=bBvBCc6fFmzoed9C_px6G2ODv-6bLC4fLGxqaFLfjX8,12401
PIL/JpegImagePlugin.py,sha256=S7pIvPg79UzVGXLjEdkUUrYXMPauF46Hu5tJ71YsFSo,29859
PIL/JpegPresets.py,sha256=WBNUfD0CpQsEEtCPmELxmdFqBAJ6xN5RrYz4QqMfmK0,12422
PIL/McIdasImagePlugin.py,sha256=8ILYNfntSiZzzNuBD2Q4UQtjj3sIywPHVI4EeVXxJIo,1901
PIL/MicImagePlugin.py,sha256=ZGO3fHxD86oc_38y647Qd6875-13iycuzwhcdA3Bfso,2667
PIL/MpegImagePlugin.py,sha256=AplKMsPC9Y9ExJfqTE_xlm8TW-CSx578NGQWyVcmIiQ,2100
PIL/MpoImagePlugin.py,sha256=hjrpIRKF2UlnqD86i0g8qam5eStU-24J5jha__rlnWM,5847
PIL/MspImagePlugin.py,sha256=cuu46H15nZY-uT643KGLS9hS8pLRzT1fQMKTAlXVmMA,5836
PIL/PSDraw.py,sha256=xn_HgAAqVZj6hk1oICB5GjDPXcSIGzyHaB06cgozpXk,6983
PIL/PaletteFile.py,sha256=rC4YrlwwpJtl7RdPDnfl21HR4Vge3cAUw5Z6zicBqIk,1211
PIL/PalmImagePlugin.py,sha256=ejCFU4C0ZhCcvfzUWL4h8_meLlnMA2czTgEXTCJl1p0,9284
PIL/PcdImagePlugin.py,sha256=BXsw4s68ByoQMD1UQjdoPkMQ3M41u_sDclZgKyUPDws,1623
PIL/PcfFontFile.py,sha256=NPZQ0XkbGB8uTlGqgmIPGkwuLMYBdykDeVuvFgIC7JU,7147
PIL/PcxImagePlugin.py,sha256=yNHdqCpKMku4j2yh2A32EYERwJhPcr8_E-7McV4fr6I,6217
PIL/PdfImagePlugin.py,sha256=73vHq63tW4EsSiysSURmX96NhOOtKCKjp1Z18W4Eqe0,8916
PIL/PdfParser.py,sha256=zqeVzciNGv3AIbYKw386D6gx_AVDwg9Z9D0bTjbCTfA,34869
PIL/PixarImagePlugin.py,sha256=4ua-rSZO8m76N0GK_Z5OVEqtORmNK47he22GEaUiyxI,1746
PIL/PngImagePlugin.py,sha256=ZciuIznkaF3Ro6Oecv7iKfdwvZ96WCZtTAcHAJXC2FY,48566
PIL/PpmImagePlugin.py,sha256=FBiEI-l93MquYwCPRl8J-LkrE-EUcdlzQtDaUgZzIy0,12216
PIL/PsdImagePlugin.py,sha256=UZY8pYFq3RGXMkkvttzqLcBQwAeucUBK_9lthX_0AGw,8289
PIL/PyAccess.py,sha256=nI1CO15048im3-Ui9q6KqwacZEtsN3Q_GFwyYVaS5sI,10843
PIL/QoiImagePlugin.py,sha256=dK8g8ClweWcmRdoLadp1fb7Qn0ccTqKlqWIBnpoTCig,4146
PIL/SgiImagePlugin.py,sha256=FRcHgPE9z3wf-6vmovvo0Han-Mi05uhR-cC40A4Xm84,6445
PIL/SpiderImagePlugin.py,sha256=xQaLV9Z0gpxYeLkn7JyVVK_OO7NBfh7N7lBZi8t6CgU,10054
PIL/SunImagePlugin.py,sha256=cIl8g1mDTT7mwYlp-PswH7AHxdrTf8g7CyiLcMlqUUA,4499
PIL/TarIO.py,sha256=gh19oHdyVhgKniKSDsEB2GxiMDT5lgAZfUs-pUQFaWw,1584
PIL/TgaImagePlugin.py,sha256=QKasL7zyr5MZ1POvk71RN8Y7O-q9GHqLKbKqd6BM44w,6942
PIL/TiffImagePlugin.py,sha256=p0i8JZXBtdB_HPuO_7mWFg9hWtr3UcUnkTztClbY-i0,78228
PIL/TiffTags.py,sha256=JeQ9iZH5Vx9rbDhlVO2ETFJgzLdMPfbUbPmTZ345dmo,16715
PIL/WalImageFile.py,sha256=OQ07uBmbAyrKUjxXtLbdv29B6TlSU3EsidJiAH6HuQY,5563
PIL/WebPImagePlugin.py,sha256=0dSTsc_8UB75Wo0zooXiKbAedCop97fhjDapVwc6QDs,11528
PIL/WmfImagePlugin.py,sha256=uZAyCbpr2QANes5CpHQedWM0fCSJarMS8nlrQMKhXkQ,4983
PIL/XVThumbImagePlugin.py,sha256=NbTby5A2QdDemhe6uDFfV1R1gmbpbxWV_-62h0sNyXU,2081
PIL/XbmImagePlugin.py,sha256=X-osASZoiQp7kje0bk2ROFEBzkgciiLnfqOziA7HUtA,2649
PIL/XpmImagePlugin.py,sha256=M9Z4vEuxuQwKypkibk-_dJThrRFI0KkLFEpNGpxs78o,3219
PIL/__init__.py,sha256=fJUwPGhI8_mcB8jNWD-hUw7MiMJyWgqVX_nFtzIj1Zs,2008
PIL/__main__.py,sha256=Lpj4vef8mI7jA1sRCUAoVYaeePD_Uc898xF5c7XLx1A,133
PIL/__pycache__/BdfFontFile.cpython-39.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-39.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-39.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-39.pyc,,
PIL/__pycache__/ContainerIO.cpython-39.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-39.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-39.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-39.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-39.pyc,,
PIL/__pycache__/ExifTags.cpython-39.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-39.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-39.pyc,,
PIL/__pycache__/FontFile.cpython-39.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-39.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-39.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-39.pyc,,
PIL/__pycache__/GdImageFile.cpython-39.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-39.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-39.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-39.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-39.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-39.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-39.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-39.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-39.pyc,,
PIL/__pycache__/Image.cpython-39.pyc,,
PIL/__pycache__/ImageChops.cpython-39.pyc,,
PIL/__pycache__/ImageCms.cpython-39.pyc,,
PIL/__pycache__/ImageColor.cpython-39.pyc,,
PIL/__pycache__/ImageDraw.cpython-39.pyc,,
PIL/__pycache__/ImageDraw2.cpython-39.pyc,,
PIL/__pycache__/ImageEnhance.cpython-39.pyc,,
PIL/__pycache__/ImageFile.cpython-39.pyc,,
PIL/__pycache__/ImageFilter.cpython-39.pyc,,
PIL/__pycache__/ImageFont.cpython-39.pyc,,
PIL/__pycache__/ImageGrab.cpython-39.pyc,,
PIL/__pycache__/ImageMath.cpython-39.pyc,,
PIL/__pycache__/ImageMode.cpython-39.pyc,,
PIL/__pycache__/ImageMorph.cpython-39.pyc,,
PIL/__pycache__/ImageOps.cpython-39.pyc,,
PIL/__pycache__/ImagePalette.cpython-39.pyc,,
PIL/__pycache__/ImagePath.cpython-39.pyc,,
PIL/__pycache__/ImageQt.cpython-39.pyc,,
PIL/__pycache__/ImageSequence.cpython-39.pyc,,
PIL/__pycache__/ImageShow.cpython-39.pyc,,
PIL/__pycache__/ImageStat.cpython-39.pyc,,
PIL/__pycache__/ImageTk.cpython-39.pyc,,
PIL/__pycache__/ImageTransform.cpython-39.pyc,,
PIL/__pycache__/ImageWin.cpython-39.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-39.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-39.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-39.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-39.pyc,,
PIL/__pycache__/JpegPresets.cpython-39.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-39.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-39.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-39.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-39.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PSDraw.cpython-39.pyc,,
PIL/__pycache__/PaletteFile.cpython-39.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PcfFontFile.cpython-39.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PdfParser.cpython-39.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PyAccess.cpython-39.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-39.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-39.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-39.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-39.pyc,,
PIL/__pycache__/TarIO.cpython-39.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-39.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-39.pyc,,
PIL/__pycache__/TiffTags.cpython-39.pyc,,
PIL/__pycache__/WalImageFile.cpython-39.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-39.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-39.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-39.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-39.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-39.pyc,,
PIL/__pycache__/__init__.cpython-39.pyc,,
PIL/__pycache__/__main__.cpython-39.pyc,,
PIL/__pycache__/_binary.cpython-39.pyc,,
PIL/__pycache__/_deprecate.cpython-39.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-39.pyc,,
PIL/__pycache__/_typing.cpython-39.pyc,,
PIL/__pycache__/_util.cpython-39.pyc,,
PIL/__pycache__/_version.cpython-39.pyc,,
PIL/__pycache__/features.cpython-39.pyc,,
PIL/__pycache__/report.cpython-39.pyc,,
PIL/_binary.py,sha256=pcM6AL04GxgmGeLfcH1V1BZHENwIrQH0uxhJ7r0HIL0,2550
PIL/_deprecate.py,sha256=Kt1jv0PTNdqqZksTO2g6XIXgnglkUv3ILRQ8nlP1IKc,2000
PIL/_imaging.cpython-39-darwin.so,sha256=N4D4QwzuoFshHkyMJXV4F8rJpNCkz9nFvbLVlBSPbIE,552784
PIL/_imaging.pyi,sha256=Km4dRUv7jeUTCkx89bsQgRPRe42z1oEGK69t35K-z0A,816
PIL/_imagingcms.cpython-39-darwin.so,sha256=IeFinUAt3LB8Mwm4_oVW2qYogDFemwICm1vxzrUPcKU,80560
PIL/_imagingcms.pyi,sha256=2QvVeGteGKKAtMFizNgvkczRiVPoDL1PYDfEyZ1rPHI,4339
PIL/_imagingft.cpython-39-darwin.so,sha256=S5qM-GVbsxnugAHem2b91mlShEHwlzCw7KMvEDn1AoE,116336
PIL/_imagingft.pyi,sha256=kWCwmA5kqvoPOcVYTVZoNmKj9dWkrkXNvAU5tdsKPdY,1679
PIL/_imagingmath.cpython-39-darwin.so,sha256=Lad303pdaKxrvYyj79KjfPKCI-qXOmpM3S2w6u2W2cY,71872
PIL/_imagingmath.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/_imagingmorph.cpython-39-darwin.so,sha256=_ZbgVBBptfimzQDPLpIANMIVyI6XeiA_WVWLhDmLqXo,51248
PIL/_imagingmorph.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/_imagingtk.cpython-39-darwin.so,sha256=JC2AJCYgy0RmbfTy0IGTmkzsxl9r-XeMaJOIkBuYcO4,52464
PIL/_tkinter_finder.py,sha256=CECvYrzWNc7BuzzR_mWZZKjPdADg6iRG8ilJToyjD3w,540
PIL/_typing.py,sha256=CXp_0BHS0oosbzsAkgFzsn_jtWhFHjhexwyNDi_Vdr4,851
PIL/_util.py,sha256=v7VPRZplBw3JU4o1ilkG5Fh2sSNF1kdRdjf1vhrxwKU,813
PIL/_version.py,sha256=GCP8_FGnOYLQxdMV6rdu1229lOC_zIfYKf-6stQNJSE,87
PIL/_webp.cpython-39-darwin.so,sha256=4t7zbGhAtJiqY8wD8AvhRjY9FR7QzdERGb0APusGFvM,76336
PIL/_webp.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/features.py,sha256=WJTP33hgqKT88aRq0p3grRTE0K5wZ7fCI3pXRZI5dOM,10513
PIL/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
PIL/report.py,sha256=4JY6-IU7sH1RKuRbOvy1fUt0dAoi79FX4tYJN3p1DT0,100
pillow-10.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow-10.4.0.dist-info/LICENSE,sha256=DO-Z-d_U91IsODM1lUFv-5YCPbK4M4_hCIpb_JdwUuQ,60073
pillow-10.4.0.dist-info/METADATA,sha256=Lt2Tz2XQj1cpxT1CLZwdJXvg7v2TbIaveTvy6QphsQA,9167
pillow-10.4.0.dist-info/RECORD,,
pillow-10.4.0.dist-info/WHEEL,sha256=wGb9ImUAQJUTBINSx_Z49LHYKjFqnKf-yeNRdifsLY4,107
pillow-10.4.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
pillow-10.4.0.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
