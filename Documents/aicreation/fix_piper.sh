#!/bin/bash
# fix_piper.sh - Script pour réparer Piper TTS

echo "🎤 RÉPARATION PIPER TTS"
echo "======================"

# Étape 1: Vérifier les fichiers
echo "🔍 Vérification des fichiers..."
if [ ! -d "./piper" ]; then
    echo "❌ Dossier ./piper non trouvé"
    exit 1
fi

if [ ! -f "./piper/piper" ]; then
    echo "❌ Exécutable ./piper/piper non trouvé"
    exit 1
fi

echo "✅ Fichiers Piper trouvés"

# Étape 2: Corriger les permissions
echo "🔧 Correction des permissions..."
chmod +x ./piper/piper
echo "✅ Permissions d'exécution définies"

# Étape 3: Supprimer la quarantaine
echo "🔓 Suppression de la quarantaine..."
xattr -d com.apple.quarantine ./piper/piper 2>/dev/null || echo "⚠️ Pas de quarantaine ou déjà supprimée"
echo "✅ Quarantaine traitée"

# Étape 4: Installer espeak-ng si nécessaire
echo "📦 Vérification espeak-ng..."
if ! command -v espeak-ng &> /dev/null; then
    echo "🔄 Installation espeak-ng..."
    brew install espeak-ng
    echo "✅ espeak-ng installé"
else
    echo "✅ espeak-ng déjà installé"
fi

# Étape 5: Configurer l'environnement et tester
echo "🧪 Test de Piper..."
export DYLD_LIBRARY_PATH="/opt/homebrew/lib:./piper:$DYLD_LIBRARY_PATH"
export DYLD_FALLBACK_LIBRARY_PATH="/opt/homebrew/lib:./piper:$DYLD_FALLBACK_LIBRARY_PATH"
export ESPEAK_DATA_PATH="./piper/espeak-ng-data"

# Test simple
echo "🔄 Test --version..."
timeout 10s ./piper/piper --version

if [ $? -eq 0 ]; then
    echo "✅ Piper fonctionne!"
else
    echo "❌ Piper ne fonctionne pas"
    echo "🔧 Diagnostic supplémentaire..."
    
    # Vérifier les bibliothèques
    echo "📚 Vérification des bibliothèques..."
    otool -L ./piper/piper | head -10
    
    # Vérifier espeak-ng
    echo "🔍 Localisation espeak-ng..."
    which espeak-ng
    ls -la /opt/homebrew/lib/libespeak* 2>/dev/null || echo "❌ Bibliothèques espeak non trouvées"
fi

echo "🎯 Réparation terminée"
