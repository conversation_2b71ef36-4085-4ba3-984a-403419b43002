# 🚀 Enhanced Auto YouTube Video Generator

Un système professionnel de création automatique de vidéos YouTube utilisant l'IA et Blender.

## ✨ Fonctionnalités

### 🎬 Génération de Vidéos Professionnelles
- **Blender Integration**: Création de vidéos 3D professionnelles avec animations
- **FFmpeg Fallback**: Système de secours robuste pour la création de vidéos
- **Qualité HD**: Vidéos en 1920x1080 avec audio haute qualité

### 🤖 IA et Script Generation
- **Ollama Integration**: Utilise des modèles IA locaux (phi3, mistral, etc.)
- **Scripts Intelligents**: Génération de scripts de 3 minutes structurés
- **Fallback System**: Scripts de secours si l'IA n'est pas disponible

### 🎵 Audio Professionnel
- **gTTS + pydub**: Synthèse vocale avec amélioration audio
- **Normalisation**: Ajustement automatique des niveaux audio
- **Compression**: Optimisation de la qualité sonore

### 🖼️ Miniatures Attrayantes
- **Gradients**: Arrière-plans dégradés professionnels
- **Typography**: Texte avec ombres et effets visuels
- **Play Button**: Bouton de lecture intégré

### 🔧 Architecture Modulaire
- **Configuration**: Système de configuration centralisé
- **Logging**: Journalisation complète avec niveaux
- **Error Handling**: Gestion d'erreurs robuste avec fallbacks

## 📁 Structure du Projet

```
Documents/aicreation/
├── config.py                    # Configuration centralisée
├── ollama_manager.py            # Gestion d'Ollama et IA
├── audio_manager.py             # Traitement audio avancé
├── video_manager.py             # Création de vidéos
├── enhanced_auto_youtube.py     # Script principal amélioré
├── blender_simple.py           # Script Blender optimisé
├── test_single_keyword.py      # Test d'un mot-clé
├── demo.py                     # Démonstration interactive
├── simple_auto_youtube.py      # Version originale (legacy)
└── output/                     # Fichiers générés
    ├── videos/                 # Vidéos MP4
    ├── audio/                  # Fichiers audio MP3
    ├── thumbnails/             # Miniatures JPG
    ├── scripts/                # Scripts JSON
    ├── transcripts/            # Transcriptions
    ├── temp/                   # Fichiers temporaires
    └── logs/                   # Journaux
```

## 🚀 Installation

### Prérequis
- Python 3.8+
- Blender (pour les vidéos professionnelles)
- Ollama (pour la génération de scripts IA)
- FFmpeg (pour le traitement vidéo)

### Dépendances Python
```bash
pip install gtts pydub pillow pytrends requests
pip install google-api-python-client google-auth-oauthlib
pip install ollama
```

### Installation d'Ollama
```bash
# Installer Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Télécharger des modèles
ollama pull phi3
ollama pull mistral
```

## 🎮 Utilisation

### Démonstration Interactive
```bash
python demo.py
```

### Test d'un Mot-clé
```bash
python test_single_keyword.py "intelligence artificielle"
```

### Script Principal
```bash
python enhanced_auto_youtube.py
```

### Traitement d'un Mot-clé Spécifique
```bash
python demo.py "énergie renouvelable"
```

## ⚙️ Configuration

Modifiez `config.py` pour personnaliser :

```python
# Modèles Ollama disponibles
OLLAMA_CONFIG = {
    "model": "phi3",
    "available_models": ["phi3", "mistral", "qwen2.5-coder"],
    "timeout": 300
}

# Qualité vidéo
VIDEO_CONFIG = {
    "resolution": {"width": 1920, "height": 1080},
    "fps": 30,
    "quality": "high"
}

# Mots-clés de secours
FALLBACK_KEYWORDS = [
    "intelligence artificielle",
    "changement climatique",
    "exploration spatiale"
]
```

## 📊 Exemples de Résultats

### Vidéos Générées
- **space_exploration.mp4** (1.1 MB) - Exploration spatiale
- **renewable_energy.mp4** (1.2 MB) - Énergies renouvelables
- **artificial_intelligence.mp4** - Intelligence artificielle

### Scripts IA Générés
```json
{
    "title": "Beyond Earth: The Marvels and Missions of Space Exploration",
    "introduction": "Welcome to a journey beyond our home planet!",
    "key_point_1": "Historical Milestones: Our story begins with Yuri Gagarin...",
    "key_point_2": "Technological Triumphs: Technology has been pivotal...",
    "key_point_3": "Future Frontiers: But we're not done yet!",
    "conclusion": "Space is not just a destination; it's a canvas..."
}
```

## 🔍 Fonctionnalités Avancées

### Gestion d'Erreurs
- **Fallback Systems**: Plusieurs niveaux de secours
- **Retry Logic**: Tentatives multiples avec différents modèles
- **Validation**: Vérification de la qualité des fichiers générés

### Performance
- **Parallel Processing**: Traitement optimisé
- **Memory Management**: Gestion efficace de la mémoire
- **Temp Cleanup**: Nettoyage automatique des fichiers temporaires

### Monitoring
- **Detailed Logging**: Journalisation complète
- **Progress Tracking**: Suivi de la progression
- **Error Reporting**: Rapports d'erreurs détaillés

## 🎯 Améliorations Apportées

### Par rapport à la version originale :

1. **Architecture Modulaire** : Code organisé en modules spécialisés
2. **Ollama Integration** : Génération de scripts IA de haute qualité
3. **Audio Enhancement** : Traitement audio professionnel avec pydub
4. **Blender Support** : Vidéos 3D professionnelles
5. **Configuration System** : Paramètres centralisés et flexibles
6. **Error Handling** : Gestion d'erreurs robuste avec fallbacks
7. **Logging System** : Journalisation complète et structurée
8. **Validation** : Vérification de la qualité des fichiers
9. **Performance** : Optimisations et gestion de la mémoire
10. **User Experience** : Interface améliorée et démonstrations

## 🛠️ Dépannage

### Problèmes Courants

**Ollama non disponible**
```bash
# Vérifier l'installation
ollama list
# Redémarrer le service
ollama serve
```

**Blender non trouvé**
```bash
# Vérifier le chemin dans config.py
BLENDER_CONFIG["executable"] = "/path/to/blender"
```

**Erreurs FFmpeg**
```bash
# Installer FFmpeg
brew install ffmpeg  # macOS
sudo apt install ffmpeg  # Ubuntu
```

## 📈 Statistiques

- **Temps de traitement** : 30-60 secondes par vidéo
- **Qualité audio** : 192 kbps MP3
- **Résolution vidéo** : 1920x1080 HD
- **Taille moyenne** : 1-2 MB par minute de vidéo

## 🤝 Contribution

Pour contribuer au projet :
1. Fork le repository
2. Créez une branche feature
3. Committez vos changements
4. Créez une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

## 🙏 Remerciements

- **Ollama** pour l'intégration IA locale
- **Blender** pour les capacités 3D
- **gTTS** pour la synthèse vocale
- **pydub** pour le traitement audio
- **FFmpeg** pour le traitement vidéo
