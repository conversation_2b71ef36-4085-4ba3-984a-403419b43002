{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/pubsub": {"description": "View and manage Pub/Sub topics and subscriptions"}}}}, "basePath": "", "baseUrl": "https://pubsub.googleapis.com/", "batchPath": "batch", "canonicalName": "Pubsub", "description": "Provides reliable, many-to-many, asynchronous messaging between applications. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/pubsub/docs", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://pubsub.me-central2.rep.googleapis.com/", "location": "me-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.europe-west3.rep.googleapis.com/", "location": "europe-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.europe-west8.rep.googleapis.com/", "location": "europe-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.europe-west9.rep.googleapis.com/", "location": "europe-west9"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-central1.rep.googleapis.com/", "location": "us-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-central2.rep.googleapis.com/", "location": "us-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-east1.rep.googleapis.com/", "location": "us-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-east4.rep.googleapis.com/", "location": "us-east4"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-east5.rep.googleapis.com/", "location": "us-east5"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-south1.rep.googleapis.com/", "location": "us-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-west1.rep.googleapis.com/", "location": "us-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-west2.rep.googleapis.com/", "location": "us-west2"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-west3.rep.googleapis.com/", "location": "us-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://pubsub.us-west4.rep.googleapis.com/", "location": "us-west4"}], "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "pubsub:v1beta2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://pubsub.mtls.googleapis.com/", "name": "pubsub", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"subscriptions": {"methods": {"acknowledge": {"description": "Acknowledges the messages associated with the `ack_ids` in the `AcknowledgeRequest`. The Pub/Sub system can remove the relevant messages from the subscription. Acknowledging a message whose ack deadline has expired may succeed, but such a message may be redelivered later. Acknowledging a message more than once will not result in an error.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}:acknowledge", "httpMethod": "POST", "id": "pubsub.projects.subscriptions.acknowledge", "parameterOrder": ["subscription"], "parameters": {"subscription": {"description": "The subscription whose message is being acknowledged.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+subscription}:acknowledge", "request": {"$ref": "AcknowledgeRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "create": {"description": "Creates a subscription to a given topic. If the subscription already exists, returns `ALREADY_EXISTS`. If the corresponding topic doesn't exist, returns `NOT_FOUND`. If the name is not provided in the request, the server will assign a random name for this subscription on the same project as the topic. Note that for REST API requests, you must specify a name.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}", "httpMethod": "PUT", "id": "pubsub.projects.subscriptions.create", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the subscription. It must have the format `\"projects/{project}/subscriptions/{subscription}\"`. `{subscription}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "Subscription"}, "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "delete": {"description": "Deletes an existing subscription. All pending messages in the subscription are immediately dropped. Calls to `Pull` after deletion will return `NOT_FOUND`. After a subscription is deleted, a new one may be created with the same name, but the new one has no association with the old subscription, or its topic unless the same topic is specified.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}", "httpMethod": "DELETE", "id": "pubsub.projects.subscriptions.delete", "parameterOrder": ["subscription"], "parameters": {"subscription": {"description": "The subscription to delete.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+subscription}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "get": {"description": "Gets the configuration details of a subscription.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}", "httpMethod": "GET", "id": "pubsub.projects.subscriptions.get", "parameterOrder": ["subscription"], "parameters": {"subscription": {"description": "The name of the subscription to get.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+subscription}", "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}:getIamPolicy", "httpMethod": "GET", "id": "pubsub.projects.subscriptions.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "list": {"description": "Lists matching subscriptions.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions", "httpMethod": "GET", "id": "pubsub.projects.subscriptions.list", "parameterOrder": ["project"], "parameters": {"pageSize": {"description": "Maximum number of subscriptions to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListSubscriptionsResponse`; indicates that this is a continuation of a prior `ListSubscriptions` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "project": {"description": "The name of the cloud project that subscriptions belong to.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+project}/subscriptions", "response": {"$ref": "ListSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "modifyAckDeadline": {"description": "Modifies the ack deadline for a specific message. This method is useful to indicate that more time is needed to process a message by the subscriber, or to make the message available for redelivery if the processing was interrupted. Note that this does not modify the subscription-level `ackDeadlineSeconds` used for subsequent messages.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}:modifyAckDeadline", "httpMethod": "POST", "id": "pubsub.projects.subscriptions.modifyAckDeadline", "parameterOrder": ["subscription"], "parameters": {"subscription": {"description": "The name of the subscription.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+subscription}:modifyAckDeadline", "request": {"$ref": "ModifyAckDeadlineRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "modifyPushConfig": {"description": "Modifies the `PushConfig` for a specified subscription. This may be used to change a push subscription to a pull one (signified by an empty `PushConfig`) or vice versa, or change the endpoint URL and other attributes of a push subscription. Messages will accumulate for delivery continuously through the call regardless of changes to the `PushConfig`.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}:modifyPushConfig", "httpMethod": "POST", "id": "pubsub.projects.subscriptions.modifyPushConfig", "parameterOrder": ["subscription"], "parameters": {"subscription": {"description": "The name of the subscription.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+subscription}:modifyPushConfig", "request": {"$ref": "ModifyPushConfigRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "pull": {"description": "Pulls messages from the server. Returns an empty list if there are no messages available in the backlog. The server may return `UNAVAILABLE` if there are too many concurrent pull requests pending for the given subscription.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}:pull", "httpMethod": "POST", "id": "pubsub.projects.subscriptions.pull", "parameterOrder": ["subscription"], "parameters": {"subscription": {"description": "The subscription from which messages should be pulled.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+subscription}:pull", "request": {"$ref": "PullRequest"}, "response": {"$ref": "PullResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}:setIamPolicy", "httpMethod": "POST", "id": "pubsub.projects.subscriptions.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1beta2/projects/{projectsId}/subscriptions/{subscriptionsId}:testIamPermissions", "httpMethod": "POST", "id": "pubsub.projects.subscriptions.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}}}, "topics": {"methods": {"create": {"description": "Creates the given topic with the given name.", "flatPath": "v1beta2/projects/{projectsId}/topics/{topicsId}", "httpMethod": "PUT", "id": "pubsub.projects.topics.create", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the topic. It must have the format `\"projects/{project}/topics/{topic}\"`. `{topic}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "Topic"}, "response": {"$ref": "Topic"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "delete": {"description": "Deletes the topic with the given name. Returns `NOT_FOUND` if the topic does not exist. After a topic is deleted, a new topic may be created with the same name; this is an entirely new topic with none of the old configuration or subscriptions. Existing subscriptions to this topic are not deleted, but their `topic` field is set to `_deleted-topic_`.", "flatPath": "v1beta2/projects/{projectsId}/topics/{topicsId}", "httpMethod": "DELETE", "id": "pubsub.projects.topics.delete", "parameterOrder": ["topic"], "parameters": {"topic": {"description": "Name of the topic to delete.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+topic}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "get": {"description": "Gets the configuration of a topic.", "flatPath": "v1beta2/projects/{projectsId}/topics/{topicsId}", "httpMethod": "GET", "id": "pubsub.projects.topics.get", "parameterOrder": ["topic"], "parameters": {"topic": {"description": "The name of the topic to get.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+topic}", "response": {"$ref": "Topic"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1beta2/projects/{projectsId}/topics/{topicsId}:getIamPolicy", "httpMethod": "GET", "id": "pubsub.projects.topics.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "list": {"description": "Lists matching topics.", "flatPath": "v1beta2/projects/{projectsId}/topics", "httpMethod": "GET", "id": "pubsub.projects.topics.list", "parameterOrder": ["project"], "parameters": {"pageSize": {"description": "Maximum number of topics to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListTopicsResponse`; indicates that this is a continuation of a prior `ListTopics` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "project": {"description": "The name of the cloud project that topics belong to.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+project}/topics", "response": {"$ref": "ListTopicsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "publish": {"description": "Adds one or more messages to the topic. Returns `NOT_FOUND` if the topic does not exist. The message payload must not be empty; it must contain either a non-empty data field, or at least one attribute.", "flatPath": "v1beta2/projects/{projectsId}/topics/{topicsId}:publish", "httpMethod": "POST", "id": "pubsub.projects.topics.publish", "parameterOrder": ["topic"], "parameters": {"topic": {"description": "The messages in the request will be published on this topic.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+topic}:publish", "request": {"$ref": "PublishRequest"}, "response": {"$ref": "PublishResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1beta2/projects/{projectsId}/topics/{topicsId}:setIamPolicy", "httpMethod": "POST", "id": "pubsub.projects.topics.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1beta2/projects/{projectsId}/topics/{topicsId}:testIamPermissions", "httpMethod": "POST", "id": "pubsub.projects.topics.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}}, "resources": {"subscriptions": {"methods": {"list": {"description": "Lists the name of the subscriptions for this topic.", "flatPath": "v1beta2/projects/{projectsId}/topics/{topicsId}/subscriptions", "httpMethod": "GET", "id": "pubsub.projects.topics.subscriptions.list", "parameterOrder": ["topic"], "parameters": {"pageSize": {"description": "Maximum number of subscription names to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListTopicSubscriptionsResponse`; indicates that this is a continuation of a prior `ListTopicSubscriptions` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "topic": {"description": "The name of the topic that subscriptions are attached to.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+topic}/subscriptions", "response": {"$ref": "ListTopicSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}}}}}}}}, "revision": "20240730", "rootUrl": "https://pubsub.googleapis.com/", "schemas": {"AcknowledgeRequest": {"description": "Request for the Acknowledge method.", "id": "AcknowledgeRequest", "properties": {"ackIds": {"description": "The acknowledgment ID for the messages being acknowledged that was returned by the Pub/Sub system in the `Pull` response. Must not be empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ListSubscriptionsResponse": {"description": "Response for the `ListSubscriptions` method.", "id": "ListSubscriptionsResponse", "properties": {"nextPageToken": {"description": "If not empty, indicates that there may be more subscriptions that match the request; this value should be passed in a new `ListSubscriptionsRequest` to get more subscriptions.", "type": "string"}, "subscriptions": {"description": "The subscriptions that match the request.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "ListTopicSubscriptionsResponse": {"description": "Response for the `ListTopicSubscriptions` method.", "id": "ListTopicSubscriptionsResponse", "properties": {"nextPageToken": {"description": "If not empty, indicates that there may be more subscriptions that match the request; this value should be passed in a new `ListTopicSubscriptionsRequest` to get more subscriptions.", "type": "string"}, "subscriptions": {"description": "The names of the subscriptions that match the request.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTopicsResponse": {"description": "Response for the `ListTopics` method.", "id": "ListTopicsResponse", "properties": {"nextPageToken": {"description": "If not empty, indicates that there may be more topics that match the request; this value should be passed in a new `ListTopicsRequest`.", "type": "string"}, "topics": {"description": "The resulting topics.", "items": {"$ref": "Topic"}, "type": "array"}}, "type": "object"}, "ModifyAckDeadlineRequest": {"description": "Request for the ModifyAckDeadline method.", "id": "ModifyAckDeadlineRequest", "properties": {"ackDeadlineSeconds": {"description": "The new ack deadline with respect to the time this request was sent to the Pub/Sub system. Must be >= 0. For example, if the value is 10, the new ack deadline will expire 10 seconds after the `ModifyAckDeadline` call was made. Specifying zero may immediately make the message available for another pull request.", "format": "int32", "type": "integer"}, "ackId": {"description": "The acknowledgment ID. Either this or ack_ids must be populated, but not both.", "type": "string"}, "ackIds": {"description": "List of acknowledgment IDs.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ModifyPushConfigRequest": {"description": "Request for the ModifyPushConfig method.", "id": "ModifyPushConfigRequest", "properties": {"pushConfig": {"$ref": "PushConfig", "description": "The push configuration for future deliveries. An empty `pushConfig` indicates that the Pub/Sub system should stop pushing messages from the given subscription and allow messages to be pulled and acknowledged - effectively pausing the subscription if `Pull` is not called."}}, "type": "object"}, "OidcToken": {"description": "Contains information needed for generating an [OpenID Connect token](https://developers.google.com/identity/protocols/OpenIDConnect).", "id": "OidcToken", "properties": {"audience": {"description": "Audience to be used when generating OIDC token. The audience claim identifies the recipients that the JWT is intended for. The audience value is a single case-sensitive string. Having multiple values (array) for the audience field is not supported. More info about the OIDC JWT token audience here: https://tools.ietf.org/html/rfc7519#section-4.1.3 Note: if not specified, the Push endpoint URL will be used.", "type": "string"}, "serviceAccountEmail": {"description": "[Service account email](https://cloud.google.com/iam/docs/service-accounts) to be used for generating the OIDC token. The caller (for CreateSubscription, UpdateSubscription, and ModifyPushConfig RPCs) must have the iam.serviceAccounts.actAs permission for the service account.", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PublishRequest": {"description": "Request for the Publish method.", "id": "PublishRequest", "properties": {"messages": {"description": "The messages to publish.", "items": {"$ref": "PubsubMessage"}, "type": "array"}}, "type": "object"}, "PublishResponse": {"description": "Response for the `Publish` method.", "id": "PublishResponse", "properties": {"messageIds": {"description": "The server-assigned ID of each published message, in the same order as the messages in the request. IDs are guaranteed to be unique within the topic.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PubsubMessage": {"description": "A message data and its attributes. The message payload must not be empty; it must contain either a non-empty data field, or at least one attribute.", "id": "PubsubMessage", "properties": {"attributes": {"additionalProperties": {"type": "string"}, "description": "Optional attributes for this message.", "type": "object"}, "data": {"description": "The message payload. For JSON requests, the value of this field must be [base64-encoded](https://tools.ietf.org/html/rfc4648).", "format": "byte", "type": "string"}, "messageId": {"description": "ID of this message, assigned by the server when the message is published. Guaranteed to be unique within the topic. This value may be read by a subscriber that receives a `PubsubMessage` via a `Pull` call or a push delivery. It must not be populated by the publisher in a `Publish` call.", "type": "string"}, "publishTime": {"description": "The time at which the message was published, populated by the server when it receives the `Publish` call. It must not be populated by the publisher in a `Publish` call.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "PullRequest": {"description": "Request for the `Pull` method.", "id": "PullRequest", "properties": {"maxMessages": {"description": "The maximum number of messages returned for this request. The Pub/Sub system may return fewer than the number specified.", "format": "int32", "type": "integer"}, "returnImmediately": {"deprecated": true, "description": "Optional. If this is specified as true the system will respond immediately even if it is not able to return a message in the `Pull` response. Otherwise the system is allowed to wait until at least one message is available rather than returning no messages. The client may cancel the request if it does not wish to wait any longer for the response. Warning: setting this field to `true` is discouraged because it adversely impacts the performance of `Pull` operations. We recommend that users do not set this field.", "type": "boolean"}}, "type": "object"}, "PullResponse": {"description": "Response for the `Pull` method.", "id": "PullResponse", "properties": {"receivedMessages": {"description": "Received Pub/Sub messages. The Pub/Sub system will return zero messages if there are no more available in the backlog. The Pub/Sub system may return fewer than the `maxMessages` requested even if there are more messages available in the backlog.", "items": {"$ref": "ReceivedMessage"}, "type": "array"}}, "type": "object"}, "PushConfig": {"description": "Configuration for a push delivery endpoint.", "id": "PushConfig", "properties": {"attributes": {"additionalProperties": {"type": "string"}, "description": "Endpoint configuration attributes. Every endpoint has a set of API supported attributes that can be used to control different aspects of the message delivery. The currently supported attribute is `x-goog-version`, which you can use to change the format of the push message. This attribute indicates the version of the data expected by the endpoint. This controls the shape of the envelope (i.e. its fields and metadata). The endpoint version is based on the version of the Pub/Sub API. If not present during the `CreateSubscription` call, it will default to the version of the API used to make such call. If not present during a `ModifyPushConfig` call, its value will not be changed. `GetSubscription` calls will always return a valid version, even if the subscription was created without this attribute. The possible values for this attribute are: * `v1beta1`: uses the push format defined in the v1beta1 Pub/Sub API. * `v1` or `v1beta2`: uses the push format defined in the v1 Pub/Sub API.", "type": "object"}, "oidcToken": {"$ref": "OidcToken", "description": "If specified, Pub/Sub will generate and attach an OIDC JWT token as an `Authorization` header in the HTTP request for every pushed message."}, "pushEndpoint": {"description": "A URL locating the endpoint to which messages should be pushed. For example, a Webhook endpoint might use \"https://example.com/push\".", "type": "string"}}, "type": "object"}, "ReceivedMessage": {"description": "A message and its corresponding acknowledgment ID.", "id": "ReceivedMessage", "properties": {"ackId": {"description": "This ID can be used to acknowledge the received message.", "type": "string"}, "message": {"$ref": "PubsubMessage", "description": "The message."}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}}, "type": "object"}, "Subscription": {"description": "A subscription resource.", "id": "Subscription", "properties": {"ackDeadlineSeconds": {"description": "This value is the maximum time after a subscriber receives a message before the subscriber should acknowledge the message. After message delivery but before the ack deadline expires and before the message is acknowledged, it is an outstanding message and will not be delivered again during that time (on a best-effort basis). For pull subscriptions, this value is used as the initial value for the ack deadline. To override this value for a given message, call `ModifyAckDeadline` with the corresponding `ack_id` if using pull. The maximum custom deadline you can specify is 600 seconds (10 minutes). For push delivery, this value is also used to set the request timeout for the call to the push endpoint. If the subscriber never acknowledges the message, the Pub/Sub system will eventually redeliver the message. If this parameter is 0, a default value of 10 seconds is used.", "format": "int32", "type": "integer"}, "name": {"description": "The name of the subscription. It must have the format `\"projects/{project}/subscriptions/{subscription}\"`. `{subscription}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`.", "type": "string"}, "pushConfig": {"$ref": "PushConfig", "description": "If push delivery is used with this subscription, this field is used to configure it. An empty `pushConfig` signifies that the subscriber will pull and ack messages using API methods."}, "topic": {"description": "The name of the topic from which this subscription is receiving messages. The value of this field will be `_deleted-topic_` if the topic has been deleted.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Topic": {"description": "A topic resource.", "id": "Topic", "properties": {"name": {"description": "The name of the topic. It must have the format `\"projects/{project}/topics/{topic}\"`. `{topic}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Pub/Sub API", "version": "v1beta2"}