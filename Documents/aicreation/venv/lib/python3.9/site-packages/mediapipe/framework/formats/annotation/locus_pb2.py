# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/annotation/locus.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework.formats.annotation import rasterization_pb2 as mediapipe_dot_framework_dot_formats_dot_annotation_dot_rasterization__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n2mediapipe/framework/formats/annotation/locus.proto\x12\tmediapipe\x1a:mediapipe/framework/formats/annotation/rasterization.proto\"\xdf\x02\n\x05Locus\x12.\n\nlocus_type\x18\x01 \x01(\x0e\x32\x1a.mediapipe.Locus.LocusType\x12\x10\n\x08locus_id\x18\x02 \x01(\x06\x12\x15\n\rlocus_id_seed\x18\x06 \x01(\x06\x12\x1c\n\x0e\x63oncatenatable\x18\x05 \x01(\x08:\x04true\x12,\n\x0c\x62ounding_box\x18\x03 \x01(\x0b\x32\x16.mediapipe.BoundingBox\x12\x15\n\ttimestamp\x18\x07 \x01(\x05:\x02-1\x12(\n\x06region\x18\x04 \x01(\x0b\x32\x18.mediapipe.Rasterization\x12)\n\x0f\x63omponent_locus\x18\x08 \x03(\x0b\x32\x10.mediapipe.Locus\"E\n\tLocusType\x12\n\n\x06GLOBAL\x10\x01\x12\x10\n\x0c\x42OUNDING_BOX\x10\x02\x12\n\n\x06REGION\x10\x03\x12\x0e\n\nVIDEO_TUBE\x10\x04\"P\n\x0b\x42oundingBox\x12\x0e\n\x06left_x\x18\x01 \x01(\x05\x12\x0f\n\x07upper_y\x18\x02 \x01(\x05\x12\x0f\n\x07right_x\x18\x03 \x01(\x05\x12\x0f\n\x07lower_y\x18\x04 \x01(\x05\x42\x03\xf8\x01\x01')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.annotation.locus_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\370\001\001'
  _globals['_LOCUS']._serialized_start=126
  _globals['_LOCUS']._serialized_end=477
  _globals['_LOCUS_LOCUSTYPE']._serialized_start=408
  _globals['_LOCUS_LOCUSTYPE']._serialized_end=477
  _globals['_BOUNDINGBOX']._serialized_start=479
  _globals['_BOUNDINGBOX']._serialized_end=559
# @@protoc_insertion_point(module_scope)
