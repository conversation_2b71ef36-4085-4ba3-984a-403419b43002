#!/usr/bin/env python3
"""
music_manager.py - Gestionnaire de musique douce pour vidéos nature
"""

import os
import logging
import subprocess
import tempfile
from pathlib import Path
from typing import Optional, List
import random

from config import get_config

logger = logging.getLogger(__name__)

class MusicManager:
    """Gestionnaire de musique douce sans percussion pour vidéos nature."""
    
    def __init__(self):
        """Initialize music manager."""
        self.config = get_config()
        
        # Types de musique disponibles
        self.music_types = {
            "instrumental": {
                "description": "Musique instrumentale sans paroles",
                "has_vocals": False,
                "styles": ["ambient", "classical", "nature", "electronic", "cinematic"]
            },
            "vocal": {
                "description": "Musique avec paroles/voix",
                "has_vocals": True,
                "styles": ["choral", "world", "meditation_vocal", "spiritual"]
            },
            "nature_sounds": {
                "description": "Sons de la nature purs",
                "has_vocals": False,
                "styles": ["rain", "ocean", "forest", "birds", "wind"]
            }
        }

        # Configurations de musique instrumentale (sans paroles)
        self.instrumental_configs = {
            "ambient": {
                "base_freq": 432,
                "harmonics": [432, 648, 864],
                "volume": 0.25,
                "description": "Ambient électronique apaisant"
            },
            "classical": {
                "base_freq": 440,
                "harmonics": [440, 660, 880],
                "volume": 0.3,
                "description": "Harmonies classiques douces"
            },
            "nature": {
                "base_freq": 396,
                "harmonics": [396, 528, 741],
                "volume": 0.2,
                "description": "Fréquences naturelles apaisantes"
            },
            "electronic": {
                "base_freq": 528,
                "harmonics": [528, 792, 1056],
                "volume": 0.25,
                "description": "Électronique minimaliste"
            },
            "cinematic": {
                "base_freq": 285,
                "harmonics": [285, 417, 639],
                "volume": 0.35,
                "description": "Ambiance cinématographique"
            }
        }

        # Configurations de musique avec voix (paroles/chants)
        self.vocal_configs = {
            "choral": {
                "base_freq": 261,  # Do central
                "harmonics": [261, 329, 392, 523],  # Accord majeur
                "volume": 0.4,
                "description": "Chœur harmonieux",
                "vocal_layer": True
            },
            "world": {
                "base_freq": 220,  # La
                "harmonics": [220, 330, 440, 660],
                "volume": 0.35,
                "description": "Musique du monde avec voix",
                "vocal_layer": True
            },
            "meditation_vocal": {
                "base_freq": 174,  # Fréquence de guérison
                "harmonics": [174, 285, 396, 528],
                "volume": 0.3,
                "description": "Méditation avec mantras",
                "vocal_layer": True
            },
            "spiritual": {
                "base_freq": 963,  # Fréquence spirituelle
                "harmonics": [963, 741, 528, 396],
                "volume": 0.25,
                "description": "Musique spirituelle avec voix",
                "vocal_layer": True
            }
        }
        
        logger.info("Music Manager initialized with nature frequencies")
    
    def generate_music(self, music_type: str = "instrumental", style: str = "nature",
                      duration: float = 180, with_vocals: bool = False) -> Optional[str]:
        """Generate music with specified type and style."""
        try:
            # Determine configuration based on type and vocals preference
            if with_vocals and music_type == "instrumental":
                music_type = "vocal"
            elif not with_vocals and music_type == "vocal":
                music_type = "instrumental"

            # Get configuration
            if with_vocals and style in self.vocal_configs:
                config = self.vocal_configs[style]
            elif not with_vocals and style in self.instrumental_configs:
                config = self.instrumental_configs[style]
            else:
                # Default to nature instrumental
                config = self.instrumental_configs["nature"]
                style = "nature"
            
            # Create output path
            vocals_suffix = "_vocal" if with_vocals else "_instrumental"
            output_path = self.config["directories"]["temp"] / f"music_{style}{vocals_suffix}.mp3"

            logger.info(f"Generating {style} music: {config['description']} (vocals: {with_vocals})")

            # Generate music
            if with_vocals and config.get("vocal_layer"):
                return self._generate_vocal_music(config, duration, output_path)
            else:
                return self._generate_harmonic_music(config, duration, output_path)

        except Exception as e:
            logger.error(f"Error generating music: {e}")
            return None

    def generate_nature_music(self, style: str = "nature", duration: float = 180) -> Optional[str]:
        """Generate nature music (backward compatibility)."""
        return self.generate_music("instrumental", style, duration, False)
    
    def _generate_harmonic_music(self, config: dict, duration: float, output_path: Path) -> Optional[str]:
        """Generate harmonic music using sine waves."""
        try:
            base_freq = config["base_freq"]
            harmonics = config["harmonics"]
            volume = config["volume"]
            
            # Create multiple sine wave layers for rich harmonic content
            sine_waves = []
            
            # Base frequency (strongest)
            sine_waves.append(f"sine=frequency={base_freq}:duration={duration}")
            
            # Harmonics (softer)
            for i, freq in enumerate(harmonics):
                vol_factor = 0.7 ** (i + 1)  # Decreasing volume for harmonics
                sine_waves.append(f"sine=frequency={freq}:duration={duration}")
            
            # Add some natural variation (very subtle)
            # Low frequency for depth
            sine_waves.append(f"sine=frequency={base_freq/4}:duration={duration}")
            
            # Create filter complex for mixing
            inputs = []
            mix_inputs = []
            
            for i, wave in enumerate(sine_waves):
                inputs.extend(["-f", "lavfi", "-i", wave])
                vol = volume if i == 0 else volume * (0.6 ** i)
                mix_inputs.append(f"[{i}]volume={vol}[a{i}]")
            
            # Mix all waves
            mix_filter = ";".join(mix_inputs)
            amix_inputs = "".join([f"[a{i}]" for i in range(len(sine_waves))])
            mix_filter += f";{amix_inputs}amix=inputs={len(sine_waves)}:duration=longest[out]"
            
            # FFmpeg command
            cmd = ["ffmpeg", "-y"] + inputs + [
                "-filter_complex", mix_filter,
                "-map", "[out]",
                "-t", str(duration),
                "-c:a", "mp3",
                "-b:a", "128k",
                str(output_path)
            ]
            
            logger.info(f"Generating harmonic music with {len(sine_waves)} layers")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"Generated nature music: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"FFmpeg music generation failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating harmonic music: {e}")
            return None

    def _generate_vocal_music(self, config: dict, duration: float, output_path: Path) -> Optional[str]:
        """Generate music with vocal elements."""
        try:
            # For now, generate harmonic base and add vocal-like frequencies
            base_freq = config["base_freq"]
            harmonics = config["harmonics"]
            volume = config["volume"]

            # Create vocal-like frequencies (formants)
            vocal_formants = [
                base_freq * 1.5,  # First formant
                base_freq * 2.5,  # Second formant
                base_freq * 3.5   # Third formant
            ]

            # Combine harmonics with vocal formants
            all_frequencies = harmonics + vocal_formants

            sine_waves = []

            # Base frequency
            sine_waves.append(f"sine=frequency={base_freq}:duration={duration}")

            # Add all frequencies with varying volumes
            for i, freq in enumerate(all_frequencies):
                vol_factor = 0.6 ** (i + 1)
                sine_waves.append(f"sine=frequency={freq}:duration={duration}")

            # Create filter complex for mixing
            inputs = []
            mix_inputs = []

            for i, wave in enumerate(sine_waves):
                inputs.extend(["-f", "lavfi", "-i", wave])
                vol = volume if i == 0 else volume * (0.5 ** i)
                mix_inputs.append(f"[{i}]volume={vol}[a{i}]")

            # Mix all waves
            mix_filter = ";".join(mix_inputs)
            amix_inputs = "".join([f"[a{i}]" for i in range(len(sine_waves))])
            mix_filter += f";{amix_inputs}amix=inputs={len(sine_waves)}:duration=longest[out]"

            # FFmpeg command
            cmd = ["ffmpeg", "-y"] + inputs + [
                "-filter_complex", mix_filter,
                "-map", "[out]",
                "-t", str(duration),
                "-c:a", "mp3",
                "-b:a", "128k",
                str(output_path)
            ]

            logger.info(f"Generating vocal music with {len(sine_waves)} layers")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"Generated vocal music: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"FFmpeg vocal music generation failed: {result.stderr}")
                return None

        except Exception as e:
            logger.error(f"Error generating vocal music: {e}")
            return None
    
    def add_music_to_video(self, video_path: str, music_style: str = "forest") -> Optional[str]:
        """Add soft nature music to video."""
        try:
            if not os.path.exists(video_path):
                logger.error(f"Video file not found: {video_path}")
                return None
            
            # Get video duration
            duration = self._get_video_duration(video_path)
            if not duration:
                duration = 180  # Default
            
            # Generate music
            music_path = self.generate_nature_music(music_style, duration)
            if not music_path:
                logger.error("Failed to generate music")
                return video_path  # Return original video
            
            # Create output path
            video_name = Path(video_path).stem
            output_path = Path(video_path).parent / f"{video_name}_with_music.mp4"
            
            # Mix video with music
            cmd = [
                "ffmpeg", "-y",
                "-i", video_path,
                "-i", music_path,
                "-filter_complex",
                "[0:a][1:a]amix=inputs=2:duration=shortest:dropout_transition=2[audio]",
                "-map", "0:v", "-map", "[audio]",
                "-c:v", "copy",
                "-c:a", "aac",
                "-b:a", "192k",
                str(output_path)
            ]
            
            logger.info(f"Adding {music_style} music to video")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"Video with music created: {output_path} ({file_size} bytes)")
                
                # Replace original video
                os.replace(output_path, video_path)
                
                # Cleanup music file
                if os.path.exists(music_path):
                    os.remove(music_path)
                
                return video_path
            else:
                logger.error(f"Failed to add music: {result.stderr}")
                return video_path
                
        except Exception as e:
            logger.error(f"Error adding music to video: {e}")
            return video_path
    
    def _get_video_duration(self, video_path: str) -> Optional[float]:
        """Get video duration in seconds."""
        try:
            cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        
        return None
    
    def get_music_style_for_keyword(self, keyword: str) -> str:
        """Get appropriate music style for keyword."""
        keyword_lower = keyword.lower()
        
        # Map keywords to music styles
        if any(word in keyword_lower for word in ['forest', 'tree', 'jungle', 'wood']):
            return "forest"
        elif any(word in keyword_lower for word in ['ocean', 'sea', 'wave', 'water', 'river']):
            return "ocean"
        elif any(word in keyword_lower for word in ['mountain', 'peak', 'summit', 'alpine']):
            return "mountain"
        elif any(word in keyword_lower for word in ['meadow', 'field', 'grass', 'prairie']):
            return "meadow"
        elif any(word in keyword_lower for word in ['zen', 'meditation', 'peace', 'calm', 'relax']):
            return "zen"
        elif any(word in keyword_lower for word in ['nature', 'natural', 'environment']):
            # For general nature, choose randomly among peaceful styles
            return random.choice(["forest", "meadow", "zen"])
        else:
            # Default for any other topic
            return "zen"  # Most universally peaceful

# Example usage and testing
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    manager = MusicManager()
    
    print("🎵 Music Manager for Nature Videos")
    print("=" * 40)
    
    # Test music generation
    test_styles = ["nature", "ambient", "classical", "electronic", "cinematic"]

    for style in test_styles:
        print(f"\n🎼 Testing {style} music...")

        # Test instrumental
        print(f"   🎵 Instrumental version...")
        music_path = manager.generate_music("instrumental", style, 10, False)
        if music_path:
            print(f"   ✅ Generated instrumental: {music_path}")
            if os.path.exists(music_path):
                os.remove(music_path)
        else:
            print(f"   ❌ Failed to generate instrumental")

        # Test vocal (for some styles)
        if style in ["nature", "ambient"]:
            print(f"   🎤 Vocal version...")
            music_path = manager.generate_music("vocal", style, 10, True)
            if music_path:
                print(f"   ✅ Generated vocal: {music_path}")
                if os.path.exists(music_path):
                    os.remove(music_path)
            else:
                print(f"   ❌ Failed to generate vocal")
    
    print(f"\n🎯 Music style for 'nature': {manager.get_music_style_for_keyword('nature')}")
    print(f"🎯 Music style for 'forest meditation': {manager.get_music_style_for_keyword('forest meditation')}")
    print(f"🎯 Music style for 'ocean waves': {manager.get_music_style_for_keyword('ocean waves')}")
