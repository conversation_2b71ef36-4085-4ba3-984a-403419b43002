#!/usr/bin/env python3
"""
create_news_video_piper.py - Créer une vidéo d'actualité avec Piper TTS obligatoire
"""

import logging
import os
import subprocess
from pathlib import Path
import tempfile

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def fix_piper_environment():
    """Fix Piper environment variables and permissions."""
    print("🔧 CONFIGURATION ENVIRONNEMENT PIPER")
    print("-" * 40)
    
    # Set environment variables
    os.environ['DYLD_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    os.environ['DYLD_FALLBACK_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
    os.environ['ESPEAK_DATA_PATH'] = "./piper/espeak-ng-data"
    
    # Fix permissions
    piper_exe = Path("./piper/piper")
    if piper_exe.exists():
        piper_exe.chmod(0o755)
        print("✅ Permissions Piper corrigées")
    
    # Remove quarantine
    try:
        subprocess.run(["xattr", "-d", "com.apple.quarantine", str(piper_exe)], 
                      capture_output=True)
        print("✅ Quarantaine supprimée")
    except:
        print("⚠️ Quarantaine déjà supprimée")

def test_piper_direct():
    """Test Piper directly with a simple command."""
    print("\n🧪 TEST DIRECT PIPER")
    print("-" * 25)
    
    piper_exe = "./piper/piper"
    model_path = Path.home() / ".local" / "share" / "piper" / "models" / "fr_FR-gilles-low.onnx"
    
    if not Path(piper_exe).exists():
        print("❌ Exécutable Piper non trouvé")
        return False
    
    if not model_path.exists():
        print("❌ Modèle Piper non trouvé")
        return False
    
    # Test with very short text
    test_text = "Test."
    output_file = "test_piper_direct.wav"
    
    try:
        print("🔄 Test avec texte court...")
        
        # Use echo and pipe for input
        cmd = f"echo '{test_text}' | {piper_exe} --model {model_path} --output_file {output_file}"
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"✅ Test réussi! Fichier: {file_size} bytes")
            Path(output_file).unlink(missing_ok=True)
            return True
        else:
            print(f"❌ Test échoué: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout même avec texte court")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_piper_audio_direct(text: str, output_path: str) -> bool:
    """Create audio using Piper directly with shell commands."""
    print(f"\n🎤 GÉNÉRATION AUDIO PIPER DIRECT")
    print("-" * 40)
    
    piper_exe = "./piper/piper"
    model_path = Path.home() / ".local" / "share" / "piper" / "models" / "fr_FR-gilles-low.onnx"
    
    if not Path(piper_exe).exists() or not model_path.exists():
        print("❌ Piper ou modèle non disponible")
        return False
    
    # Split text into smaller chunks to avoid timeout
    sentences = text.split('. ')
    audio_chunks = []
    
    print(f"📝 Texte divisé en {len(sentences)} phrases")
    
    for i, sentence in enumerate(sentences):
        if not sentence.strip():
            continue
            
        chunk_file = f"chunk_{i}.wav"
        clean_sentence = sentence.strip() + "."
        
        print(f"🔄 Phrase {i+1}/{len(sentences)}: {clean_sentence[:50]}...")
        
        try:
            # Use echo and pipe for each chunk
            cmd = f"echo '{clean_sentence}' | {piper_exe} --model {model_path} --output_file {chunk_file}"
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and Path(chunk_file).exists():
                audio_chunks.append(chunk_file)
                print(f"   ✅ Chunk {i+1} créé")
            else:
                print(f"   ❌ Chunk {i+1} échoué: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Timeout chunk {i+1}")
        except Exception as e:
            print(f"   ❌ Erreur chunk {i+1}: {e}")
    
    if not audio_chunks:
        print("❌ Aucun chunk audio créé")
        return False
    
    print(f"✅ {len(audio_chunks)} chunks créés, assemblage...")
    
    # Combine audio chunks using FFmpeg
    try:
        # Create file list for FFmpeg
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            for chunk in audio_chunks:
                f.write(f"file '{chunk}'\n")
            filelist = f.name
        
        # Combine with FFmpeg
        cmd = [
            "ffmpeg", "-y", "-f", "concat", "-safe", "0", 
            "-i", filelist, "-c", "copy", output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        # Cleanup
        Path(filelist).unlink(missing_ok=True)
        for chunk in audio_chunks:
            Path(chunk).unlink(missing_ok=True)
        
        if result.returncode == 0 and Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            print(f"✅ Audio final créé: {output_path} ({file_size} bytes)")
            return True
        else:
            print(f"❌ Assemblage échoué: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur assemblage: {e}")
        return False

def create_news_video_with_piper():
    """Create a news video with Piper TTS."""
    print("📺" * 35)
    print("🎬 VIDÉO ACTUALITÉ AVEC PIPER TTS OBLIGATOIRE 🎬")
    print("📺" * 35)
    print()
    
    # Fix environment first
    fix_piper_environment()
    
    # Test Piper
    if not test_piper_direct():
        print("\n❌ Piper ne fonctionne pas - impossible de continuer")
        return False
    
    print("\n✅ Piper fonctionne! Création de la vidéo d'actualité...")
    
    # Create news script about current AI developments
    news_script = {
        "title": "Intelligence Artificielle : Les Dernières Avancées de 2024",
        "introduction": "Bonjour et bienvenue dans cette édition spéciale consacrée aux dernières avancées en intelligence artificielle. L'année 2024 marque un tournant décisif dans le développement de l'IA.",
        "key_point_1": "Les modèles de langage de nouvelle génération révolutionnent notre façon de communiquer avec les machines. Ces systèmes comprennent désormais le contexte avec une précision remarquable.",
        "key_point_2": "L'intelligence artificielle s'impose dans le domaine médical avec des diagnostics plus rapides et plus précis. Les médecins disposent maintenant d'outils d'aide à la décision révolutionnaires.",
        "key_point_3": "Les voitures autonomes franchissent une nouvelle étape avec des systèmes de conduite entièrement automatisés testés dans plusieurs villes du monde.",
        "conclusion": "Ces avancées promettent de transformer notre société dans les années à venir. Merci de nous avoir suivis pour cette actualité technologique générée avec Piper TTS."
    }
    
    # Combine script into full text
    full_text = ""
    for key, value in news_script.items():
        full_text += value + " "
    
    print(f"📝 Script d'actualité créé ({len(full_text)} caractères)")
    
    # Create audio with Piper
    audio_path = "output/audio/news_piper.wav"
    Path("output/audio").mkdir(parents=True, exist_ok=True)
    
    print("\n🎤 Génération audio avec Piper TTS...")
    success = create_piper_audio_direct(full_text, audio_path)
    
    if not success:
        print("❌ Échec génération audio Piper")
        return False
    
    # Create video with news-related nature videos
    try:
        from simple_nature_composer import SimpleNatureComposer
        
        composer = SimpleNatureComposer()
        
        print("\n🎬 Création vidéo avec arrière-plan adapté à l'actualité...")
        
        # Use "technology" keyword to get tech-related nature videos (wind turbines, etc.)
        video_path = composer.create_nature_video(audio_path, "technology_news", add_music=False)
        
        if video_path and Path(video_path).exists():
            video_size = Path(video_path).stat().st_size / (1024 * 1024)
            
            print("\n🎉 VIDÉO ACTUALITÉ AVEC PIPER CRÉÉE!")
            print("=" * 50)
            print(f"📁 Fichier: {video_path}")
            print(f"📊 Taille: {video_size:.1f} MB")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
            print()
            print("🎤 CARACTÉRISTIQUES:")
            print("   ✅ Narration: Piper TTS (local, haute qualité)")
            print("   ✅ Sujet: Actualité IA et technologie")
            print("   ✅ Arrière-plan: Vidéos tech/nature Pixabay")
            print("   ✅ Qualité: HD 1920x1080")
            print("   ✅ Audio: 100% Piper TTS (pas de fallback)")
            
            return True
        else:
            print("❌ Échec création vidéo")
            return False
            
    except Exception as e:
        print(f"❌ Erreur création vidéo: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    success = create_news_video_with_piper()
    
    if success:
        print("\n🎉 MISSION ACCOMPLIE!")
        print("🎤 Vidéo d'actualité créée avec Piper TTS")
        print("📺 Sujet: Avancées en Intelligence Artificielle")
        print("🌿 Arrière-plan: Vidéos technologie/nature Pixabay")
    else:
        print("\n❌ Échec de création")
        print("🔧 Vérifiez la configuration Piper")

if __name__ == "__main__":
    main()
