#!/usr/bin/env python3
"""
pixabay_videos.py - Intégration de vidéos de nature depuis Pixabay
"""

import os
import logging
import requests
import random
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import json

from config import get_config

logger = logging.getLogger(__name__)

class PixabayVideoManager:
    """Gestionnaire de vidéos Pixabay pour enrichir les vidéos avec des séquences de nature."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Pixabay video manager."""
        self.config = get_config()
        
        # API credentials
        self.api_key = api_key or os.getenv('PIXABAY_API_KEY')
        
        # API endpoints
        self.base_url = "https://pixabay.com/api/videos/"
        
        # Nature-related keywords for video search
        self.nature_keywords = [
            # Paysages généraux
            "nature", "landscape", "forest", "mountain", "ocean", "river", "lake",
            "sunset", "sunrise", "clouds", "sky", "horizon", "valley", "meadow",
            
            # Éléments naturels
            "trees", "flowers", "grass", "leaves", "water", "waves", "rain",
            "snow", "wind", "fire", "earth", "stone", "rock", "sand",
            
            # Animaux dans la nature
            "birds flying", "deer", "butterfly", "fish swimming", "eagle",
            "wildlife", "animals nature", "birds nature",
            
            # Saisons et météo
            "spring nature", "summer nature", "autumn nature", "winter nature",
            "storm", "lightning", "rainbow", "fog", "mist",
            
            # Environnements spécifiques
            "jungle", "desert", "beach", "waterfall", "canyon", "glacier",
            "volcano", "cave", "field", "garden", "park"
        ]
        
        # Video categories for different content types
        self.keyword_to_nature = {
            # Technologie -> Nature moderne/futuriste
            "technology": ["wind turbines", "solar panels nature", "green technology", "sustainable energy"],
            "artificial intelligence": ["digital nature", "futuristic landscape", "tech nature", "modern forest"],
            "innovation": ["renewable energy", "green innovation", "sustainable nature"],
            
            # Environnement -> Nature environnementale
            "climate": ["climate change", "melting glacier", "drought", "flood", "storm"],
            "environment": ["pollution", "clean nature", "pristine forest", "clear water"],
            "sustainability": ["renewable energy", "wind farm", "solar farm", "green energy"],
            
            # Santé/Bien-être -> Nature apaisante
            "health": ["peaceful nature", "calm water", "meditation nature", "zen garden"],
            "wellness": ["relaxing nature", "spa nature", "therapeutic landscape"],
            "meditation": ["peaceful lake", "calm forest", "serene nature", "tranquil"],
            
            # Business -> Nature professionnelle
            "business": ["corporate nature", "professional landscape", "success nature"],
            "finance": ["growth nature", "investment landscape", "prosperity nature"],
            
            # Éducation -> Nature éducative
            "education": ["learning nature", "discovery nature", "exploration"],
            "science": ["scientific nature", "research nature", "laboratory nature"],
            
            # Défaut -> Nature générale
            "default": ["beautiful nature", "stunning landscape", "amazing nature", "breathtaking"]
        }
        
        # Check availability
        self.available = self._check_availability()
        
        if self.available:
            logger.info("Pixabay Video Manager initialized successfully")
        else:
            logger.warning("Pixabay API key not provided - video enhancement disabled")
    
    def _check_availability(self) -> bool:
        """Check if Pixabay API is available."""
        if not self.api_key:
            logger.warning("Pixabay API key not provided")
            return False
        
        try:
            # Test API connection with a simple request
            params = {
                'key': self.api_key,
                'q': 'nature',
                'per_page': 3,
                'safesearch': 'true',
                'category': 'nature'
            }
            
            response = requests.get(self.base_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'hits' in data:
                    logger.info(f"Pixabay API working - {data.get('totalHits', 0)} videos available")
                    return True
            
            logger.error(f"Pixabay API test failed: {response.status_code}")
            return False
            
        except Exception as e:
            logger.error(f"Error checking Pixabay availability: {e}")
            return False
    
    def _get_nature_keywords_for_topic(self, keyword: str) -> List[str]:
        """Get appropriate nature keywords based on video topic."""
        keyword_lower = keyword.lower()
        
        # Find matching category
        for category, nature_terms in self.keyword_to_nature.items():
            if category in keyword_lower:
                return nature_terms
        
        # Check for specific words
        if any(word in keyword_lower for word in ['tech', 'ai', 'digital', 'computer']):
            return self.keyword_to_nature["technology"]
        elif any(word in keyword_lower for word in ['climate', 'environment', 'green']):
            return self.keyword_to_nature["environment"]
        elif any(word in keyword_lower for word in ['health', 'wellness', 'relax']):
            return self.keyword_to_nature["health"]
        elif any(word in keyword_lower for word in ['business', 'finance', 'money']):
            return self.keyword_to_nature["business"]
        elif any(word in keyword_lower for word in ['education', 'learn', 'science']):
            return self.keyword_to_nature["education"]
        else:
            return self.keyword_to_nature["default"]
    
    def search_nature_videos(self, keyword: str, count: int = 5) -> List[Dict]:
        """Search for nature videos related to the keyword."""
        if not self.available:
            logger.error("Pixabay not available")
            return []
        
        try:
            # Get appropriate nature keywords
            nature_keywords = self._get_nature_keywords_for_topic(keyword)
            
            # Add some general nature keywords
            search_terms = nature_keywords + random.sample(self.nature_keywords, 3)
            
            all_videos = []
            
            for search_term in search_terms[:3]:  # Limit to 3 searches to avoid rate limits
                params = {
                    'key': self.api_key,
                    'q': search_term,
                    'per_page': min(count * 2, 20),  # Get more to have options
                    'safesearch': 'true',
                    'category': 'nature',
                    'min_width': 1280,  # HD quality minimum
                    'min_height': 720,
                    'order': 'popular'
                }
                
                logger.info(f"Searching Pixabay for: '{search_term}'")
                
                response = requests.get(self.base_url, params=params, timeout=15)
                response.raise_for_status()
                
                data = response.json()
                videos = data.get('hits', [])
                
                logger.info(f"Found {len(videos)} videos for '{search_term}'")
                
                # Filter and process videos
                for video in videos:
                    if self._is_suitable_video(video):
                        all_videos.append({
                            'id': video['id'],
                            'url': video['videos']['large']['url'],
                            'preview_url': video['videos']['medium']['url'],
                            'thumbnail': video['userImageURL'],
                            'duration': video['duration'],
                            'tags': video['tags'],
                            'search_term': search_term,
                            'width': video['videos']['large']['width'],
                            'height': video['videos']['large']['height'],
                            'size': video['videos']['large']['size']
                        })
                
                # Small delay to respect rate limits
                time.sleep(0.5)
            
            # Remove duplicates and select best videos
            unique_videos = self._remove_duplicates(all_videos)
            selected_videos = self._select_best_videos(unique_videos, count)
            
            logger.info(f"Selected {len(selected_videos)} nature videos for '{keyword}'")
            return selected_videos
            
        except Exception as e:
            logger.error(f"Error searching Pixabay videos: {e}")
            return []
    
    def _is_suitable_video(self, video: Dict) -> bool:
        """Check if video is suitable for our needs."""
        try:
            # Check duration (between 5 and 60 seconds)
            duration = video.get('duration', 0)
            if duration < 5 or duration > 60:
                return False
            
            # Check video quality
            large_video = video.get('videos', {}).get('large', {})
            if not large_video:
                return False
            
            width = large_video.get('width', 0)
            height = large_video.get('height', 0)
            
            # Minimum HD quality
            if width < 1280 or height < 720:
                return False
            
            # Check file size (not too large)
            size = large_video.get('size', 0)
            if size > 50 * 1024 * 1024:  # 50MB max
                return False
            
            return True
            
        except Exception:
            return False
    
    def _remove_duplicates(self, videos: List[Dict]) -> List[Dict]:
        """Remove duplicate videos."""
        seen_ids = set()
        unique_videos = []
        
        for video in videos:
            video_id = video['id']
            if video_id not in seen_ids:
                seen_ids.add(video_id)
                unique_videos.append(video)
        
        return unique_videos
    
    def _select_best_videos(self, videos: List[Dict], count: int) -> List[Dict]:
        """Select the best videos based on quality and variety."""
        if len(videos) <= count:
            return videos
        
        # Sort by quality criteria
        def video_score(video):
            score = 0
            
            # Prefer HD+ quality
            if video['width'] >= 1920:
                score += 3
            elif video['width'] >= 1280:
                score += 2
            
            # Prefer moderate duration (10-30 seconds)
            duration = video['duration']
            if 10 <= duration <= 30:
                score += 2
            elif 5 <= duration <= 45:
                score += 1
            
            # Prefer smaller file sizes (faster download)
            size_mb = video['size'] / (1024 * 1024)
            if size_mb < 10:
                score += 2
            elif size_mb < 20:
                score += 1
            
            return score
        
        # Sort by score and select top videos
        videos.sort(key=video_score, reverse=True)
        return videos[:count]
    
    def download_video(self, video_info: Dict, keyword: str) -> Optional[str]:
        """Download a video from Pixabay."""
        try:
            video_url = video_info['url']
            video_id = video_info['id']
            
            # Create filename
            safe_keyword = keyword.replace(' ', '_').replace('/', '_')
            filename = f"nature_{safe_keyword}_{video_id}.mp4"
            output_path = self.config["directories"]["temp"] / filename
            
            logger.info(f"Downloading nature video: {filename}")
            
            # Download video
            response = requests.get(video_url, stream=True, timeout=60)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            # Verify download
            if output_path.exists() and output_path.stat().st_size > 1000:
                logger.info(f"Downloaded nature video: {output_path} ({output_path.stat().st_size} bytes)")
                return str(output_path)
            else:
                logger.error("Downloaded video is too small or corrupted")
                return None
                
        except Exception as e:
            logger.error(f"Error downloading video: {e}")
            return None
    
    def get_nature_videos_for_keyword(self, keyword: str, count: int = 3) -> List[str]:
        """Get and download nature videos for a keyword."""
        if not self.available:
            logger.warning("Pixabay not available - no nature videos will be added")
            return []
        
        try:
            # Search for videos
            videos = self.search_nature_videos(keyword, count)
            
            if not videos:
                logger.warning(f"No suitable nature videos found for '{keyword}'")
                return []
            
            # Download videos
            downloaded_paths = []
            
            for video in videos:
                video_path = self.download_video(video, keyword)
                if video_path:
                    downloaded_paths.append(video_path)
                
                # Limit downloads to avoid overwhelming
                if len(downloaded_paths) >= count:
                    break
            
            logger.info(f"Downloaded {len(downloaded_paths)} nature videos for '{keyword}'")
            return downloaded_paths
            
        except Exception as e:
            logger.error(f"Error getting nature videos: {e}")
            return []

# Example usage and testing
if __name__ == "__main__":
    # Test Pixabay Video Manager
    logging.basicConfig(level=logging.INFO)
    
    manager = PixabayVideoManager()
    
    if manager.available:
        print("✅ Pixabay Video Manager is available")
        
        # Test search
        test_keyword = "artificial intelligence"
        videos = manager.search_nature_videos(test_keyword, 3)
        
        if videos:
            print(f"Found {len(videos)} videos for '{test_keyword}':")
            for video in videos:
                print(f"  • {video['search_term']}: {video['duration']}s, {video['width']}x{video['height']}")
            
            # Test download
            if videos:
                downloaded = manager.download_video(videos[0], test_keyword)
                if downloaded:
                    print(f"✅ Test download successful: {downloaded}")
                else:
                    print("❌ Test download failed")
        else:
            print("No videos found")
    else:
        print("❌ Pixabay Video Manager not available")
        print("Set PIXABAY_API_KEY environment variable")
        print("Get your API key at: https://pixabay.com/api/docs/")
