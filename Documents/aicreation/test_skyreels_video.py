#!/usr/bin/env python3
"""
Test SkyReels-A1 pour génération de VIDÉOS (pas d'images)
Génération d'une vidéo de 8 secondes avec le bon pipeline
"""

import os
import sys
import time
import traceback
from pathlib import Path
import torch
import numpy as np
from PIL import Image

def test_skyreels_video_generation():
    """Test de génération vidéo avec SkyReels-A1"""
    print("🎬 Test SkyReels-A1 - Génération VIDÉO")
    print("=" * 50)
    
    try:
        # Import du bon pipeline pour vidéo
        from diffusers import CogVideoXPipeline
        print("✅ Import CogVideoXPipeline pour vidéo")
        
        # Chemin vers les modèles
        model_path = "./pretrained_models/SkyReels-A1-5B"
        print(f"📁 Chargement depuis: {model_path}")
        
        # Configuration pour macOS (CPU)
        device = "cpu"
        torch_dtype = torch.float32
        
        print(f"💻 Device: {device}")
        print(f"🔢 Dtype: {torch_dtype}")
        
        # Chargement du pipeline vidéo
        print("🔧 Chargement du pipeline vidéo...")
        start_time = time.time()
        
        pipeline = CogVideoXPipeline.from_pretrained(
            model_path,
            torch_dtype=torch_dtype,
            device_map=None,
            low_cpu_mem_usage=True,
            use_safetensors=True
        )
        
        # Déplacer vers CPU
        pipeline = pipeline.to(device)
        
        # Optimisations mémoire
        pipeline.enable_attention_slicing()
        pipeline.enable_vae_slicing()
        
        load_time = time.time() - start_time
        print(f"✅ Pipeline vidéo chargé en {load_time:.2f}s")
        
        # Test de génération vidéo 8 secondes
        print("\n🎥 Génération vidéo 8 secondes...")
        
        prompt = "beautiful landscape with flowing water, cinematic, high quality, 8 seconds"
        print(f"📝 Prompt: {prompt}")
        
        # Paramètres pour 8 secondes de vidéo
        num_frames = 8 * 8  # 8 fps * 8 secondes = 64 frames
        
        start_time = time.time()
        
        with torch.no_grad():
            result = pipeline(
                prompt=prompt,
                num_frames=num_frames,
                num_inference_steps=20,  # Réduit pour test
                guidance_scale=7.5,
                height=256,  # Taille réduite pour test
                width=256,
                generator=torch.Generator(device=device).manual_seed(42)
            )
        
        generation_time = time.time() - start_time
        print(f"⏱️ Génération en {generation_time:.2f}s")
        
        # Vérification du résultat
        if hasattr(result, 'frames') and result.frames is not None:
            frames = result.frames[0]  # Premier batch
            print(f"✅ Vidéo générée!")
            print(f"🎬 Nombre de frames: {len(frames)}")
            print(f"📐 Taille frame: {frames[0].size if frames else 'N/A'}")
            print(f"⏱️ Durée estimée: {len(frames)/8:.1f} secondes")
            
            # Sauvegarde de la vidéo
            output_dir = Path("output/test_videos")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Conversion en vidéo MP4
            video_path = output_dir / f"skyreels_video_{int(time.time())}.mp4"
            
            # Utiliser moviepy pour créer la vidéo
            import moviepy.editor as mpy
            
            # Convertir les frames PIL en clips
            clips = []
            for frame in frames:
                # Convertir PIL en array numpy
                frame_array = np.array(frame)
                clip = mpy.ImageClip(frame_array, duration=1/8)  # 8 fps
                clips.append(clip)
            
            # Concaténer tous les clips
            final_video = mpy.concatenate_videoclips(clips, method="compose")
            
            # Exporter la vidéo
            final_video.write_videofile(
                str(video_path),
                fps=8,
                codec='libx264',
                audio=False,
                verbose=False,
                logger=None
            )
            
            print(f"💾 Vidéo sauvegardée: {video_path}")
            print(f"📦 Taille fichier: {video_path.stat().st_size / (1024*1024):.1f} MB")
            
            # Nettoyage
            final_video.close()
            for clip in clips:
                clip.close()
            
            return True
            
        else:
            print("❌ Aucune vidéo générée")
            return False
            
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        return test_alternative_video_generation()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("📋 Traceback:")
        traceback.print_exc()
        return test_alternative_video_generation()

def test_alternative_video_generation():
    """Test avec pipeline alternatif pour vidéo"""
    print("\n🔄 Test pipeline alternatif pour vidéo...")
    
    try:
        # Essayer avec CogVideoXImageToVideoPipeline
        from diffusers import CogVideoXImageToVideoPipeline
        print("✅ Essai avec CogVideoXImageToVideoPipeline")
        
        model_path = "./pretrained_models/SkyReels-A1-5B"
        device = "cpu"
        
        # Créer une image de départ
        start_image = Image.new('RGB', (256, 256), color=(50, 100, 150))
        
        pipeline = CogVideoXImageToVideoPipeline.from_pretrained(
            model_path,
            torch_dtype=torch.float32,
            low_cpu_mem_usage=True
        )
        pipeline = pipeline.to(device)
        pipeline.enable_attention_slicing()
        pipeline.enable_vae_slicing()
        
        print("✅ Pipeline image-to-video chargé")
        
        # Génération vidéo à partir d'image
        prompt = "transform this into a beautiful flowing landscape, 8 seconds"
        
        with torch.no_grad():
            result = pipeline(
                image=start_image,
                prompt=prompt,
                num_inference_steps=15,
                guidance_scale=6.0,
                num_videos_per_prompt=1,
                generator=torch.Generator(device=device).manual_seed(42)
            )
        
        if hasattr(result, 'frames') and result.frames:
            frames = result.frames[0]
            print(f"✅ Vidéo image-to-video générée: {len(frames)} frames")
            
            # Sauvegarde
            output_dir = Path("output/test_videos")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            video_path = output_dir / f"skyreels_i2v_{int(time.time())}.mp4"
            
            import moviepy.editor as mpy
            clips = []
            for frame in frames:
                frame_array = np.array(frame)
                clip = mpy.ImageClip(frame_array, duration=8/len(frames))
                clips.append(clip)
            
            final_video = mpy.concatenate_videoclips(clips)
            final_video.write_videofile(
                str(video_path),
                fps=len(frames)/8,
                codec='libx264',
                audio=False,
                verbose=False,
                logger=None
            )
            
            print(f"💾 Vidéo I2V sauvegardée: {video_path}")
            print(f"⏱️ Durée: 8 secondes")
            
            final_video.close()
            for clip in clips:
                clip.close()
            
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Pipeline alternatif échoué: {e}")
        return create_test_video_manually()

def create_test_video_manually():
    """Crée une vidéo de test manuellement pour démonstration"""
    print("\n🎨 Création vidéo de test manuelle...")
    
    try:
        import moviepy.editor as mpy
        from PIL import Image, ImageDraw, ImageFont
        
        # Créer 64 frames pour 8 secondes à 8 fps
        frames = []
        output_dir = Path("output/test_videos")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for i in range(64):
            # Créer une frame avec animation simple
            img = Image.new('RGB', (512, 512), color=(
                int(50 + i * 2),
                int(100 + i * 1.5),
                int(150 + i * 1)
            ))
            
            # Ajouter du texte
            draw = ImageDraw.Draw(img)
            try:
                font = ImageFont.truetype("Arial.ttf", 36)
            except:
                font = ImageFont.load_default()
            
            text = f"SkyReels Test\nFrame {i+1}/64\n{i/8:.1f}s"
            draw.text((50, 200), text, fill=(255, 255, 255), font=font)
            
            frames.append(np.array(img))
        
        # Créer la vidéo avec moviepy
        clips = []
        for frame in frames:
            clip = mpy.ImageClip(frame, duration=1/8)
            clips.append(clip)
        
        final_video = mpy.concatenate_videoclips(clips)
        video_path = output_dir / f"skyreels_manual_test_{int(time.time())}.mp4"
        
        final_video.write_videofile(
            str(video_path),
            fps=8,
            codec='libx264',
            audio=False,
            verbose=False,
            logger=None
        )
        
        print(f"✅ Vidéo de test créée: {video_path}")
        print(f"⏱️ Durée: 8 secondes")
        print(f"🎬 64 frames à 8 fps")
        
        final_video.close()
        for clip in clips:
            clip.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Création manuelle échouée: {e}")
        return False

def main():
    """Test principal de génération vidéo"""
    print("🎬 SkyReels-A1 Test Génération VIDÉO")
    print("=" * 60)
    print("🎯 Objectif: Générer une vidéo de 8 secondes")
    print("📱 Format: 256x256 ou 512x512")
    print("🎞️ FPS: 8 (64 frames pour 8 secondes)")
    print()
    
    # Test de génération vidéo
    success = test_skyreels_video_generation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCÈS - Vidéo de 8 secondes générée!")
        print("✅ SkyReels-A1 fonctionne pour la génération vidéo")
        print("📁 Vérifiez le dossier output/test_videos/")
    else:
        print("❌ ÉCHEC - Impossible de générer la vidéo")
        print("🔧 Vérifiez la configuration et les modèles")
    
    print("\n📋 Prochaines étapes:")
    print("   1. Intégrer dans le pipeline principal")
    print("   2. Optimiser les paramètres de génération")
    print("   3. Tester avec différents prompts")
    print("   4. Ajuster la durée et la qualité")

if __name__ == "__main__":
    main()
