"""
Advanced logging system with structured logging and metrics.

This module provides a comprehensive logging system that supports:
- Structured logging with JSON output
- Performance metrics
- Error tracking
- Log aggregation
- Context management
"""

import logging
import json
import time
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from contextlib import contextmanager
from functools import wraps

from .interfaces import ILogger, IMetrics
from .config import get_config


class StructuredLogger(ILogger):
    """Structured logger implementation."""
    
    def __init__(self, name: str, level: str = "INFO"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        self._setup_handlers()
        self._context: Dict[str, Any] = {}
    
    def _setup_handlers(self):
        """Setup logging handlers."""
        if self.logger.handlers:
            return  # Already configured
        
        config = get_config()
        
        # Console handler with structured format
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = StructuredFormatter()
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler for all logs
        log_dir = config.output_dir / "logs"
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_dir / f"{self.name}.log")
        file_handler.setLevel(logging.DEBUG)
        file_formatter = StructuredFormatter(include_traceback=True)
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # Error file handler
        error_handler = logging.FileHandler(log_dir / f"{self.name}_errors.log")
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        self.logger.addHandler(error_handler)
    
    def _log(self, level: int, message: str, **kwargs):
        """Internal logging method."""
        extra = {
            "timestamp": datetime.utcnow().isoformat(),
            "logger_name": self.name,
            "context": self._context.copy(),
            **kwargs
        }
        self.logger.log(level, message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self._log(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self._log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        if 'exception' not in kwargs:
            kwargs['traceback'] = traceback.format_exc()
        self._log(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        if 'exception' not in kwargs:
            kwargs['traceback'] = traceback.format_exc()
        self._log(logging.CRITICAL, message, **kwargs)
    
    def add_context(self, **kwargs):
        """Add context to all future log messages."""
        self._context.update(kwargs)
    
    def remove_context(self, *keys):
        """Remove context keys."""
        for key in keys:
            self._context.pop(key, None)
    
    def clear_context(self):
        """Clear all context."""
        self._context.clear()
    
    @contextmanager
    def context(self, **kwargs):
        """Temporary context manager."""
        old_context = self._context.copy()
        self._context.update(kwargs)
        try:
            yield
        finally:
            self._context = old_context


class StructuredFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def __init__(self, include_traceback: bool = False):
        super().__init__()
        self.include_traceback = include_traceback
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add extra fields
        if hasattr(record, 'context'):
            log_data["context"] = record.context
        
        # Add any additional fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                          'pathname', 'filename', 'module', 'lineno', 
                          'funcName', 'created', 'msecs', 'relativeCreated',
                          'thread', 'threadName', 'processName', 'process',
                          'getMessage', 'exc_info', 'exc_text', 'stack_info',
                          'context']:
                log_data[key] = value
        
        # Add traceback if available and requested
        if self.include_traceback and record.exc_info:
            log_data["traceback"] = self.formatException(record.exc_info)
        
        return json.dumps(log_data, default=str, ensure_ascii=False)


class MetricsCollector(IMetrics):
    """Metrics collector implementation."""
    
    def __init__(self):
        self.counters: Dict[str, int] = {}
        self.histograms: Dict[str, List[float]] = {}
        self.gauges: Dict[str, float] = {}
        self.logger = StructuredLogger("metrics")
    
    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None):
        """Increment a counter metric."""
        key = self._make_key(name, tags)
        self.counters[key] = self.counters.get(key, 0) + 1
        self.logger.debug(f"Counter incremented: {name}", 
                         metric_name=name, metric_type="counter", 
                         metric_value=self.counters[key], tags=tags)
    
    def record_histogram(
        self, 
        name: str, 
        value: float, 
        tags: Optional[Dict[str, str]] = None
    ):
        """Record a histogram metric."""
        key = self._make_key(name, tags)
        if key not in self.histograms:
            self.histograms[key] = []
        self.histograms[key].append(value)
        self.logger.debug(f"Histogram recorded: {name}", 
                         metric_name=name, metric_type="histogram", 
                         metric_value=value, tags=tags)
    
    def record_gauge(
        self, 
        name: str, 
        value: float, 
        tags: Optional[Dict[str, str]] = None
    ):
        """Record a gauge metric."""
        key = self._make_key(name, tags)
        self.gauges[key] = value
        self.logger.debug(f"Gauge recorded: {name}", 
                         metric_name=name, metric_type="gauge", 
                         metric_value=value, tags=tags)
    
    def _make_key(self, name: str, tags: Optional[Dict[str, str]]) -> str:
        """Create a unique key for the metric."""
        if not tags:
            return name
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of all metrics."""
        summary = {
            "counters": self.counters.copy(),
            "gauges": self.gauges.copy(),
            "histograms": {}
        }
        
        # Calculate histogram statistics
        for key, values in self.histograms.items():
            if values:
                summary["histograms"][key] = {
                    "count": len(values),
                    "min": min(values),
                    "max": max(values),
                    "avg": sum(values) / len(values),
                    "sum": sum(values)
                }
        
        return summary
    
    def reset_metrics(self):
        """Reset all metrics."""
        self.counters.clear()
        self.histograms.clear()
        self.gauges.clear()
        self.logger.info("Metrics reset")


class PerformanceTracker:
    """Performance tracking utilities."""
    
    def __init__(self, metrics: IMetrics, logger: ILogger):
        self.metrics = metrics
        self.logger = logger
    
    @contextmanager
    def track_time(self, operation_name: str, tags: Optional[Dict[str, str]] = None):
        """Context manager to track operation time."""
        start_time = time.time()
        self.logger.debug(f"Starting operation: {operation_name}", 
                         operation=operation_name, tags=tags)
        
        try:
            yield
            duration = time.time() - start_time
            self.metrics.record_histogram(f"{operation_name}_duration", duration, tags)
            self.logger.info(f"Operation completed: {operation_name}", 
                           operation=operation_name, duration=duration, tags=tags)
        except Exception as e:
            duration = time.time() - start_time
            self.metrics.increment_counter(f"{operation_name}_errors", tags)
            self.logger.error(f"Operation failed: {operation_name}", 
                            operation=operation_name, duration=duration, 
                            error=str(e), tags=tags)
            raise
    
    def track_function(self, func_name: Optional[str] = None):
        """Decorator to track function performance."""
        def decorator(func):
            name = func_name or f"{func.__module__}.{func.__name__}"
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.track_time(name):
                    return func(*args, **kwargs)
            return wrapper
        return decorator


# Global instances
_logger_cache: Dict[str, StructuredLogger] = {}
_metrics: Optional[MetricsCollector] = None
_performance_tracker: Optional[PerformanceTracker] = None


def get_logger(name: str) -> StructuredLogger:
    """Get or create a structured logger."""
    if name not in _logger_cache:
        config = get_config()
        _logger_cache[name] = StructuredLogger(name, config.log_level)
    return _logger_cache[name]


def get_metrics() -> MetricsCollector:
    """Get global metrics collector."""
    global _metrics
    if _metrics is None:
        _metrics = MetricsCollector()
    return _metrics


def get_performance_tracker() -> PerformanceTracker:
    """Get global performance tracker."""
    global _performance_tracker
    if _performance_tracker is None:
        _performance_tracker = PerformanceTracker(get_metrics(), get_logger("performance"))
    return _performance_tracker


def setup_logging():
    """Setup global logging configuration."""
    config = get_config()
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, config.log_level.upper()),
        format='%(message)s'  # We use structured formatter
    )
    
    # Disable some noisy loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    
    logger = get_logger("system")
    logger.info("Logging system initialized", 
               log_level=config.log_level, 
               environment=config.environment)
