#!/usr/bin/env python3
"""
create_nature_video.py - <PERSON><PERSON>er une vidéo nature avec musique douce
"""

import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_nature_video_with_music():
    """Create a nature video with soft music."""
    print("🌿" * 30)
    print("🎬 CRÉATION VIDÉO NATURE AVEC MUSIQUE DOUCE 🎬")
    print("🌿" * 30)
    print()
    print("✨ Création d'une vidéo sur la nature avec :")
    print("   🌿 Vidéos nature HD depuis Pixabay")
    print("   🎵 Musique douce sans percussion")
    print("   🎯 Fréquences apaisantes (432Hz, 528Hz)")
    print("   📝 Script IA optimisé pour la nature")
    print()
    
    try:
        # Import modules
        from enhanced_auto_youtube import EnhancedAutoYouTube
        
        # Initialize system
        print("🔄 Initialisation du système...")
        auto_youtube = EnhancedAutoYouTube()
        
        # Check if Pixabay is available
        if hasattr(auto_youtube.video_manager, 'video_composer') and auto_youtube.video_manager.video_composer:
            if auto_youtube.video_manager.video_composer.pixabay_manager.available:
                print("✅ Pixabay configuré - vidéos nature disponibles")
            else:
                print("⚠️ Pixabay non configuré - utilise méthodes standard")
            
            if auto_youtube.video_manager.video_composer.music_manager:
                print("✅ Gestionnaire de musique disponible")
            else:
                print("⚠️ Gestionnaire de musique non disponible")
        
        print()
        print("🎬 Génération de la vidéo nature...")
        print("⏳ Cela peut prendre 2-4 minutes...")
        print()
        
        # Process the nature keyword
        keyword = "nature"
        success = auto_youtube.process_keyword(keyword)
        
        if success:
            print("✅ Vidéo nature créée avec succès!")
            print()
            
            # Show generated files
            video_path = Path("output/videos") / f"{keyword}.mp4"
            audio_path = Path("output/audio") / f"{keyword}.mp3"
            thumbnail_path = Path("output/thumbnails") / f"{keyword}.jpg"
            script_path = Path("output/scripts") / f"{keyword}.json"
            
            print("📁 FICHIERS GÉNÉRÉS:")
            
            if video_path.exists():
                file_size = video_path.stat().st_size / (1024 * 1024)
                print(f"🎬 Vidéo: {video_path} ({file_size:.1f} MB)")
                print(f"   🌐 Voir: file://{video_path.absolute()}")
            
            if audio_path.exists():
                file_size = audio_path.stat().st_size / (1024 * 1024)
                print(f"🎵 Audio: {audio_path} ({file_size:.1f} MB)")
            
            if thumbnail_path.exists():
                file_size = thumbnail_path.stat().st_size / 1024
                print(f"🖼️ Miniature: {thumbnail_path} ({file_size:.1f} KB)")
            
            if script_path.exists():
                print(f"📝 Script: {script_path}")
            
            print()
            print("🎉 VOTRE VIDÉO NATURE EST PRÊTE!")
            print("🌿 Elle contient des séquences nature HD et de la musique apaisante")
            
        else:
            print("❌ Erreur lors de la création de la vidéo")
            
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        print("💡 Assurez-vous que tous les modules sont installés")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("🔧 Vérifiez la configuration et les logs")

def test_music_generation():
    """Test music generation separately."""
    print("\n🎵 TEST DE GÉNÉRATION MUSICALE:")
    print("-" * 40)
    
    try:
        from music_manager import MusicManager
        
        manager = MusicManager()
        
        # Test different styles
        styles = ["forest", "ocean", "zen"]
        
        for style in styles:
            print(f"\n🎼 Test musique {style}...")
            music_path = manager.generate_nature_music(style, 30)  # 30 seconds
            
            if music_path and Path(music_path).exists():
                file_size = Path(music_path).stat().st_size / 1024
                print(f"   ✅ Généré: {music_path} ({file_size:.1f} KB)")
                
                # Cleanup
                Path(music_path).unlink(missing_ok=True)
            else:
                print(f"   ❌ Échec génération {style}")
        
        print(f"\n🎯 Style pour 'nature': {manager.get_music_style_for_keyword('nature')}")
        
    except ImportError:
        print("❌ Module music_manager non trouvé")
    except Exception as e:
        print(f"❌ Erreur test musique: {e}")

def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] == "test-music":
        test_music_generation()
    else:
        create_nature_video_with_music()

if __name__ == "__main__":
    main()
