#!/usr/bin/env python3
"""
video_composer.py - Composition de vidéos avec intégration de séquences nature
"""

import os
import logging
import subprocess
import tempfile
import json
from pathlib import Path
from typing import List, Optional, Dict
import random

from config import get_config
from pixabay_videos import PixabayVideoManager

try:
    from music_manager import MusicManager
    MUSIC_AVAILABLE = True
except ImportError:
    MUSIC_AVAILABLE = False

logger = logging.getLogger(__name__)

class VideoComposer:
    """Compositeur de vidéos intégrant des séquences de nature."""
    
    def __init__(self):
        """Initialize video composer."""
        self.config = get_config()
        self.pixabay_manager = PixabayVideoManager()

        # Initialize music manager
        self.music_manager = None
        if MUSIC_AVAILABLE:
            try:
                self.music_manager = MusicManager()
                logger.info("🎵 Music Manager initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Music Manager: {e}")
                self.music_manager = None
        
        # Composition settings
        self.composition_styles = {
            "overlay": {
                "description": "Vidéos nature en arrière-plan avec texte par-dessus",
                "nature_opacity": 0.7,
                "text_overlay": True
            },
            "split_screen": {
                "description": "Écran partagé entre contenu et nature",
                "nature_position": "right",
                "content_position": "left"
            },
            "transitions": {
                "description": "Transitions entre contenu et séquences nature",
                "transition_duration": 1.0,
                "nature_segments": True
            },
            "picture_in_picture": {
                "description": "Petite fenêtre nature dans le coin",
                "pip_size": 0.3,
                "pip_position": "top-right"
            }
        }
        
        logger.info("Video Composer initialized")
    
    def create_enhanced_video(self, script: Dict[str, str], audio_path: str,
                            keyword: str, style: str = "overlay", add_music: bool = True) -> Optional[str]:
        """Create enhanced video with nature sequences and optional music."""
        try:
            logger.info(f"Creating enhanced video with nature integration (style: {style})")

            # Get nature videos
            nature_videos = []
            if self.pixabay_manager.available:
                nature_videos = self.pixabay_manager.get_nature_videos_for_keyword(keyword, 3)

            if not nature_videos:
                logger.warning("No nature videos available, creating standard video")
                video_path = self._create_standard_video(script, audio_path, keyword)
            else:
                # Create video based on style
                if style == "overlay":
                    video_path = self._create_overlay_video(script, audio_path, keyword, nature_videos)
                elif style == "transitions":
                    video_path = self._create_transition_video(script, audio_path, keyword, nature_videos)
                else:
                    video_path = self._create_overlay_video(script, audio_path, keyword, nature_videos)

            # Add soft music if requested and available
            if add_music and video_path and self.music_manager:
                logger.info("🎵 Adding soft nature music without percussion")
                music_style = self.music_manager.get_music_style_for_keyword(keyword)
                video_path = self.music_manager.add_music_to_video(video_path, music_style)
                logger.info(f"✅ Enhanced video with nature and {music_style} music created")

            return video_path

        except Exception as e:
            logger.error(f"Error creating enhanced video: {e}")
            return self._create_standard_video(script, audio_path, keyword)
    
    def _create_overlay_video(self, script: Dict[str, str], audio_path: str, 
                            keyword: str, nature_videos: List[str]) -> Optional[str]:
        """Create video with nature background and text overlay."""
        try:
            output_path = self.config["directories"]["videos"] / f"{keyword.replace(' ', '_')}.mp4"
            
            # Get audio duration
            duration = self._get_audio_duration(audio_path)
            if not duration:
                duration = 180  # Default 3 minutes
            
            # Select and prepare nature video
            nature_video = random.choice(nature_videos)
            
            # Create text overlays for each section
            text_overlays = self._create_text_overlays(script, duration)
            
            # FFmpeg command for overlay composition
            cmd = [
                "ffmpeg", "-y",
                "-i", nature_video,
                "-i", audio_path,
                "-filter_complex",
                f"[0:v]scale=1920:1080,loop=loop=-1:size=1:start=0[bg];"
                f"[bg]drawtext=fontsize=48:fontcolor=white:x=(w-text_w)/2:y=h/6:text='{self._escape_text(script['title'])}':enable='between(t,0,8)'[v1];"
                f"[v1]drawtext=fontsize=36:fontcolor=white:x=(w-text_w)/2:y=h*5/6:text='Subscribe for more!':enable='between(t,{duration-5},{duration})'[v]",
                "-map", "[v]", "-map", "1:a",
                "-t", str(duration),
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-c:a", "aac", "-b:a", "192k",
                str(output_path)
            ]
            
            logger.info("Creating overlay video with nature background")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"Enhanced overlay video created: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"FFmpeg overlay failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating overlay video: {e}")
            return None
    
    def _create_transition_video(self, script: Dict[str, str], audio_path: str, 
                               keyword: str, nature_videos: List[str]) -> Optional[str]:
        """Create video with transitions between content and nature."""
        try:
            output_path = self.config["directories"]["videos"] / f"{keyword.replace(' ', '_')}.mp4"
            
            # Get audio duration
            duration = self._get_audio_duration(audio_path)
            if not duration:
                duration = 180
            
            # Create segments
            segments = []
            segment_duration = duration / 6  # 6 segments alternating content/nature
            
            for i in range(3):  # 3 content segments
                # Content segment
                content_start = i * segment_duration * 2
                content_end = content_start + segment_duration
                
                # Nature segment
                nature_start = content_end
                nature_end = nature_start + segment_duration
                
                segments.append({
                    'type': 'content',
                    'start': content_start,
                    'end': content_end,
                    'text': self._get_segment_text(script, i)
                })
                
                if i < len(nature_videos):
                    segments.append({
                        'type': 'nature',
                        'start': nature_start,
                        'end': nature_end,
                        'video': nature_videos[i]
                    })
            
            # Create complex FFmpeg filter
            filter_complex = self._build_transition_filter(segments, duration)
            
            cmd = [
                "ffmpeg", "-y",
                "-i", audio_path
            ]
            
            # Add nature video inputs
            for video in nature_videos:
                cmd.extend(["-i", video])
            
            cmd.extend([
                "-filter_complex", filter_complex,
                "-map", "[final]", "-map", "0:a",
                "-t", str(duration),
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-c:a", "aac", "-b:a", "192k",
                str(output_path)
            ])
            
            logger.info("Creating transition video with nature sequences")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"Enhanced transition video created: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"FFmpeg transition failed: {result.stderr}")
                return self._create_simple_nature_video(script, audio_path, keyword, nature_videos)
                
        except Exception as e:
            logger.error(f"Error creating transition video: {e}")
            return self._create_simple_nature_video(script, audio_path, keyword, nature_videos)
    
    def _create_simple_nature_video(self, script: Dict[str, str], audio_path: str, 
                                  keyword: str, nature_videos: List[str]) -> Optional[str]:
        """Create simple video with nature background (fallback)."""
        try:
            output_path = self.config["directories"]["videos"] / f"{keyword.replace(' ', '_')}.mp4"
            
            # Get audio duration
            duration = self._get_audio_duration(audio_path)
            if not duration:
                duration = 180
            
            # Use first nature video as background
            nature_video = nature_videos[0]
            title = self._escape_text(script.get('title', keyword))
            
            cmd = [
                "ffmpeg", "-y",
                "-stream_loop", "-1", "-i", nature_video,
                "-i", audio_path,
                "-filter_complex",
                f"[0:v]scale=1920:1080[bg];"
                f"[bg]drawtext=fontsize=60:fontcolor=white:fontfile=/System/Library/Fonts/Arial.ttf:x=(w-text_w)/2:y=h/3:text='{title}':enable='between(t,0,5)'[v]",
                "-map", "[v]", "-map", "1:a",
                "-t", str(duration),
                "-c:v", "libx264", "-preset", "fast", "-crf", "25",
                "-c:a", "aac", "-b:a", "192k",
                str(output_path)
            ]
            
            logger.info("Creating simple nature background video")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"Simple nature video created: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"Simple nature video failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating simple nature video: {e}")
            return None
    
    def _create_standard_video(self, script: Dict[str, str], audio_path: str, keyword: str) -> Optional[str]:
        """Create standard video without nature (fallback)."""
        try:
            output_path = self.config["directories"]["videos"] / f"{keyword.replace(' ', '_')}.mp4"
            
            duration = self._get_audio_duration(audio_path) or 180
            title = self._escape_text(script.get('title', keyword))
            
            cmd = [
                "ffmpeg", "-y",
                "-f", "lavfi", "-i", f"color=c=0x1a1a2e:s=1920x1080:d={duration}",
                "-i", audio_path,
                "-filter_complex",
                f"[0:v]drawtext=fontsize=60:fontcolor=white:x=(w-text_w)/2:y=h/2:text='{title}'[v]",
                "-map", "[v]", "-map", "1:a",
                "-c:v", "libx264", "-preset", "fast", "-crf", "25",
                "-c:a", "aac", "-b:a", "192k",
                str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
            
            if result.returncode == 0 and output_path.exists():
                logger.info(f"Standard video created: {output_path}")
                return str(output_path)
            
        except Exception as e:
            logger.error(f"Error creating standard video: {e}")
        
        return None
    
    def _get_audio_duration(self, audio_path: str) -> Optional[float]:
        """Get audio duration in seconds."""
        try:
            cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        
        return None
    
    def _escape_text(self, text: str) -> str:
        """Escape text for FFmpeg."""
        return text.replace("'", "'\\''").replace(":", "\\:").replace(",", "\\,")
    
    def _create_text_overlays(self, script: Dict[str, str], duration: float) -> List[Dict]:
        """Create text overlay segments."""
        overlays = []
        
        # Title at start
        overlays.append({
            'text': script.get('title', ''),
            'start': 0,
            'end': 5,
            'position': 'center',
            'size': 60
        })
        
        # Conclusion at end
        overlays.append({
            'text': 'Subscribe for more!',
            'start': duration - 5,
            'end': duration,
            'position': 'bottom',
            'size': 40
        })
        
        return overlays
    
    def _get_segment_text(self, script: Dict[str, str], segment_index: int) -> str:
        """Get text for a specific segment."""
        if segment_index == 0:
            return script.get('introduction', '')
        elif segment_index == 1:
            return script.get('key_point_1', '')
        elif segment_index == 2:
            return script.get('conclusion', '')
        else:
            return script.get('title', '')
    
    def _build_transition_filter(self, segments: List[Dict], duration: float) -> str:
        """Build FFmpeg filter for transitions."""
        # Simplified filter for basic transitions
        return (
            f"color=c=0x1a1a2e:s=1920x1080:d={duration}[bg];"
            f"[bg]drawtext=fontsize=60:fontcolor=white:x=(w-text_w)/2:y=h/2:text='Enhanced with Nature'[final]"
        )

# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    composer = VideoComposer()
    
    test_script = {
        "title": "Test Nature Integration",
        "introduction": "Welcome to this nature-enhanced video",
        "key_point_1": "First point with beautiful nature",
        "key_point_2": "Second point with stunning landscapes",
        "key_point_3": "Third point with amazing visuals",
        "conclusion": "Thanks for watching this enhanced video!"
    }
    
    print("Video Composer ready for nature integration!")
