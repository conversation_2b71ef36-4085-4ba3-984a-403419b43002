# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/logic_calculator.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1mediapipe/calculators/util/logic_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xef\x01\n\x16LogicCalculatorOptions\x12\x37\n\x02op\x18\x01 \x01(\x0e\x32+.mediapipe.LogicCalculatorOptions.Operation\x12\x0e\n\x06negate\x18\x02 \x01(\x08\x12\x13\n\x0binput_value\x18\x03 \x03(\x08\"%\n\tOperation\x12\x07\n\x03\x41ND\x10\x00\x12\x06\n\x02OR\x10\x01\x12\x07\n\x03XOR\x10\x02\x32P\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xee\xc1\xc2\xa1\x01 \x01(\x0b\x32!.mediapipe.LogicCalculatorOptionsBD\n%com.google.mediapipe.calculator.protoB\x1bLogicCalculatorOptionsProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.logic_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.google.mediapipe.calculator.protoB\033LogicCalculatorOptionsProto'
  _globals['_LOGICCALCULATOROPTIONS']._serialized_start=103
  _globals['_LOGICCALCULATOROPTIONS']._serialized_end=342
  _globals['_LOGICCALCULATOROPTIONS_OPERATION']._serialized_start=223
  _globals['_LOGICCALCULATOROPTIONS_OPERATION']._serialized_end=260
# @@protoc_insertion_point(module_scope)
