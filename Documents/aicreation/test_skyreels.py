"""
Script de test pour l'initialisation de SkyReels-A1
"""

import logging
from skyreels_manager import SkyReelsManager

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Fonction principale pour tester l'initialisation de SkyReels-A1"""
    try:
        # Initialiser SkyReels-A1 avec le gestionnaire de contexte
        with SkyReelsManager("test_config.yaml") as manager:
            # Test simple de génération
            prompt = "Une jeune femme souriante parlant à la caméra dans un studio moderne"
            result = manager.generate_image(prompt=prompt)
            
            if result is not None and result.shape[2] == 3:  # Vérifier que c'est bien une image RGB
                print("Test d'initialisation et de génération réussi!")
            else:
                raise ValueError("La génération n'a pas produit une image valide")
            
    except Exception as e:
        logging.error(f"Erreur lors de l'initialisation ou de la génération: {str(e)}")
        raise

if __name__ == "__main__":
    main()


"""
Tests pour le module SkyReelsManager
"""

import unittest
import torch
from pathlib import Path
from skyreels_manager import SkyReelsManager
import yaml
import os

class TestSkyReelsManager(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """Initialisation des tests - création d'une configuration de test"""
        cls.test_config_path = "test_config.yaml"
        if not os.path.exists(cls.test_config_path):
            test_config = {
                "models": {
                    "base_path": str(Path("pretrained_models/SkyReels-A1-5B").absolute()),
                    "transformer": "${base_path}/transformer",
                    "text_encoder": "${base_path}/text_encoder",
                    "siglip": "${base_path}/siglip-so400m-patch14-384",
                    "pose_guider": "${base_path}/pose_guider",
                    "vae": "${base_path}/vae"
                },
                "performance": {
                    "use_gpu": torch.cuda.is_available(),
                    "offload_to_cpu": not torch.cuda.is_available(),
                    "use_fp16": True,
                    "use_attention_slicing": True,
                    "use_vae_slicing": True,
                    "use_xformers": False,
                    "max_workers": 1,
                    "memory_limit": 2048
                }
            }
            with open(cls.test_config_path, 'w') as f:
                yaml.dump(test_config, f)
    
    def setUp(self):
        """Initialisation avant chaque test"""
        # Configuration de test pour macOS
        test_config = {
            "models": {
                "base_path": os.path.join(os.getcwd(), "pretrained_models/SkyReels-A1-5B"),
                "siglip": "${base_path}/siglip-so400m-patch14-384",
                "text_encoder": "${base_path}/text_encoder",
                "transformer": "${base_path}/transformer",
                "vae": "${base_path}/vae",
                "pose_guider": "${base_path}/pose_guider"
            },
            "performance": {
                "use_gpu": False,
                "offload_to_cpu": True,
                "use_fp16": True,
                "max_workers": 1,
                "memory_limit": 2048
            },
            "generation": {
                "batch_size": 1,
                "num_inference_steps": 20,
                "guidance_scale": 7.5,
                "height": 256,
                "width": 256
            }
        }

        # Écrire la configuration de test
        config_path = "test_config_temp.yaml"
        with open(config_path, "w") as f:
            yaml.dump(test_config, f)

        self.manager = SkyReelsManager(config_path)
        self.test_config_path = config_path
        
    def tearDown(self):
        """Nettoyage après chaque test"""
        if hasattr(self, 'manager'):
            self.manager.cleanup()

    def test_generate_content_minimal(self):
        """Test la génération de contenu avec des paramètres minimaux"""
        result = self.manager.generate_content(
            prompt="Test simple",
            num_images=1,
            height=256,  # Taille très réduite pour le test
            width=256,
            num_inference_steps=20  # Moins d'étapes
        )
        
        # Vérifier la structure du résultat
        self.assertIsInstance(result, dict)
        self.assertIn("images", result)
        self.assertIn("prompt", result)
        self.assertIn("parameters", result)
        
        # Vérifier les images
        self.assertEqual(len(result["images"]), 1)
        self.assertEqual(result["images"][0].shape, (256, 256, 3))
        
    def test_generate_single_image(self):
        """Test de génération d'une seule image"""
        prompt = "Test simple d'une image"
        result = self.manager.generate_image(
            prompt=prompt,
            width=256,  # Taille réduite pour le test
            height=256,
            num_inference_steps=20  # Moins d'étapes pour accélérer le test
        )
        
        # Vérifier que le résultat est un tableau numpy avec la bonne forme
        self.assertIsNotNone(result)
        self.assertEqual(len(result.shape), 3)
        self.assertEqual(result.shape[0], 256)
        self.assertEqual(result.shape[1], 256)
        self.assertEqual(result.shape[2], 3)  # RGB

    def test_generate_batch(self):
        """Test de génération d'un lot d'images"""
        prompts = [
            "Test de la première image",
            "Test de la deuxième image"
        ]
        results = self.manager.generate_batch(
            prompts=prompts,
            width=256,
            height=256,
            num_inference_steps=20
        )
        
        # Vérifier le nombre d'images générées
        self.assertEqual(len(results), len(prompts))
        
        # Vérifier que chaque image a la bonne forme
        for img in results:
            self.assertEqual(img.shape, (256, 256, 3))

    def test_memory_management(self):
        """Test de la gestion de la mémoire"""
        if not torch.cuda.is_available():
            self.skipTest("Test de mémoire ignoré - GPU non disponible")
            
        initial_memory = torch.cuda.memory_allocated()
        
        with SkyReelsManager(self.test_config_path) as manager:
            # Générer une image
            _ = manager.generate_image(
                prompt="Test de gestion mémoire",
                width=256,
                height=256,
                num_inference_steps=20
            )
            
            # Vérifier que la mémoire est utilisée
            peak_memory = torch.cuda.memory_allocated()
            self.assertGreater(peak_memory, initial_memory)
            
        # Vérifier que la mémoire est libérée
        final_memory = torch.cuda.memory_allocated()
        self.assertLessEqual(final_memory, initial_memory * 1.1)  # Permettre une petite marge

if __name__ == "__main__":
    unittest.main()
