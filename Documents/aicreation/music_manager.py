#!/usr/bin/env python3
"""
music_manager.py - Gestionnaire de musique douce pour vidéos nature
"""

import os
import logging
import subprocess
import tempfile
from pathlib import Path
from typing import Optional, List
import random

from config import get_config

logger = logging.getLogger(__name__)

class MusicManager:
    """Gestionnaire de musique douce sans percussion pour vidéos nature."""
    
    def __init__(self):
        """Initialize music manager."""
        self.config = get_config()
        
        # Musiques douces générées par IA (tons, fréquences apaisantes)
        self.nature_music_configs = {
            "forest": {
                "base_freq": 432,  # Fréquence apaisante
                "harmonics": [432, 648, 864],  # Harmoniques naturelles
                "duration": 180,
                "volume": 0.3,
                "description": "Sons de forêt avec harmoniques naturelles"
            },
            "ocean": {
                "base_freq": 396,  # Fréquence de guérison
                "harmonics": [396, 528, 741],
                "duration": 180,
                "volume": 0.25,
                "description": "Vagues océaniques avec fréquences de guérison"
            },
            "mountain": {
                "base_freq": 528,  # Fréquence de l'amour
                "harmonics": [528, 792, 1056],
                "duration": 180,
                "volume": 0.35,
                "description": "Ambiance montagnarde avec fréquences d'amour"
            },
            "meadow": {
                "base_freq": 741,  # Fréquence d'éveil
                "harmonics": [741, 852, 963],
                "duration": 180,
                "volume": 0.3,
                "description": "Prairie paisible avec fréquences d'éveil"
            },
            "zen": {
                "base_freq": 285,  # Fréquence de régénération
                "harmonics": [285, 417, 639],
                "duration": 180,
                "volume": 0.2,
                "description": "Méditation zen avec fréquences de régénération"
            }
        }
        
        logger.info("Music Manager initialized with nature frequencies")
    
    def generate_nature_music(self, style: str = "forest", duration: float = 180) -> Optional[str]:
        """Generate soft nature music without percussion."""
        try:
            if style not in self.nature_music_configs:
                style = "forest"  # Default
            
            config = self.nature_music_configs[style]
            
            # Create output path
            output_path = self.config["directories"]["temp"] / f"nature_music_{style}.mp3"
            
            logger.info(f"Generating {style} music: {config['description']}")
            
            # Generate using FFmpeg with multiple sine waves (no percussion)
            return self._generate_harmonic_music(config, duration, output_path)
            
        except Exception as e:
            logger.error(f"Error generating nature music: {e}")
            return None
    
    def _generate_harmonic_music(self, config: dict, duration: float, output_path: Path) -> Optional[str]:
        """Generate harmonic music using sine waves."""
        try:
            base_freq = config["base_freq"]
            harmonics = config["harmonics"]
            volume = config["volume"]
            
            # Create multiple sine wave layers for rich harmonic content
            sine_waves = []
            
            # Base frequency (strongest)
            sine_waves.append(f"sine=frequency={base_freq}:duration={duration}")
            
            # Harmonics (softer)
            for i, freq in enumerate(harmonics):
                vol_factor = 0.7 ** (i + 1)  # Decreasing volume for harmonics
                sine_waves.append(f"sine=frequency={freq}:duration={duration}")
            
            # Add some natural variation (very subtle)
            # Low frequency for depth
            sine_waves.append(f"sine=frequency={base_freq/4}:duration={duration}")
            
            # Create filter complex for mixing
            inputs = []
            mix_inputs = []
            
            for i, wave in enumerate(sine_waves):
                inputs.extend(["-f", "lavfi", "-i", wave])
                vol = volume if i == 0 else volume * (0.6 ** i)
                mix_inputs.append(f"[{i}]volume={vol}[a{i}]")
            
            # Mix all waves
            mix_filter = ";".join(mix_inputs)
            amix_inputs = "".join([f"[a{i}]" for i in range(len(sine_waves))])
            mix_filter += f";{amix_inputs}amix=inputs={len(sine_waves)}:duration=longest[out]"
            
            # FFmpeg command
            cmd = ["ffmpeg", "-y"] + inputs + [
                "-filter_complex", mix_filter,
                "-map", "[out]",
                "-t", str(duration),
                "-c:a", "mp3",
                "-b:a", "128k",
                str(output_path)
            ]
            
            logger.info(f"Generating harmonic music with {len(sine_waves)} layers")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"Generated nature music: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"FFmpeg music generation failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating harmonic music: {e}")
            return None
    
    def add_music_to_video(self, video_path: str, music_style: str = "forest") -> Optional[str]:
        """Add soft nature music to video."""
        try:
            if not os.path.exists(video_path):
                logger.error(f"Video file not found: {video_path}")
                return None
            
            # Get video duration
            duration = self._get_video_duration(video_path)
            if not duration:
                duration = 180  # Default
            
            # Generate music
            music_path = self.generate_nature_music(music_style, duration)
            if not music_path:
                logger.error("Failed to generate music")
                return video_path  # Return original video
            
            # Create output path
            video_name = Path(video_path).stem
            output_path = Path(video_path).parent / f"{video_name}_with_music.mp4"
            
            # Mix video with music
            cmd = [
                "ffmpeg", "-y",
                "-i", video_path,
                "-i", music_path,
                "-filter_complex",
                "[0:a][1:a]amix=inputs=2:duration=shortest:dropout_transition=2[audio]",
                "-map", "0:v", "-map", "[audio]",
                "-c:v", "copy",
                "-c:a", "aac",
                "-b:a", "192k",
                str(output_path)
            ]
            
            logger.info(f"Adding {music_style} music to video")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"Video with music created: {output_path} ({file_size} bytes)")
                
                # Replace original video
                os.replace(output_path, video_path)
                
                # Cleanup music file
                if os.path.exists(music_path):
                    os.remove(music_path)
                
                return video_path
            else:
                logger.error(f"Failed to add music: {result.stderr}")
                return video_path
                
        except Exception as e:
            logger.error(f"Error adding music to video: {e}")
            return video_path
    
    def _get_video_duration(self, video_path: str) -> Optional[float]:
        """Get video duration in seconds."""
        try:
            cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        
        return None
    
    def get_music_style_for_keyword(self, keyword: str) -> str:
        """Get appropriate music style for keyword."""
        keyword_lower = keyword.lower()
        
        # Map keywords to music styles
        if any(word in keyword_lower for word in ['forest', 'tree', 'jungle', 'wood']):
            return "forest"
        elif any(word in keyword_lower for word in ['ocean', 'sea', 'wave', 'water', 'river']):
            return "ocean"
        elif any(word in keyword_lower for word in ['mountain', 'peak', 'summit', 'alpine']):
            return "mountain"
        elif any(word in keyword_lower for word in ['meadow', 'field', 'grass', 'prairie']):
            return "meadow"
        elif any(word in keyword_lower for word in ['zen', 'meditation', 'peace', 'calm', 'relax']):
            return "zen"
        elif any(word in keyword_lower for word in ['nature', 'natural', 'environment']):
            # For general nature, choose randomly among peaceful styles
            return random.choice(["forest", "meadow", "zen"])
        else:
            # Default for any other topic
            return "zen"  # Most universally peaceful

# Example usage and testing
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    manager = MusicManager()
    
    print("🎵 Music Manager for Nature Videos")
    print("=" * 40)
    
    # Test music generation
    test_styles = ["forest", "ocean", "mountain", "meadow", "zen"]
    
    for style in test_styles:
        print(f"\n🎼 Testing {style} music...")
        config = manager.nature_music_configs[style]
        print(f"   📝 {config['description']}")
        print(f"   🎵 Base frequency: {config['base_freq']} Hz")
        print(f"   🎶 Harmonics: {config['harmonics']}")
        
        # Generate short sample
        music_path = manager.generate_nature_music(style, 10)  # 10 seconds for test
        if music_path:
            print(f"   ✅ Generated: {music_path}")
            # Cleanup test file
            if os.path.exists(music_path):
                os.remove(music_path)
        else:
            print(f"   ❌ Failed to generate")
    
    print(f"\n🎯 Music style for 'nature': {manager.get_music_style_for_keyword('nature')}")
    print(f"🎯 Music style for 'forest meditation': {manager.get_music_style_for_keyword('forest meditation')}")
    print(f"🎯 Music style for 'ocean waves': {manager.get_music_style_for_keyword('ocean waves')}")
