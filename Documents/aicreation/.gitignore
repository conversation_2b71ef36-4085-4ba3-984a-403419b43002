# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Generated content (keep structure but not files)
output/videos/*.mp4
output/audio/*.mp3
output/audio/*.wav
output/thumbnails/*.jpg
output/thumbnails/*.png
output/scripts/*.json
output/transcripts/*.txt
output/temp/*
output/logs/*.log

# API Keys (security)
.env
*.key
credentials.json

# Temporary files
*.tmp
*.temp
temp_*
