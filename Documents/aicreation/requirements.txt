# Professional YouTube Video Creation System v2.0
# Enterprise-grade dependencies with version pinning for stability

# ===== CORE FRAMEWORK =====
# Data validation and settings management
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# Async support
asyncio-throttle>=1.0.2
aiofiles>=23.2.1
aiohttp>=3.9.0

# ===== AI & SCRIPT GENERATION =====
# Local LLM integration
ollama>=0.1.7

# OpenAI integration (optional)
openai>=1.6.0

# ===== AUDIO PROCESSING =====
# Text-to-Speech
gtts>=2.4.0
pydub>=0.25.1

# Audio enhancement
librosa>=0.10.1
soundfile>=0.12.1
scipy>=1.11.0

# ===== VIDEO PROCESSING =====
# Video editing and processing
moviepy>=1.0.3
opencv-python>=4.8.0
ffmpeg-python>=0.2.0

# Image processing
Pillow>=10.1.0
numpy>=1.24.0

# ===== WEB & API =====
# HTTP requests with retry and rate limiting
requests>=2.31.0
httpx>=0.25.0
tenacity>=8.2.0

# Web scraping and data
pytrends>=4.9.2
beautifulsoup4>=4.12.0

# ===== CACHING & PERFORMANCE =====
# Redis for distributed caching (optional)
redis>=5.0.0
hiredis>=2.2.0

# Memory caching
cachetools>=5.3.0

# ===== MONITORING & LOGGING =====
# Structured logging
structlog>=23.2.0
colorama>=0.4.6

# Metrics and monitoring
prometheus-client>=0.19.0
psutil>=5.9.0

# ===== CONFIGURATION =====
# Environment and configuration
python-dotenv>=1.0.0
PyYAML>=6.0.1
toml>=0.10.2

# ===== UTILITIES =====
# Date and time
python-dateutil>=2.8.2

# File handling
pathlib2>=2.3.7  # Compatibility
watchdog>=3.0.0  # File monitoring

# Progress bars
tqdm>=4.66.0
rich>=13.7.0

# ===== OPTIONAL INTEGRATIONS =====
# YouTube API
google-api-python-client>=2.108.0
google-auth-oauthlib>=1.1.0
google-auth>=2.23.0

# Premium TTS services (install manually if needed)
# elevenlabs>=0.2.0
# azure-cognitiveservices-speech>=1.34.0
