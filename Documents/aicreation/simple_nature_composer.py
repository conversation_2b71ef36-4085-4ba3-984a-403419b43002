#!/usr/bin/env python3
"""
simple_nature_composer.py - Compositeur simplifié pour vidéos nature avec Pixabay
"""

import os
import logging
import subprocess
import random
from pathlib import Path
from typing import Optional, List

from config import get_config
from pixabay_videos import PixabayVideoManager
from music_manager import MusicManager

logger = logging.getLogger(__name__)

class SimpleNatureComposer:
    """Compositeur simplifié pour créer des vidéos avec arrière-plan nature."""
    
    def __init__(self):
        """Initialize simple nature composer."""
        self.config = get_config()
        self.pixabay_manager = PixabayVideoManager()
        self.music_manager = MusicManager()
        
        logger.info("Simple Nature Composer initialized")
    
    def create_nature_video(self, audio_path: str, keyword: str, add_music: bool = True) -> Optional[str]:
        """Create video with multiple nature backgrounds and audio."""
        try:
            logger.info(f"Creating nature video for '{keyword}' with multiple Pixabay videos")

            # Determine video category
            video_category = None
            if hasattr(self.pixabay_manager, 'get_video_category_for_keyword'):
                video_category = self.pixabay_manager.get_video_category_for_keyword(keyword)

            # Get multiple videos (5-8 for variety)
            videos = []
            if self.pixabay_manager.available:
                logger.info(f"🎬 Downloading multiple Pixabay videos for category: {video_category}")
                if hasattr(self.pixabay_manager, 'get_videos_for_keyword'):
                    videos = self.pixabay_manager.get_videos_for_keyword(keyword, video_category, 8)
                else:
                    videos = self.pixabay_manager.get_nature_videos_for_keyword(keyword, 8)

            if not videos:
                logger.warning("No videos available, trying fallback keywords")
                # Try fallback keywords
                fallback_keywords = ["nature", "landscape", "forest", "ocean", "mountains"]
                for fallback in fallback_keywords:
                    if hasattr(self.pixabay_manager, 'get_videos_for_keyword'):
                        videos = self.pixabay_manager.get_videos_for_keyword(fallback, "nature", 5)
                    else:
                        videos = self.pixabay_manager.get_nature_videos_for_keyword(fallback, 5)
                    if videos:
                        logger.info(f"Found videos with fallback keyword: {fallback}")
                        break

            if not videos:
                logger.error("No videos available even with fallbacks")
                return None

            logger.info(f"✅ Downloaded {len(videos)} Pixabay videos for composition")

            # Get audio duration
            audio_duration = self._get_audio_duration(audio_path)
            if not audio_duration:
                audio_duration = 180  # Default 3 minutes

            # Create output path
            output_path = self.config["directories"]["videos"] / f"{keyword.replace(' ', '_')}.mp4"

            # Create video with multiple backgrounds
            video_path = self._create_multi_video_composition(videos, audio_path, audio_duration, output_path)

            if not video_path:
                return None

            # Add music if requested
            if add_music and self.music_manager:
                logger.info("🎵 Adding soft nature music")
                music_style = self.music_manager.get_music_style_for_keyword(keyword)
                video_path = self.music_manager.add_music_to_video(video_path, music_style)

            return video_path

        except Exception as e:
            logger.error(f"Error creating nature video: {e}")
            return None
    
    def _create_multi_video_composition(self, videos: List[str], audio_path: str,
                                       duration: float, output_path: Path) -> Optional[str]:
        """Create video composition with multiple videos."""
        try:
            logger.info(f"Creating composition with {len(videos)} videos")

            # Calculate duration per video segment
            segment_duration = duration / len(videos)
            min_segment_duration = 8  # Minimum 8 seconds per segment

            if segment_duration < min_segment_duration:
                # If too many videos for the duration, use fewer videos
                num_videos = max(1, int(duration / min_segment_duration))
                videos = videos[:num_videos]
                segment_duration = duration / len(videos)
                logger.info(f"Adjusted to {len(videos)} videos with {segment_duration:.1f}s each")

            # Create video segments
            video_segments = []
            for i, video_path in enumerate(videos):
                segment_output = output_path.parent / f"segment_{i}.mp4"

                # Create segment with crossfade transitions
                cmd = [
                    "ffmpeg", "-y",
                    "-i", video_path,
                    "-t", str(segment_duration),
                    "-vf", f"scale=1920:1080:force_original_aspect_ratio=increase,crop=1920:1080,fade=in:0:30,fade=out:{int(segment_duration*30-30)}:30",
                    "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                    str(segment_output)
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

                if result.returncode == 0 and segment_output.exists():
                    video_segments.append(str(segment_output))
                    logger.info(f"✅ Created segment {i+1}/{len(videos)}")
                else:
                    logger.warning(f"⚠️ Failed to create segment {i+1}: {result.stderr}")

            if not video_segments:
                logger.error("No video segments created")
                return None

            # Concatenate all segments
            concat_file = output_path.parent / "concat_list.txt"
            with open(concat_file, 'w') as f:
                for segment in video_segments:
                    f.write(f"file '{segment}'\n")

            # Combine segments and add audio
            cmd = [
                "ffmpeg", "-y",
                "-f", "concat", "-safe", "0", "-i", str(concat_file),
                "-i", audio_path,
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-c:a", "aac", "-b:a", "192k",
                "-shortest",
                str(output_path)
            ]

            logger.info("🎬 Combining all segments with audio...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            # Cleanup temporary files
            for segment in video_segments:
                Path(segment).unlink(missing_ok=True)
            concat_file.unlink(missing_ok=True)

            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"✅ Multi-video composition created: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"Composition failed: {result.stderr}")
                # Fallback to simple single video
                return self._create_simple_nature_video(videos[0], audio_path, duration, output_path)

        except Exception as e:
            logger.error(f"Error creating multi-video composition: {e}")
            # Fallback to simple single video
            if videos:
                return self._create_simple_nature_video(videos[0], audio_path, duration, output_path)
            return None

    def _select_best_video(self, nature_videos: List[str]) -> Optional[str]:
        """Select the best nature video from the list."""
        if not nature_videos:
            return None

        # For now, just select the first one
        # Could add logic to select based on duration, quality, etc.
        return nature_videos[0]
    
    def _create_simple_nature_video(self, nature_video: str, audio_path: str, 
                                  duration: float, output_path: Path) -> Optional[str]:
        """Create simple video with nature background."""
        try:
            # Simple approach: loop nature video and add audio
            cmd = [
                "ffmpeg", "-y",
                "-stream_loop", "-1",  # Loop nature video
                "-i", nature_video,
                "-i", audio_path,
                "-t", str(duration),  # Duration from audio
                "-c:v", "libx264",
                "-c:a", "aac",
                "-b:a", "192k",
                "-shortest",  # Stop when shortest input ends
                str(output_path)
            ]
            
            logger.info(f"Creating simple nature video: {output_path}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"✅ Nature video created: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"FFmpeg failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating simple nature video: {e}")
            return None
    
    def _get_audio_duration(self, audio_path: str) -> Optional[float]:
        """Get audio duration in seconds."""
        try:
            cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        
        return None

# Test function
def test_simple_composer():
    """Test the simple nature composer."""
    print("🌿 TEST DU COMPOSITEUR NATURE SIMPLIFIÉ")
    print("=" * 50)
    
    try:
        composer = SimpleNatureComposer()
        
        # Check if we have audio from previous generation
        audio_path = "output/audio/nature.mp3"
        
        if not Path(audio_path).exists():
            print(f"❌ Audio non trouvé: {audio_path}")
            print("💡 Créez d'abord l'audio avec: python create_nature_video.py")
            return
        
        print(f"✅ Audio trouvé: {audio_path}")
        
        # Test creation
        print("🎬 Création de la vidéo nature avec arrière-plan Pixabay...")
        
        video_path = composer.create_nature_video(audio_path, "nature_test", add_music=True)
        
        if video_path and Path(video_path).exists():
            file_size = Path(video_path).stat().st_size / (1024 * 1024)
            print(f"✅ Vidéo créée: {video_path} ({file_size:.1f} MB)")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
        else:
            print("❌ Échec de création")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_simple_composer()
