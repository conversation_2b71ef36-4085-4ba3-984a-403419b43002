#!/usr/bin/env python3
"""
elevenlabs_tts.py - ElevenLabs Text-to-Speech integration for ultra-realistic voices
"""

import os
import logging
import requests
import time
from pathlib import Path
from typing import Dict, Optional, List
import json

from config import get_config

logger = logging.getLogger(__name__)

class ElevenLabsTTS:
    """ElevenLabs Text-to-Speech manager for ultra-realistic voice generation."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize ElevenLabs TTS client."""
        self.config = get_config()
        
        # API credentials
        self.api_key = api_key or os.getenv('ELEVENLABS_API_KEY')
        
        # API endpoints
        self.base_url = "https://api.elevenlabs.io/v1"
        self.tts_endpoint = f"{self.base_url}/text-to-speech"
        self.voices_endpoint = f"{self.base_url}/voices"
        
        # Pre-defined voices (these are real ElevenLabs voice IDs)
        self.available_voices = {
            # English voices
            "rachel_calm": "21m00Tcm4TlvDq8ikWAM",  # <PERSON> <PERSON> <PERSON><PERSON>, young female
            "drew_news": "29vD33N1CtxCmqQRPOHJ",   # Drew - News anchor, male
            "clyde_warm": "2EiwWnXFnvU5JabPnv8n",  # Clyde - Warm, middle-aged male
            "bella_soft": "EXAVITQu4vr4xnSDxMaL",  # Bella - Soft, young female
            "antoni_deep": "ErXwobaYiN019PkySvjV", # Antoni - Deep, mature male
            "elli_energetic": "MF3mGyEYCl7XYWbV9V6O", # Elli - Energetic female
            "josh_professional": "TxGEqnHWrfWFTfGW9XjX", # Josh - Professional male
            "arnold_strong": "VR6AewLTigWG4xSOukaG",  # Arnold - Strong, confident
            "adam_deep": "pNInz6obpgDQGcFmaJgB",     # Adam - Deep, narrative
            "sam_serious": "yoZ06aMxZJJ28mfd3POQ"    # Sam - Serious, authoritative
        }
        
        # Check availability
        self.available = self._check_availability()
        
        if self.available:
            logger.info("ElevenLabs TTS initialized successfully")
        else:
            logger.warning("ElevenLabs TTS not available - missing API key or connection failed")
    
    def _check_availability(self) -> bool:
        """Check if ElevenLabs TTS is available."""
        if not self.api_key:
            logger.warning("ElevenLabs API key not provided")
            return False
        
        try:
            # Test API connection
            headers = {
                'xi-api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            response = requests.get(self.voices_endpoint, headers=headers, timeout=10)
            
            if response.status_code == 200:
                return True
            elif response.status_code == 401:
                logger.error("ElevenLabs API key is invalid")
                return False
            else:
                logger.warning(f"ElevenLabs API returned status {response.status_code}")
                return False
            
        except Exception as e:
            logger.error(f"Error checking ElevenLabs availability: {e}")
            return False
    
    def get_available_voices(self) -> Dict[str, str]:
        """Get available voice IDs and their descriptions."""
        if not self.available:
            return {}
        
        try:
            headers = {'xi-api-key': self.api_key}
            response = requests.get(self.voices_endpoint, headers=headers)
            
            if response.status_code == 200:
                voices_data = response.json()
                voices = {}
                
                for voice in voices_data.get('voices', []):
                    voice_id = voice.get('voice_id')
                    name = voice.get('name')
                    if voice_id and name:
                        voices[name.lower().replace(' ', '_')] = voice_id
                
                # Merge with pre-defined voices
                voices.update(self.available_voices)
                return voices
            
        except Exception as e:
            logger.error(f"Error fetching ElevenLabs voices: {e}")
        
        return self.available_voices
    
    def generate_speech(self, text: str, voice_id: Optional[str] = None, 
                       model_id: str = "eleven_monolingual_v1") -> Optional[bytes]:
        """Generate speech from text using ElevenLabs TTS."""
        if not self.available:
            logger.error("ElevenLabs TTS not available")
            return None
        
        # Use default voice if none specified
        if not voice_id:
            voice_id = self.available_voices["rachel_calm"]
        
        try:
            url = f"{self.tts_endpoint}/{voice_id}"
            
            headers = {
                'xi-api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            data = {
                'text': text,
                'model_id': model_id,
                'voice_settings': {
                    'stability': 0.5,
                    'similarity_boost': 0.75,
                    'style': 0.0,
                    'use_speaker_boost': True
                }
            }
            
            logger.info(f"Generating speech with ElevenLabs (voice: {voice_id})")
            
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            
            logger.info("Successfully generated speech with ElevenLabs")
            return response.content
            
        except Exception as e:
            logger.error(f"Error generating speech with ElevenLabs: {e}")
            return None
    
    def set_voice_for_keyword(self, keyword: str) -> str:
        """Select appropriate voice based on keyword."""
        keyword_lower = keyword.lower()
        
        # Voice selection logic based on content type
        if any(word in keyword_lower for word in ['business', 'finance', 'professional', 'corporate']):
            return self.available_voices["josh_professional"]
        elif any(word in keyword_lower for word in ['technology', 'ai', 'science', 'research']):
            return self.available_voices["adam_deep"]
        elif any(word in keyword_lower for word in ['lifestyle', 'health', 'wellness', 'beauty']):
            return self.available_voices["bella_soft"]
        elif any(word in keyword_lower for word in ['entertainment', 'gaming', 'fun', 'trending']):
            return self.available_voices["elli_energetic"]
        elif any(word in keyword_lower for word in ['news', 'breaking', 'update', 'report']):
            return self.available_voices["drew_news"]
        elif any(word in keyword_lower for word in ['education', 'tutorial', 'learning', 'guide']):
            return self.available_voices["clyde_warm"]
        elif any(word in keyword_lower for word in ['motivation', 'success', 'achievement']):
            return self.available_voices["arnold_strong"]
        else:
            # Default to calm female voice
            return self.available_voices["rachel_calm"]
    
    def generate_enhanced_speech(self, script: Dict[str, str], keyword: str, 
                                voice_id: Optional[str] = None) -> Optional[str]:
        """Generate enhanced speech from script using ElevenLabs TTS."""
        try:
            # Create speech text from script
            speech_parts = []
            
            # Helper function to extract text
            def extract_text(value):
                if isinstance(value, str):
                    return value
                elif isinstance(value, dict):
                    return value.get("text", str(value))
                else:
                    return str(value)
            
            # Build speech with natural pauses
            intro = extract_text(script.get("introduction", ""))
            if intro:
                speech_parts.append(f"{intro}")
            
            for i in range(1, 4):
                key = f"key_point_{i}"
                point = script.get(key, "")
                if point:
                    point_text = extract_text(point)
                    speech_parts.append(f"{point_text}")
            
            conclusion = extract_text(script.get("conclusion", ""))
            if conclusion:
                speech_parts.append(conclusion)
            
            # Join with natural pauses
            full_text = " ... ".join(speech_parts)
            
            # ElevenLabs has a character limit (usually 2500 for free tier)
            if len(full_text) > 2000:
                full_text = full_text[:2000] + "..."
                logger.warning("Text truncated to fit ElevenLabs character limit")
            
            # Select voice if not provided
            if not voice_id:
                voice_id = self.set_voice_for_keyword(keyword)
            
            # Generate speech
            audio_data = self.generate_speech(full_text, voice_id)
            
            if not audio_data:
                logger.error("Failed to generate speech with ElevenLabs")
                return None
            
            # Save audio file
            output_path = self.config["directories"]["audio"] / f"{keyword.replace(' ', '_')}.mp3"
            
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            logger.info(f"Enhanced speech generated with ElevenLabs: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error generating enhanced speech: {e}")
            return None

# Example usage and testing
if __name__ == "__main__":
    # Test ElevenLabs TTS
    logging.basicConfig(level=logging.INFO)
    
    tts = ElevenLabsTTS()
    
    if tts.available:
        print("✅ ElevenLabs TTS is available")
        
        # Get available voices
        voices = tts.get_available_voices()
        print(f"Available voices: {len(voices)}")
        for name, voice_id in list(voices.items())[:5]:  # Show first 5
            print(f"  • {name}: {voice_id}")
        
        # Test speech generation
        test_script = {
            "title": "ElevenLabs Test Video",
            "introduction": "Welcome to this amazing test of ElevenLabs text-to-speech technology.",
            "key_point_1": "This is the first important point with ultra-realistic voice.",
            "key_point_2": "Here's the second crucial insight with natural expression.",
            "key_point_3": "Finally, the third key takeaway with emotional depth.",
            "conclusion": "Thanks for listening to this ElevenLabs demonstration!"
        }
        
        audio_path = tts.generate_enhanced_speech(test_script, "test_keyword")
        if audio_path:
            print(f"✅ Test audio generated: {audio_path}")
        else:
            print("❌ Test audio generation failed")
    else:
        print("❌ ElevenLabs TTS not available")
        print("Set ELEVENLABS_API_KEY environment variable")
        print("Get your API key at: https://elevenlabs.io/")
