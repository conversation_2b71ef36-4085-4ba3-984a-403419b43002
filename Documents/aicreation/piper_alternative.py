#!/usr/bin/env python3
"""
piper_alternative.py - Alternative Piper TTS using Python subprocess with better error handling
"""

import os
import subprocess
import logging
from pathlib import Path
import tempfile
import time

logger = logging.getLogger(__name__)

class PiperAlternative:
    """Alternative Piper TTS implementation."""
    
    def __init__(self):
        self.piper_executable = "./piper/piper"
        self.models_dir = Path.home() / ".local" / "share" / "piper" / "models"
        self.available = self._check_availability()
    
    def _check_availability(self) -> bool:
        """Check if Piper is available."""
        piper_path = Path(self.piper_executable)
        
        if not piper_path.exists():
            logger.error(f"Piper executable not found: {piper_path}")
            return False
        
        # Ensure executable permissions
        piper_path.chmod(0o755)
        
        # Check for model
        models = list(self.models_dir.glob("*.onnx"))
        if not models:
            logger.error("No Piper models found")
            return False
        
        logger.info("Piper Alternative ready")
        return True
    
    def _setup_environment(self):
        """Setup environment for Piper."""
        env = os.environ.copy()
        
        # Library paths for macOS
        lib_paths = [
            "/opt/homebrew/lib",
            "/usr/local/lib",
            "./piper"
        ]
        
        env['DYLD_LIBRARY_PATH'] = ":".join(lib_paths)
        env['DYLD_FALLBACK_LIBRARY_PATH'] = ":".join(lib_paths)
        env['ESPEAK_DATA_PATH'] = "./piper/espeak-ng-data"
        
        return env
    
    def synthesize_text_to_file(self, text: str, output_file: str) -> bool:
        """Synthesize text to audio file using alternative method."""
        if not self.available:
            return False
        
        try:
            # Get first available model
            models = list(self.models_dir.glob("*.onnx"))
            if not models:
                logger.error("No models available")
                return False
            
            model_path = models[0]
            logger.info(f"Using model: {model_path.name}")
            
            # Setup environment
            env = self._setup_environment()
            
            # Create temporary text file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(text)
                text_file = f.name
            
            try:
                # Method 1: Try with file input
                logger.info("Trying Piper with file input...")
                
                cmd = [
                    self.piper_executable,
                    "--model", str(model_path),
                    "--output_file", output_file,
                    "--input_file", text_file
                ]
                
                result = subprocess.run(
                    cmd, 
                    env=env, 
                    capture_output=True, 
                    text=True, 
                    timeout=20
                )
                
                if result.returncode == 0 and Path(output_file).exists():
                    logger.info("✅ Piper synthesis successful with file input")
                    return True
                
                # Method 2: Try with stdin
                logger.info("Trying Piper with stdin...")
                
                cmd = [
                    self.piper_executable,
                    "--model", str(model_path),
                    "--output_file", output_file
                ]
                
                with open(text_file, 'r') as f:
                    result = subprocess.run(
                        cmd,
                        stdin=f,
                        env=env,
                        capture_output=True,
                        text=True,
                        timeout=20
                    )
                
                if result.returncode == 0 and Path(output_file).exists():
                    logger.info("✅ Piper synthesis successful with stdin")
                    return True
                
                # Method 3: Try with shell and echo
                logger.info("Trying Piper with shell command...")
                
                shell_cmd = f"echo '{text}' | {self.piper_executable} --model {model_path} --output_file {output_file}"
                
                result = subprocess.run(
                    shell_cmd,
                    shell=True,
                    env=env,
                    capture_output=True,
                    text=True,
                    timeout=20
                )
                
                if result.returncode == 0 and Path(output_file).exists():
                    logger.info("✅ Piper synthesis successful with shell")
                    return True
                
                logger.error(f"All Piper methods failed. Last error: {result.stderr}")
                return False
                
            finally:
                # Cleanup temp file
                Path(text_file).unlink(missing_ok=True)
                
        except subprocess.TimeoutExpired:
            logger.error("Piper synthesis timeout")
            return False
        except Exception as e:
            logger.error(f"Piper synthesis error: {e}")
            return False
    
    def create_audio_from_script(self, script_text: str, output_path: str) -> bool:
        """Create audio from script text."""
        logger.info(f"Creating audio with Piper Alternative: {len(script_text)} chars")
        
        # Split into smaller chunks to avoid timeout
        sentences = [s.strip() + "." for s in script_text.split('.') if s.strip()]
        
        if len(sentences) <= 3:
            # Short text, try directly
            return self.synthesize_text_to_file(script_text, output_path)
        
        # Long text, process in chunks
        logger.info(f"Processing {len(sentences)} sentences in chunks")
        
        chunk_files = []
        chunk_size = 2  # Process 2 sentences at a time
        
        for i in range(0, len(sentences), chunk_size):
            chunk_sentences = sentences[i:i+chunk_size]
            chunk_text = " ".join(chunk_sentences)
            chunk_file = f"chunk_{i//chunk_size}.wav"
            
            logger.info(f"Processing chunk {i//chunk_size + 1}: {chunk_text[:50]}...")
            
            if self.synthesize_text_to_file(chunk_text, chunk_file):
                chunk_files.append(chunk_file)
                logger.info(f"✅ Chunk {i//chunk_size + 1} created")
            else:
                logger.error(f"❌ Chunk {i//chunk_size + 1} failed")
        
        if not chunk_files:
            logger.error("No chunks created successfully")
            return False
        
        if len(chunk_files) == 1:
            # Only one chunk, just rename it
            Path(chunk_files[0]).rename(output_path)
            return True
        
        # Combine chunks with FFmpeg
        logger.info(f"Combining {len(chunk_files)} chunks...")
        
        try:
            # Create file list for FFmpeg
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                for chunk in chunk_files:
                    f.write(f"file '{chunk}'\n")
                filelist = f.name
            
            # Combine with FFmpeg
            cmd = [
                "ffmpeg", "-y", "-f", "concat", "-safe", "0",
                "-i", filelist, "-c", "copy", output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            # Cleanup
            Path(filelist).unlink(missing_ok=True)
            for chunk in chunk_files:
                Path(chunk).unlink(missing_ok=True)
            
            if result.returncode == 0 and Path(output_path).exists():
                logger.info(f"✅ Audio combined successfully: {output_path}")
                return True
            else:
                logger.error(f"FFmpeg combination failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error combining chunks: {e}")
            return False

def test_piper_alternative():
    """Test the alternative Piper implementation."""
    print("🧪 TEST PIPER ALTERNATIVE")
    print("=" * 30)
    
    piper = PiperAlternative()
    
    if not piper.available:
        print("❌ Piper Alternative non disponible")
        return False
    
    test_text = "Bonjour, ceci est un test de Piper TTS alternatif."
    output_file = "test_piper_alt.wav"
    
    print(f"📝 Test: {test_text}")
    
    success = piper.synthesize_text_to_file(test_text, output_file)
    
    if success and Path(output_file).exists():
        file_size = Path(output_file).stat().st_size
        print(f"✅ Test réussi! Fichier: {file_size} bytes")
        Path(output_file).unlink(missing_ok=True)
        return True
    else:
        print("❌ Test échoué")
        return False

if __name__ == "__main__":
    test_piper_alternative()
