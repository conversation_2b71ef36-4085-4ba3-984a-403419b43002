#!/usr/bin/env python3
"""
test_minimax.py - Test script for Minimax TTS integration
"""

import os
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_minimax_availability():
    """Test if Minimax TTS is available."""
    print("🔍 Testing Minimax TTS availability...")
    
    try:
        from minimax_tts import MinimaxTTS
        
        # Check environment variables
        api_key = os.getenv('MINIMAX_API_KEY')
        group_id = os.getenv('MINIMAX_GROUP_ID')
        
        print(f"API Key: {'✅ Set' if api_key else '❌ Not set'}")
        print(f"Group ID: {'✅ Set' if group_id else '❌ Not set'}")
        
        if not api_key or not group_id:
            print("\n⚠️  To use Minimax TTS, set these environment variables:")
            print("export MINIMAX_API_KEY='your_api_key'")
            print("export MINIMAX_GROUP_ID='your_group_id'")
            print("\nGet your credentials at: https://www.minimaxi.com/")
            return False
        
        # Initialize Minimax TTS
        tts = MinimaxTTS()
        
        if tts.available:
            print("✅ Minimax TTS is available and ready!")
            print(f"Available voices: {list(tts.available_voices.keys())}")
            return True
        else:
            print("❌ Minimax TTS initialization failed")
            return False
            
    except ImportError:
        print("❌ Minimax TTS module not found")
        return False
    except Exception as e:
        print(f"❌ Error testing Minimax: {e}")
        return False

def test_voice_generation():
    """Test voice generation with Minimax."""
    print("\n🎤 Testing voice generation...")
    
    try:
        from minimax_tts import MinimaxTTS
        
        tts = MinimaxTTS()
        
        if not tts.available:
            print("❌ Minimax TTS not available")
            return False
        
        # Test script
        test_script = {
            "title": "Minimax TTS Test",
            "introduction": "Welcome to this amazing test of Minimax text-to-speech technology.",
            "key_point_1": "This is the first key point demonstrating realistic voice synthesis.",
            "key_point_2": "Here's the second point showing emotional expression capabilities.",
            "key_point_3": "Finally, the third point highlights multilingual support.",
            "conclusion": "Thanks for listening to this Minimax TTS demonstration!"
        }
        
        # Generate speech
        print("🔄 Generating speech with Minimax...")
        audio_path = tts.generate_enhanced_speech(test_script, "minimax_test")
        
        if audio_path:
            file_size = Path(audio_path).stat().st_size / 1024  # KB
            print(f"✅ Audio generated successfully!")
            print(f"📁 File: {audio_path}")
            print(f"📊 Size: {file_size:.1f} KB")
            return True
        else:
            print("❌ Audio generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error generating voice: {e}")
        return False

def test_voice_selection():
    """Test voice selection logic."""
    print("\n🎯 Testing voice selection...")
    
    try:
        from minimax_tts import MinimaxTTS
        
        tts = MinimaxTTS()
        
        if not tts.available:
            print("❌ Minimax TTS not available")
            return False
        
        # Test different keywords
        test_keywords = [
            "business finance",
            "artificial intelligence",
            "health wellness",
            "gaming entertainment",
            "education tutorial",
            "random topic"
        ]
        
        print("Voice selection for different topics:")
        for keyword in test_keywords:
            voice_id = tts.set_voice_for_keyword(keyword)
            print(f"  • '{keyword}' → {voice_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing voice selection: {e}")
        return False

def test_audio_manager_integration():
    """Test integration with AudioManager."""
    print("\n🔗 Testing AudioManager integration...")
    
    try:
        from audio_manager import AudioManager
        
        audio_manager = AudioManager()
        
        # Check if Minimax is integrated
        has_minimax = hasattr(audio_manager, 'minimax_tts') and audio_manager.minimax_tts is not None
        
        print(f"Minimax integration: {'✅ Active' if has_minimax else '❌ Not active'}")
        
        if has_minimax:
            print(f"Minimax available: {'✅ Yes' if audio_manager.minimax_tts.available else '❌ No'}")
        
        # Test audio generation
        test_script = {
            "title": "Integration Test",
            "introduction": "Testing AudioManager with Minimax integration.",
            "key_point_1": "This should use Minimax TTS if available.",
            "key_point_2": "Otherwise it will fallback to gTTS.",
            "key_point_3": "Both options provide good quality audio.",
            "conclusion": "Integration test completed successfully!"
        }
        
        print("🔄 Generating audio through AudioManager...")
        audio_path = audio_manager.generate_audio(test_script, "integration_test")
        
        if audio_path:
            file_size = Path(audio_path).stat().st_size / 1024  # KB
            print(f"✅ Audio generated through AudioManager!")
            print(f"📁 File: {audio_path}")
            print(f"📊 Size: {file_size:.1f} KB")
            return True
        else:
            print("❌ Audio generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        return False

def show_setup_instructions():
    """Show setup instructions for Minimax."""
    print("\n📋 MINIMAX TTS SETUP INSTRUCTIONS:")
    print("=" * 50)
    print("1. Sign up at https://www.minimaxi.com/")
    print("2. Get your API credentials:")
    print("   - API Key")
    print("   - Group ID")
    print("3. Set environment variables:")
    print("   export MINIMAX_API_KEY='your_api_key_here'")
    print("   export MINIMAX_GROUP_ID='your_group_id_here'")
    print("4. Restart your terminal and run this test again")
    print("\n💡 Benefits of Minimax TTS:")
    print("   • Ultra-realistic voice synthesis")
    print("   • Emotional expression capabilities")
    print("   • 300+ pre-built voices")
    print("   • Multilingual support (17+ languages)")
    print("   • Voice cloning with 10 seconds of audio")
    print("   • Professional quality output")

def main():
    """Main test function."""
    print("🚀 MINIMAX TTS INTEGRATION TEST")
    print("=" * 40)
    
    # Test 1: Availability
    minimax_available = test_minimax_availability()
    
    if not minimax_available:
        show_setup_instructions()
        return
    
    # Test 2: Voice generation
    voice_test = test_voice_generation()
    
    # Test 3: Voice selection
    selection_test = test_voice_selection()
    
    # Test 4: Integration
    integration_test = test_audio_manager_integration()
    
    # Summary
    print("\n📊 TEST SUMMARY:")
    print("=" * 20)
    print(f"Minimax availability: {'✅' if minimax_available else '❌'}")
    print(f"Voice generation: {'✅' if voice_test else '❌'}")
    print(f"Voice selection: {'✅' if selection_test else '❌'}")
    print(f"AudioManager integration: {'✅' if integration_test else '❌'}")
    
    if all([minimax_available, voice_test, selection_test, integration_test]):
        print("\n🎉 All tests passed! Minimax TTS is ready to use.")
        print("💡 Your videos will now have ultra-realistic voices!")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
