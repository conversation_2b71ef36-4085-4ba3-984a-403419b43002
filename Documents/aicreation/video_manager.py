#!/usr/bin/env python3
"""
video_manager.py - Enhanced video generation and processing
"""

import logging
import os
import subprocess
import time
from pathlib import Path
from typing import Dict, Optional, List

from config import get_config

logger = logging.getLogger(__name__)

class VideoManager:
    """Enhanced video manager for creating professional videos."""
    
    def __init__(self):
        self.config = get_config()
        self.video_config = self.config["video"]
        self.blender_config = self.config["blender"]
        self.directories = self.config["directories"]
        
    def _check_blender_availability(self) -> bool:
        """Check if Blender is available."""
        try:
            blender_path = self.blender_config["executable"]
            if not os.path.exists(blender_path):
                logger.warning(f"Blender not found at {blender_path}")
                return False
            
            # Test Blender execution
            result = subprocess.run([
                blender_path, "--version"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logger.info("Blender is available and working")
                return True
            else:
                logger.warning("Blender test failed")
                return False
                
        except Exception as e:
            logger.error(f"Error checking Blender availability: {e}")
            return False
    
    def _create_blender_video(self, script: Dict[str, str], audio_path: str, keyword: str) -> Optional[str]:
        """Create video using Blender."""
        try:
            video_path = self.directories["videos"] / f"{keyword.replace(' ', '_')}.mp4"
            script_path = self.directories["scripts"] / f"{keyword.replace(' ', '_')}.json"
            blender_script = Path(__file__).parent / "blender_simple.py"
            
            if not blender_script.exists():
                logger.error(f"Blender script not found: {blender_script}")
                return None
            
            # Create Blender command
            cmd = [
                self.blender_config["executable"],
                "--background",
                "--python", str(blender_script),
                "--",
                "--title", script["title"],
                "--script", str(script_path),
                "--audio", audio_path,
                "--output", str(video_path)
            ]
            
            logger.info(f"Creating video with Blender for '{keyword}'")
            start_time = time.time()
            
            # Run Blender with timeout
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes timeout
            )
            
            elapsed_time = time.time() - start_time
            logger.info(f"Blender rendering completed in {elapsed_time:.2f} seconds")
            
            if result.returncode == 0:
                if video_path.exists():
                    file_size = video_path.stat().st_size
                    logger.info(f"Blender video created: {video_path} ({file_size} bytes)")
                    return str(video_path)
                else:
                    logger.error("Blender completed but video file not found")
                    return None
            else:
                logger.error(f"Blender failed: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error("Blender rendering timed out")
            return None
        except Exception as e:
            logger.error(f"Error creating Blender video: {e}")
            return None
    
    def _create_ffmpeg_video(self, script: Dict[str, str], audio_path: str, keyword: str) -> Optional[str]:
        """Create video using FFmpeg as fallback."""
        try:
            video_path = self.directories["videos"] / f"{keyword.replace(' ', '_')}.mp4"
            
            # Create a more sophisticated FFmpeg command
            title = script["title"]
            safe_title = title.replace("'", "'\\''").replace(":", "\\:").replace(",", "\\,")
            
            # Get audio duration for video length
            duration_cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", audio_path
            ]
            
            try:
                duration_result = subprocess.run(duration_cmd, capture_output=True, text=True)
                duration = float(duration_result.stdout.strip()) if duration_result.returncode == 0 else 180
            except:
                duration = 180  # Default 3 minutes
            
            # Create video with animated text
            cmd = [
                "ffmpeg", "-y",
                "-f", "lavfi", "-i", f"color=c=0x1a1a2e:s=1920x1080:d={duration}",
                "-i", audio_path,
                "-filter_complex", 
                f"[0:v]drawtext=fontsize=60:fontcolor=white:x=(w-text_w)/2:y=h/3:text='{safe_title}':enable='between(t,0,5)',"
                f"drawtext=fontsize=40:fontcolor=yellow:x=(w-text_w)/2:y=h*2/3:text='Subscribe for more!':enable='between(t,{duration-5},{duration})'[v]",
                "-map", "[v]", "-map", "1:a",
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-c:a", "aac", "-b:a", "192k",
                "-shortest",
                str(video_path)
            ]
            
            logger.info(f"Creating video with FFmpeg for '{keyword}'")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                if video_path.exists():
                    file_size = video_path.stat().st_size
                    logger.info(f"FFmpeg video created: {video_path} ({file_size} bytes)")
                    return str(video_path)
                else:
                    logger.error("FFmpeg completed but video file not found")
                    return None
            else:
                logger.error(f"FFmpeg failed: {result.stderr}")
                return self._create_simple_ffmpeg_video(script, audio_path, keyword)
                
        except Exception as e:
            logger.error(f"Error creating FFmpeg video: {e}")
            return self._create_simple_ffmpeg_video(script, audio_path, keyword)
    
    def _create_simple_ffmpeg_video(self, script: Dict[str, str], audio_path: str, keyword: str) -> Optional[str]:
        """Create simple video with FFmpeg as last resort."""
        try:
            video_path = self.directories["videos"] / f"{keyword.replace(' ', '_')}.mp4"
            
            # Very simple command that should always work
            cmd = [
                "ffmpeg", "-y",
                "-f", "lavfi", "-i", "color=c=blue:s=1280x720:d=180",
                "-i", audio_path,
                "-c:v", "libx264", "-c:a", "aac",
                "-shortest",
                str(video_path)
            ]
            
            logger.info(f"Creating simple video with FFmpeg for '{keyword}'")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0 and video_path.exists():
                file_size = video_path.stat().st_size
                logger.info(f"Simple FFmpeg video created: {video_path} ({file_size} bytes)")
                return str(video_path)
            else:
                logger.error(f"Simple FFmpeg failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating simple FFmpeg video: {e}")
            return None
    
    def create_video(self, script: Dict[str, str], audio_path: str, keyword: str) -> Optional[str]:
        """Create video using the best available method."""
        if not audio_path or not os.path.exists(audio_path):
            logger.error("Audio file not found or invalid")
            return None
        
        # Create transcript file
        self._create_transcript(script, keyword)
        
        # Try Blender first if available
        if self._check_blender_availability():
            logger.info("Attempting to create professional video with Blender")
            video_path = self._create_blender_video(script, audio_path, keyword)
            if video_path:
                return video_path
            else:
                logger.warning("Blender failed, falling back to FFmpeg")
        
        # Fallback to FFmpeg
        logger.info("Creating video with FFmpeg")
        video_path = self._create_ffmpeg_video(script, audio_path, keyword)
        if video_path:
            return video_path
        
        logger.error("All video creation methods failed")
        return None
    
    def _create_transcript(self, script: Dict[str, str], keyword: str):
        """Create a transcript file."""
        try:
            transcript_path = self.directories["transcripts"] / f"{keyword.replace(' ', '_')}.txt"
            
            with open(transcript_path, 'w', encoding='utf-8') as f:
                f.write(f"Video Transcript: {script['title']}\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Introduction:\n{script['introduction']}\n\n")
                f.write(f"Key Point 1:\n{script['key_point_1']}\n\n")
                f.write(f"Key Point 2:\n{script['key_point_2']}\n\n")
                f.write(f"Key Point 3:\n{script['key_point_3']}\n\n")
                f.write(f"Conclusion:\n{script['conclusion']}\n")
            
            logger.info(f"Transcript created: {transcript_path}")
            
        except Exception as e:
            logger.error(f"Error creating transcript: {e}")
    
    def validate_video(self, video_path: str) -> bool:
        """Validate that video file is properly formatted."""
        try:
            path = Path(video_path)
            if not path.exists():
                return False
            
            # Check file size
            file_size = path.stat().st_size
            if file_size < 100000:  # Less than 100KB
                logger.warning(f"Video file seems too small: {file_size} bytes")
                return False
            
            # Check video properties using ffprobe
            cmd = [
                "ffprobe", "-v", "quiet", "-print_format", "json",
                "-show_format", "-show_streams", str(path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("ffprobe failed to analyze video")
                return False
            
            import json
            info = json.loads(result.stdout)
            
            # Check if we have video and audio streams
            has_video = any(stream.get('codec_type') == 'video' for stream in info.get('streams', []))
            has_audio = any(stream.get('codec_type') == 'audio' for stream in info.get('streams', []))
            
            if not has_video:
                logger.error("Video file has no video stream")
                return False
            
            if not has_audio:
                logger.warning("Video file has no audio stream")
            
            # Check duration
            duration = float(info.get('format', {}).get('duration', 0))
            if duration < 10:  # Less than 10 seconds
                logger.warning(f"Video duration seems too short: {duration} seconds")
                return False
            
            logger.info(f"Video validation passed: {video_path} ({duration:.1f}s, {file_size} bytes)")
            return True
            
        except Exception as e:
            logger.error(f"Error validating video: {e}")
            return False
    
    def get_video_info(self, video_path: str) -> Optional[Dict]:
        """Get video information."""
        try:
            cmd = [
                "ffprobe", "-v", "quiet", "-print_format", "json",
                "-show_format", "-show_streams", video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                import json
                return json.loads(result.stdout)
            
        except Exception as e:
            logger.error(f"Error getting video info: {e}")
        
        return None
