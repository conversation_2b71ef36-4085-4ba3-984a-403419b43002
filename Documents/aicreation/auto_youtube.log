2025-05-19 22:31:51,152 - __main__ - WARNING - pytrends module not found. Trending keyword functionality will be limited.
2025-05-19 22:31:51,201 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 22:31:52,202 - __main__ - INFO - Available modules:
2025-05-19 22:31:52,202 - __main__ - INFO -   pytrends: Not available
2025-05-19 22:31:52,202 - __main__ - INFO -   ollama: Not available
2025-05-19 22:31:52,202 - __main__ - INFO -   gtts: Available
2025-05-19 22:31:52,202 - __main__ - INFO -   PIL: Available
2025-05-19 22:31:52,202 - __main__ - INFO -   google.oauth2: Available
2025-05-19 22:31:52,202 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 22:31:52,202 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 22:31:57,872 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 22:32:01,644 - __main__ - INFO - Created video for 'history facts'
2025-05-19 22:32:01,740 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 22:32:01,741 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 22:32:01,741 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 22:33:01,743 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 22:33:07,381 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 22:33:09,516 - __main__ - INFO - Created video for 'economic trends'
2025-05-19 22:33:09,558 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 22:33:09,558 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 22:33:09,558 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 22:34:09,563 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 22:34:15,310 - __main__ - INFO - Generated audio for 'current events'
2025-05-19 22:34:17,551 - __main__ - INFO - Created video for 'current events'
2025-05-19 22:34:17,592 - __main__ - INFO - Created thumbnail for 'current events'
2025-05-19 22:34:17,592 - __main__ - ERROR - Error processing keyword 'current events': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 22:34:17,592 - __main__ - WARNING - Failed to process 'current events'
2025-05-19 22:35:17,597 - __main__ - INFO - Completed processing all keywords. Waiting for 24 hours...
2025-05-19 22:35:42,983 - __main__ - INFO - Process interrupted by user. Exiting...
2025-05-19 23:00:22,812 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:00:23,474 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:00:24,274 - __main__ - INFO - Available modules:
2025-05-19 23:00:24,274 - __main__ - INFO -   pytrends: Available
2025-05-19 23:00:24,275 - __main__ - INFO -   ollama: Not available
2025-05-19 23:00:24,275 - __main__ - INFO -   gtts: Available
2025-05-19 23:00:24,275 - __main__ - INFO -   PIL: Available
2025-05-19 23:00:24,275 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:00:24,590 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:00:24,592 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:00:24,592 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:00:30,063 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:00:32,652 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:00:32,720 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:00:32,720 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:00:32,720 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:01:34,867 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:01:35,248 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:01:35,918 - __main__ - INFO - Available modules:
2025-05-19 23:01:35,918 - __main__ - INFO -   pytrends: Available
2025-05-19 23:01:35,918 - __main__ - INFO -   ollama: Not available
2025-05-19 23:01:35,918 - __main__ - INFO -   gtts: Available
2025-05-19 23:01:35,918 - __main__ - INFO -   PIL: Available
2025-05-19 23:01:35,918 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:01:36,236 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:01:36,239 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:01:36,239 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:01:41,027 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:01:41,910 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:01:41,955 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:01:41,956 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:01:41,956 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:01:41,956 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:02:42,007 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:02:42,341 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:02:43,076 - __main__ - INFO - Available modules:
2025-05-19 23:02:43,076 - __main__ - INFO -   pytrends: Available
2025-05-19 23:02:43,076 - __main__ - INFO -   ollama: Not available
2025-05-19 23:02:43,076 - __main__ - INFO -   gtts: Available
2025-05-19 23:02:43,076 - __main__ - INFO -   PIL: Available
2025-05-19 23:02:43,076 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:02:43,398 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:02:43,403 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:02:43,403 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:02:43,403 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:02:48,425 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:02:50,605 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:02:50,668 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:02:50,668 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:02:50,668 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:02:50,668 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:03:50,673 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:03:50,675 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:03:55,927 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 23:03:57,174 - __main__ - INFO - Created video for 'economic trends'
2025-05-19 23:03:57,210 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 23:03:57,210 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:03:57,210 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 23:03:57,210 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:04:57,216 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:04:57,217 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:05:02,504 - __main__ - INFO - Generated audio for 'current events'
2025-05-19 23:05:05,140 - __main__ - INFO - Created video for 'current events'
2025-05-19 23:05:05,182 - __main__ - INFO - Created thumbnail for 'current events'
2025-05-19 23:05:05,183 - __main__ - ERROR - Error processing keyword 'current events': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:05:05,183 - __main__ - WARNING - Failed to process 'current events'
2025-05-19 23:05:05,183 - __main__ - INFO - Completed processing all keywords.
2025-05-19 23:07:04,757 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:07:05,096 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:07:05,906 - __main__ - INFO - Available modules:
2025-05-19 23:07:05,907 - __main__ - INFO -   pytrends: Available
2025-05-19 23:07:05,907 - __main__ - INFO -   ollama: Not available
2025-05-19 23:07:05,907 - __main__ - INFO -   gtts: Available
2025-05-19 23:07:05,907 - __main__ - INFO -   PIL: Available
2025-05-19 23:07:05,907 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:07:06,222 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:07:06,228 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:07:06,229 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:07:06,229 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:07:10,882 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:07:15,174 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:07:15,247 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:07:15,247 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:07:15,247 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:07:15,247 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:09:02,681 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:09:03,046 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:09:03,833 - __main__ - INFO - Available modules:
2025-05-19 23:09:03,833 - __main__ - INFO -   pytrends: Available
2025-05-19 23:09:03,833 - __main__ - INFO -   ollama: Not available
2025-05-19 23:09:03,833 - __main__ - INFO -   gtts: Available
2025-05-19 23:09:03,833 - __main__ - INFO -   PIL: Available
2025-05-19 23:09:03,833 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:09:04,144 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:09:04,146 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:09:04,146 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:09:04,146 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:09:04,146 - __main__ - WARNING - Ollama not available. Using fallback script generation.
2025-05-19 23:09:08,741 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:09:10,301 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:09:10,366 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:09:10,367 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:09:10,367 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:09:10,367 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:09:57,955 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:09:58,315 - __main__ - WARNING - ollama module not found. Will try direct HTTP API instead.
2025-05-19 23:09:59,089 - __main__ - INFO - Available modules:
2025-05-19 23:09:59,089 - __main__ - INFO -   pytrends: Available
2025-05-19 23:09:59,089 - __main__ - INFO -   ollama: Available
2025-05-19 23:09:59,089 - __main__ - INFO -   gtts: Available
2025-05-19 23:09:59,090 - __main__ - INFO -   PIL: Available
2025-05-19 23:09:59,090 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:09:59,406 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:09:59,409 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:09:59,409 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:09:59,409 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:09:59,409 - __main__ - INFO - Generating script with Ollama model 'phi3'
2025-05-19 23:09:59,409 - __main__ - INFO - Using direct HTTP API call to Ollama
2025-05-19 23:10:10,407 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:10:10,437 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:10:10,441 - __main__ - WARNING - Ollama not available. Using fallback script generation.
2025-05-19 23:10:29,648 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 23:10:47,883 - __main__ - INFO - Created video for 'economic trends'
2025-05-19 23:10:48,014 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 23:10:48,020 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:10:48,028 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 23:10:48,028 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:11:22,378 - __main__ - ERROR - Ollama API error: Extra data: line 2 column 1 (char 120)
2025-05-19 23:11:22,384 - __main__ - ERROR - Error generating script for 'history facts': Extra data: line 2 column 1 (char 120)
2025-05-19 23:11:27,724 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:11:29,463 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:11:29,564 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:11:29,564 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:11:29,564 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:11:29,564 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:11:48,031 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:11:48,034 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:11:48,034 - __main__ - WARNING - Ollama not available. Using fallback script generation.
2025-05-19 23:11:53,376 - __main__ - INFO - Generated audio for 'current events'
2025-05-19 23:11:55,246 - __main__ - INFO - Created video for 'current events'
2025-05-19 23:11:55,283 - __main__ - INFO - Created thumbnail for 'current events'
2025-05-19 23:11:55,284 - __main__ - ERROR - Error processing keyword 'current events': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:11:55,284 - __main__ - WARNING - Failed to process 'current events'
2025-05-19 23:11:55,284 - __main__ - INFO - Completed processing all keywords.
2025-05-19 23:12:29,570 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:12:29,574 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:12:29,574 - __main__ - INFO - Generating script with Ollama model 'phi3'
2025-05-19 23:12:29,574 - __main__ - INFO - Using direct HTTP API call to Ollama
2025-05-19 23:13:20,441 - __main__ - ERROR - Ollama API error: Extra data: line 2 column 1 (char 120)
2025-05-19 23:13:20,444 - __main__ - ERROR - Error generating script for 'economic trends': Extra data: line 2 column 1 (char 120)
2025-05-19 23:13:25,034 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 23:13:27,059 - __main__ - INFO - Created video for 'economic trends'
2025-05-19 23:13:27,102 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 23:13:27,103 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:13:27,103 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 23:13:27,103 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:14:49,408 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:14:49,715 - __main__ - WARNING - ollama module not found. Script generation will use fallback methods.
2025-05-19 23:14:50,518 - __main__ - INFO - Available modules:
2025-05-19 23:14:50,519 - __main__ - INFO -   pytrends: Available
2025-05-19 23:14:50,519 - __main__ - INFO -   ollama: Not available
2025-05-19 23:14:50,519 - __main__ - INFO -   gtts: Available
2025-05-19 23:14:50,519 - __main__ - INFO -   PIL: Available
2025-05-19 23:14:50,519 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:14:50,838 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:14:50,841 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:14:50,841 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:14:50,841 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:14:50,841 - __main__ - WARNING - Ollama not available. Using fallback script generation.
2025-05-19 23:14:55,336 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:14:57,231 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:14:57,287 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:14:57,287 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:14:57,287 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:14:57,287 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:20:08,966 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:20:09,332 - __main__ - INFO - Ollama command line is available
2025-05-19 23:20:10,063 - __main__ - INFO - Available modules:
2025-05-19 23:20:10,063 - __main__ - INFO -   pytrends: Available
2025-05-19 23:20:10,063 - __main__ - INFO -   ollama: Available
2025-05-19 23:20:10,064 - __main__ - INFO -   gtts: Available
2025-05-19 23:20:10,064 - __main__ - INFO -   PIL: Available
2025-05-19 23:20:10,064 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:20:10,385 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:20:10,387 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:20:10,388 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:20:10,388 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:20:10,388 - __main__ - INFO - Generating script with Ollama model 'llama3.2'
2025-05-19 23:20:10,388 - __main__ - INFO - Calling Ollama command line with model 'llama3.2'
2025-05-19 23:22:19,340 - __main__ - INFO - Received response from Ollama
2025-05-19 23:22:19,364 - __main__ - INFO - Extracted JSON from generic code block
2025-05-19 23:22:19,378 - __main__ - ERROR - Failed to parse JSON: Expecting ',' delimiter: line 10 column 22 (char 409)
2025-05-19 23:22:19,382 - __main__ - ERROR - Raw JSON string: {
    "title": "Bizarre History Facts That Will Blow Your Mind",
    "introduction": {
        "star...
2025-05-19 23:22:19,383 - __main__ - ERROR - Ollama API error: Expecting ',' delimiter: line 10 column 22 (char 409)
2025-05-19 23:22:19,385 - __main__ - ERROR - Error generating script for 'history facts': Expecting ',' delimiter: line 10 column 22 (char 409)
2025-05-19 23:22:24,562 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:22:26,664 - __main__ - INFO - Created video for 'history facts'
2025-05-19 23:22:26,773 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:22:26,773 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:22:26,773 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:22:26,773 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:23:26,779 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:23:26,780 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:23:26,780 - __main__ - INFO - Generating script with Ollama model 'llama3.2'
2025-05-19 23:23:26,780 - __main__ - INFO - Calling Ollama command line with model 'llama3.2'
2025-05-19 23:24:21,355 - __main__ - INFO - Received response from Ollama
2025-05-19 23:24:21,358 - __main__ - INFO - Extracted JSON from generic code block
2025-05-19 23:24:21,371 - __main__ - INFO - Generated script for 'economic trends' using Ollama
2025-05-19 23:24:54,075 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 23:24:54,312 - __main__ - ERROR - FFmpeg error: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers
  built with clang version 12.0.0
  configuration: --prefix=/Users/<USER>/demo/mc3/conda-bld/ffmpeg_1628925491858/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac --cc=arm64-apple-darwin20.0.0-clang --disable-doc --enable-avresample --enable-gmp --enable-hardcoded-tables --enable-libfreetype --enable-libvpx --enable-pthreads --enable-libopus --enable-postproc --enable-pic --enable-pthreads --enable-shared --enable-static --enable-version3 --enable-zlib --enable-libmp3lame --disable-nonfree --enable-gpl --enable-gnutls --disable-openssl --enable-libopenh264 --enable-libx264
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
Input #0, lavfi, from 'color=c=gray:s=1280x720:d=180':
  Duration: N/A, start: 0.000000, bitrate: N/A
    Stream #0:0: Video: rawvideo (I420 / 0x30323449), yuv420p, 1280x720 [SAR 1:1 DAR 16:9], 25 tbr, 25 tbn, 25 tbc
[mp3 @ 0x15200ba00] Estimating duration from bitrate, this may be inaccurate
Input #1, mp3, from 'output/audio/economic_trends.mp3':
  Duration: 00:02:23.16, start: 0.000000, bitrate: 64 kb/s
    Stream #1:0: Audio: mp3, 24000 Hz, mono, fltp, 64 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (rawvideo (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (mp3 (mp3float) -> aac (native))
Press [q] to stop, [?] for help
[Parsed_drawtext_0 @ 0x131604190] Could not load font "What You Need to Know": cannot open resource
[AVFilterGraph @ 0x151719b30] Error initializing filter 'drawtext' with args 'fontfile=/System/Library/Fonts/Helvetica.ttc:fontsize=24:fontcolor=white:x=(w-text_w)/2:y=h/4:text=Navigating Economic Trends: What You Need to Know:box=1:boxcolor=black@0.5:boxborderw=5'
Error reinitializing filters!
Failed to inject frame into filter network: Invalid argument
Error while processing the decoded data for stream #0:0
Conversion failed!

2025-05-19 23:25:01,899 - __main__ - INFO - Created simple video for 'economic trends'
2025-05-19 23:25:01,945 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 23:25:01,945 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:25:01,945 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 23:25:01,945 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:26:01,951 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:26:01,952 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:26:01,953 - __main__ - INFO - Generating script with Ollama model 'llama3.2'
2025-05-19 23:26:01,953 - __main__ - INFO - Calling Ollama command line with model 'llama3.2'
2025-05-19 23:27:29,619 - __main__ - INFO - Received response from Ollama
2025-05-19 23:27:29,747 - __main__ - INFO - Extracted JSON from generic code block
2025-05-19 23:27:29,841 - __main__ - INFO - Generated script for 'current events' using Ollama
2025-05-19 23:28:11,064 - __main__ - INFO - Generated audio for 'current events'
2025-05-19 23:28:11,301 - __main__ - ERROR - FFmpeg error: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers
  built with clang version 12.0.0
  configuration: --prefix=/Users/<USER>/demo/mc3/conda-bld/ffmpeg_1628925491858/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac --cc=arm64-apple-darwin20.0.0-clang --disable-doc --enable-avresample --enable-gmp --enable-hardcoded-tables --enable-libfreetype --enable-libvpx --enable-pthreads --enable-libopus --enable-postproc --enable-pic --enable-pthreads --enable-shared --enable-static --enable-version3 --enable-zlib --enable-libmp3lame --disable-nonfree --enable-gpl --enable-gnutls --disable-openssl --enable-libopenh264 --enable-libx264
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
Input #0, lavfi, from 'color=c=gray:s=1280x720:d=180':
  Duration: N/A, start: 0.000000, bitrate: N/A
    Stream #0:0: Video: rawvideo (I420 / 0x30323449), yuv420p, 1280x720 [SAR 1:1 DAR 16:9], 25 tbr, 25 tbn, 25 tbc
[mp3 @ 0x146009a00] Estimating duration from bitrate, this may be inaccurate
Input #1, mp3, from 'output/audio/current_events.mp3':
  Duration: 00:03:14.16, start: 0.000000, bitrate: 64 kb/s
    Stream #1:0: Audio: mp3, 24000 Hz, mono, fltp, 64 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (rawvideo (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (mp3 (mp3float) -> aac (native))
Press [q] to stop, [?] for help
[Parsed_drawtext_0 @ 0x1470051e0] Could not load font "The World in Turmoil": cannot open resource
[AVFilterGraph @ 0x147004080] Error initializing filter 'drawtext' with args 'fontfile=/System/Library/Fonts/Helvetica.ttc:fontsize=24:fontcolor=white:x=(w-text_w)/2:y=h/4:text=Breaking News: The World in Turmoil:box=1:boxcolor=black@0.5:boxborderw=5'
Error reinitializing filters!
Failed to inject frame into filter network: Invalid argument
Error while processing the decoded data for stream #0:0
Conversion failed!

2025-05-19 23:28:21,099 - __main__ - INFO - Created simple video for 'current events'
2025-05-19 23:28:21,157 - __main__ - INFO - Created thumbnail for 'current events'
2025-05-19 23:28:21,157 - __main__ - ERROR - Error processing keyword 'current events': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:28:21,158 - __main__ - WARNING - Failed to process 'current events'
2025-05-19 23:28:21,158 - __main__ - INFO - Completed processing all keywords.
2025-05-19 23:33:10,634 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:33:11,021 - __main__ - INFO - Ollama command line is available
2025-05-19 23:33:11,872 - __main__ - INFO - Available modules:
2025-05-19 23:33:11,873 - __main__ - INFO -   pytrends: Available
2025-05-19 23:33:11,873 - __main__ - INFO -   ollama: Available
2025-05-19 23:33:11,873 - __main__ - INFO -   gtts: Available
2025-05-19 23:33:11,873 - __main__ - INFO -   PIL: Available
2025-05-19 23:33:11,873 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:33:12,196 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:33:12,198 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:33:12,199 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:33:12,199 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:33:12,200 - __main__ - INFO - Generating script with Ollama model 'phi3:medium'
2025-05-19 23:33:12,200 - __main__ - INFO - Calling Ollama command line with model 'phi3:medium'
2025-05-19 23:41:19,240 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-05-19 23:41:19,671 - __main__ - INFO - Ollama command line is available
2025-05-19 23:41:20,548 - __main__ - INFO - Available modules:
2025-05-19 23:41:20,548 - __main__ - INFO -   pytrends: Available
2025-05-19 23:41:20,548 - __main__ - INFO -   ollama: Available
2025-05-19 23:41:20,548 - __main__ - INFO -   gtts: Available
2025-05-19 23:41:20,548 - __main__ - INFO -   PIL: Available
2025-05-19 23:41:20,548 - __main__ - INFO -   google.oauth2: Available
2025-05-19 23:41:20,862 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-05-19 23:41:20,863 - __main__ - INFO - Found 3 trending keywords: ['history facts', 'economic trends', 'current events']
2025-05-19 23:41:20,863 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:41:20,863 - __main__ - INFO - Processing keyword: 'history facts'
2025-05-19 23:41:20,863 - __main__ - INFO - Generating script with Ollama model 'phi3'
2025-05-19 23:41:20,863 - __main__ - INFO - Calling Ollama command line with model 'phi3'
2025-05-19 23:42:16,676 - __main__ - INFO - Received response from Ollama
2025-05-19 23:42:16,682 - __main__ - INFO - Extracted JSON from code block with json tag
2025-05-19 23:42:16,689 - __main__ - WARNING - First JSON parsing attempt failed: Expecting ',' delimiter: line 5 column 142 (char 176)
2025-05-19 23:42:16,702 - __main__ - INFO - Generated script for 'history facts' using Ollama
2025-05-19 23:42:23,484 - __main__ - INFO - Generated audio for 'history facts'
2025-05-19 23:42:24,112 - __main__ - ERROR - FFmpeg error: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers
  built with clang version 12.0.0
  configuration: --prefix=/Users/<USER>/demo/mc3/conda-bld/ffmpeg_1628925491858/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac --cc=arm64-apple-darwin20.0.0-clang --disable-doc --enable-avresample --enable-gmp --enable-hardcoded-tables --enable-libfreetype --enable-libvpx --enable-pthreads --enable-libopus --enable-postproc --enable-pic --enable-pthreads --enable-shared --enable-static --enable-version3 --enable-zlib --enable-libmp3lame --disable-nonfree --enable-gpl --enable-gnutls --disable-openssl --enable-libopenh264 --enable-libx264
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
Input #0, lavfi, from 'color=c=gray:s=1280x720:d=180':
  Duration: N/A, start: 0.000000, bitrate: N/A
    Stream #0:0: Video: rawvideo (I420 / 0x30323449), yuv420p, 1280x720 [SAR 1:1 DAR 16:9], 25 tbr, 25 tbn, 25 tbc
[mp3 @ 0x13280a800] Estimating duration from bitrate, this may be inaccurate
Input #1, mp3, from 'output/audio/history_facts.mp3':
  Duration: 00:00:27.00, start: 0.000000, bitrate: 64 kb/s
    Stream #1:0: Audio: mp3, 24000 Hz, mono, fltp, 64 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (rawvideo (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (mp3 (mp3float) -> aac (native))
Press [q] to stop, [?] for help
[Parsed_drawtext_0 @ 0x1106040a0] No font filename provided
[AVFilterGraph @ 0x130605bc0] Error initializing filter 'drawtext' with args 'fontsize=40:fontcolor=white:x=(w-text_w)/2:y=h/3:text=Era of Empires:box=1:boxcolor=black@0.5:boxborderw=5'
Error reinitializing filters!
Failed to inject frame into filter network: Invalid argument
Error while processing the decoded data for stream #0:0
Conversion failed!

2025-05-19 23:42:26,293 - __main__ - INFO - Created simple video for 'history facts'
2025-05-19 23:42:26,423 - __main__ - INFO - Created thumbnail for 'history facts'
2025-05-19 23:42:26,423 - __main__ - ERROR - Error processing keyword 'history facts': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:42:26,423 - __main__ - WARNING - Failed to process 'history facts'
2025-05-19 23:42:26,423 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:43:26,424 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:43:26,426 - __main__ - INFO - Processing keyword: 'economic trends'
2025-05-19 23:43:26,427 - __main__ - INFO - Generating script with Ollama model 'phi3'
2025-05-19 23:43:26,427 - __main__ - INFO - Calling Ollama command line with model 'phi3'
2025-05-19 23:44:19,493 - __main__ - INFO - Received response from Ollama
2025-05-19 23:44:19,510 - __main__ - INFO - Extracted JSON from code block with json tag
2025-05-19 23:44:19,520 - __main__ - WARNING - First JSON parsing attempt failed: Expecting ',' delimiter: line 15 column 58 (char 451)
2025-05-19 23:44:19,634 - __main__ - INFO - Generated script for 'economic trends' using Ollama
2025-05-19 23:44:30,631 - __main__ - INFO - Generated audio for 'economic trends'
2025-05-19 23:44:30,726 - __main__ - ERROR - FFmpeg error: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers
  built with clang version 12.0.0
  configuration: --prefix=/Users/<USER>/demo/mc3/conda-bld/ffmpeg_1628925491858/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac --cc=arm64-apple-darwin20.0.0-clang --disable-doc --enable-avresample --enable-gmp --enable-hardcoded-tables --enable-libfreetype --enable-libvpx --enable-pthreads --enable-libopus --enable-postproc --enable-pic --enable-pthreads --enable-shared --enable-static --enable-version3 --enable-zlib --enable-libmp3lame --disable-nonfree --enable-gpl --enable-gnutls --disable-openssl --enable-libopenh264 --enable-libx264
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
Input #0, lavfi, from 'color=c=gray:s=1280x720:d=180':
  Duration: N/A, start: 0.000000, bitrate: N/A
    Stream #0:0: Video: rawvideo (I420 / 0x30323449), yuv420p, 1280x720 [SAR 1:1 DAR 16:9], 25 tbr, 25 tbn, 25 tbc
[mp3 @ 0x139008e00] Estimating duration from bitrate, this may be inaccurate
Input #1, mp3, from 'output/audio/economic_trends.mp3':
  Duration: 00:00:38.64, start: 0.000000, bitrate: 64 kb/s
    Stream #1:0: Audio: mp3, 24000 Hz, mono, fltp, 64 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (rawvideo (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (mp3 (mp3float) -> aac (native))
Press [q] to stop, [?] for help
[Parsed_drawtext_0 @ 0x107604190] No font filename provided
[AVFilterGraph @ 0x137605850] Error initializing filter 'drawtext' with args 'fontsize=40:fontcolor=white:x=(w-text_w)/2:y=h/3:text=Navigating Through Economic Waves:box=1:boxcolor=black@0.5:boxborderw=5'
Error reinitializing filters!
Failed to inject frame into filter network: Invalid argument
Error while processing the decoded data for stream #0:0
Conversion failed!

2025-05-19 23:44:34,941 - __main__ - INFO - Created simple video for 'economic trends'
2025-05-19 23:44:35,003 - __main__ - INFO - Created thumbnail for 'economic trends'
2025-05-19 23:44:35,003 - __main__ - ERROR - Error processing keyword 'economic trends': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:44:35,003 - __main__ - WARNING - Failed to process 'economic trends'
2025-05-19 23:44:35,004 - __main__ - INFO - Waiting 60 seconds before processing next keyword...
2025-05-19 23:45:35,008 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:45:35,011 - __main__ - INFO - Processing keyword: 'current events'
2025-05-19 23:45:35,011 - __main__ - INFO - Generating script with Ollama model 'phi3'
2025-05-19 23:45:35,011 - __main__ - INFO - Calling Ollama command line with model 'phi3'
2025-05-19 23:46:46,980 - __main__ - INFO - Received response from Ollama
2025-05-19 23:46:46,987 - __main__ - INFO - Extracted JSON from code block with json tag
2025-05-19 23:46:47,001 - __main__ - WARNING - First JSON parsing attempt failed: Expecting ',' delimiter: line 7 column 55 (char 137)
2025-05-19 23:46:47,022 - __main__ - INFO - Generated script for 'current events' using Ollama
2025-05-19 23:46:52,712 - __main__ - INFO - Generated audio for 'current events'
2025-05-19 23:46:52,835 - __main__ - ERROR - FFmpeg error: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers
  built with clang version 12.0.0
  configuration: --prefix=/Users/<USER>/demo/mc3/conda-bld/ffmpeg_1628925491858/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac --cc=arm64-apple-darwin20.0.0-clang --disable-doc --enable-avresample --enable-gmp --enable-hardcoded-tables --enable-libfreetype --enable-libvpx --enable-pthreads --enable-libopus --enable-postproc --enable-pic --enable-pthreads --enable-shared --enable-static --enable-version3 --enable-zlib --enable-libmp3lame --disable-nonfree --enable-gpl --enable-gnutls --disable-openssl --enable-libopenh264 --enable-libx264
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
Input #0, lavfi, from 'color=c=gray:s=1280x720:d=180':
  Duration: N/A, start: 0.000000, bitrate: N/A
    Stream #0:0: Video: rawvideo (I420 / 0x30323449), yuv420p, 1280x720 [SAR 1:1 DAR 16:9], 25 tbr, 25 tbn, 25 tbc
[mp3 @ 0x130008200] Estimating duration from bitrate, this may be inaccurate
Input #1, mp3, from 'output/audio/current_events.mp3':
  Duration: 00:00:20.64, start: 0.000000, bitrate: 64 kb/s
    Stream #1:0: Audio: mp3, 24000 Hz, mono, fltp, 64 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (rawvideo (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (mp3 (mp3float) -> aac (native))
Press [q] to stop, [?] for help
[Parsed_drawtext_0 @ 0x12701f1a0] No font filename provided
[AVFilterGraph @ 0x126606070] Error initializing filter 'drawtext' with args 'fontsize=40:fontcolor=white:x=(w-text_w)/2:y=h/3:text=Global Climate Action Amidst Current Events:box=1:boxcolor=black@0.5:boxborderw=5'
Error reinitializing filters!
Failed to inject frame into filter network: Invalid argument
Error while processing the decoded data for stream #0:0
Conversion failed!

2025-05-19 23:46:56,087 - __main__ - INFO - Created simple video for 'current events'
2025-05-19 23:46:56,303 - __main__ - INFO - Created thumbnail for 'current events'
2025-05-19 23:46:56,311 - __main__ - ERROR - Error processing keyword 'current events': [Errno 2] No such file or directory: 'client_secrets.json'
2025-05-19 23:46:56,312 - __main__ - WARNING - Failed to process 'current events'
2025-05-19 23:46:56,312 - __main__ - INFO - Completed processing all keywords.
