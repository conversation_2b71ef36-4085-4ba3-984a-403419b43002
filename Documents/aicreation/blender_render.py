#!/usr/bin/env python3
"""
blender_render.py - Optimized Blender script for professional video generation
This script is designed to be executed by Blender in background mode.

Usage:
blender -b -P blender_render.py -- --title "Title" --script "script.json" --audio "audio.mp3" --output "video.mp4"
"""

import bpy
import json
import os
import sys
import math
import argparse
import random
import time
from mathutils import Vector, Euler

# Fonction pour analyser les arguments de ligne de commande
def get_args():
    parser = argparse.ArgumentParser()
    
    # Récupérer tous les arguments après "--"
    if "--" not in sys.argv:
        args = []
    else:
        args = sys.argv[sys.argv.index("--") + 1:]
    
    parser.add_argument("--title", type=str, required=True, help="Titre de la vidéo")
    parser.add_argument("--script", type=str, required=True, help="Chemin vers le fichier JSON du script")
    parser.add_argument("--audio", type=str, required=True, help="Chemin vers le fichier audio MP3")
    parser.add_argument("--output", type=str, required=True, help="Chemin de sortie pour la vidéo MP4")
    
    return parser.parse_args(args)

# Fonction pour nettoyer la scène par défaut
def clean_scene():
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete()
    
    # Supprimer toutes les collections sauf la collection Master
    for collection in bpy.data.collections:
        bpy.data.collections.remove(collection)
    
    # Supprimer toutes les caméras, lumières et matériaux
    for camera in bpy.data.cameras:
        bpy.data.cameras.remove(camera)
    
    for light in bpy.data.lights:
        bpy.data.lights.remove(light)
    
    for material in bpy.data.materials:
        bpy.data.materials.remove(material)

# Fonction pour configurer la scène
def setup_scene(title):
    # Configurer le rendu
    bpy.context.scene.render.engine = 'CYCLES'
    bpy.context.scene.render.film_transparent = True
    bpy.context.scene.render.resolution_x = 1920
    bpy.context.scene.render.resolution_y = 1080
    bpy.context.scene.render.resolution_percentage = 100
    bpy.context.scene.render.fps = 30
    
    # Configurer la durée de la scène (3 minutes = 180 secondes)
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 30 * 180  # 30 fps * 180 secondes
    
    # Ajouter une caméra
    bpy.ops.object.camera_add(location=(0, -10, 2), rotation=(math.radians(80), 0, 0))
    camera = bpy.context.active_object
    bpy.context.scene.camera = camera
    
    # Ajouter un éclairage principal
    bpy.ops.object.light_add(type='SUN', location=(5, -5, 10))
    sun = bpy.context.active_object
    sun.data.energy = 2.0
    
    # Ajouter un éclairage d'ambiance
    bpy.ops.object.light_add(type='AREA', location=(-5, -5, 5))
    area = bpy.context.active_object
    area.data.energy = 1.0
    
    # Créer un fond
    bpy.ops.mesh.primitive_plane_add(size=40, location=(0, 0, -1))
    background = bpy.context.active_object
    
    # Créer un matériau pour le fond
    mat = bpy.data.materials.new(name="Background_Material")
    mat.use_nodes = True
    nodes = mat.node_tree.nodes
    links = mat.node_tree.links
    
    # Nettoyer les nœuds existants
    for node in nodes:
        nodes.remove(node)
    
    # Créer un nœud de sortie
    output = nodes.new(type='ShaderNodeOutputMaterial')
    output.location = (300, 0)
    
    # Créer un nœud de shader diffus
    diffuse = nodes.new(type='ShaderNodeBsdfDiffuse')
    diffuse.location = (0, 0)
    diffuse.inputs[0].default_value = (0.05, 0.05, 0.2, 1.0)  # Bleu foncé
    
    # Connecter les nœuds
    links.new(diffuse.outputs[0], output.inputs[0])
    
    # Assigner le matériau à l'objet
    background.data.materials.append(mat)
    
    # Créer un texte 3D pour le titre
    bpy.ops.object.text_add(location=(0, 0, 0))
    text_obj = bpy.context.active_object
    text_obj.data.body = title
    text_obj.data.align_x = 'CENTER'
    text_obj.data.size = 1.0
    
    # Centrer le texte
    bpy.ops.object.origin_set(type='ORIGIN_CENTER_OF_MASS')
    text_obj.location = (0, 0, 0)
    
    # Créer un matériau pour le texte
    text_mat = bpy.data.materials.new(name="Text_Material")
    text_mat.use_nodes = True
    text_nodes = text_mat.node_tree.nodes
    text_links = text_mat.node_tree.links
    
    # Nettoyer les nœuds existants
    for node in text_nodes:
        text_nodes.remove(node)
    
    # Créer un nœud de sortie
    text_output = text_nodes.new(type='ShaderNodeOutputMaterial')
    text_output.location = (300, 0)
    
    # Créer un nœud de shader émissif
    emission = text_nodes.new(type='ShaderNodeEmission')
    emission.location = (0, 0)
    emission.inputs[0].default_value = (1.0, 0.8, 0.2, 1.0)  # Or
    emission.inputs[1].default_value = 2.0  # Force
    
    # Connecter les nœuds
    text_links.new(emission.outputs[0], text_output.inputs[0])
    
    # Assigner le matériau au texte
    text_obj.data.materials.append(text_mat)
    
    # Animer le texte (apparition et disparition)
    text_obj.scale = (0, 0, 0)
    text_obj.keyframe_insert(data_path="scale", frame=1)
    
    text_obj.scale = (1, 1, 1)
    text_obj.keyframe_insert(data_path="scale", frame=30)
    
    text_obj.scale = (1, 1, 1)
    text_obj.keyframe_insert(data_path="scale", frame=150)
    
    text_obj.scale = (0, 0, 0)
    text_obj.keyframe_insert(data_path="scale", frame=180)
    
    return text_obj

# Fonction pour créer des animations basées sur le script
def create_animations(script_path, text_obj):
    # Charger le script JSON
    with open(script_path, 'r') as f:
        script = json.load(f)
    
    # Créer une collection pour les animations
    animation_collection = bpy.data.collections.new("Animations")
    bpy.context.scene.collection.children.link(animation_collection)
    
    # Fonction pour créer un objet 3D avec animation
    def create_animated_object(text, start_frame, end_frame, location):
        # Créer un texte 3D
        bpy.ops.object.text_add(location=location)
        obj = bpy.context.active_object
        obj.data.body = text
        obj.data.size = 0.5
        
        # Ajouter à la collection
        bpy.ops.object.select_all(action='DESELECT')
        obj.select_set(True)
        bpy.ops.collection.objects_remove_all()
        animation_collection.objects.link(obj)
        
        # Créer un matériau
        mat = bpy.data.materials.new(name=f"Material_{text[:10]}")
        mat.use_nodes = True
        nodes = mat.node_tree.nodes
        links = mat.node_tree.links
        
        # Nettoyer les nœuds existants
        for node in nodes:
            nodes.remove(node)
        
        # Créer un nœud de sortie
        output = nodes.new(type='ShaderNodeOutputMaterial')
        output.location = (300, 0)
        
        # Créer un nœud de shader émissif
        emission = nodes.new(type='ShaderNodeEmission')
        emission.location = (0, 0)
        
        # Couleur aléatoire claire
        r = random.uniform(0.5, 1.0)
        g = random.uniform(0.5, 1.0)
        b = random.uniform(0.5, 1.0)
        emission.inputs[0].default_value = (r, g, b, 1.0)
        emission.inputs[1].default_value = 1.5  # Force
        
        # Connecter les nœuds
        links.new(emission.outputs[0], output.inputs[0])
        
        # Assigner le matériau
        obj.data.materials.append(mat)
        
        # Animer l'apparition
        obj.scale = (0, 0, 0)
        obj.keyframe_insert(data_path="scale", frame=start_frame)
        
        obj.scale = (1, 1, 1)
        obj.keyframe_insert(data_path="scale", frame=start_frame + 15)
        
        # Animer la disparition
        obj.scale = (1, 1, 1)
        obj.keyframe_insert(data_path="scale", frame=end_frame - 15)
        
        obj.scale = (0, 0, 0)
        obj.keyframe_insert(data_path="scale", frame=end_frame)
        
        return obj
    
    # Masquer le titre après l'introduction
    text_obj.keyframe_insert(data_path="hide_render", frame=1)
    text_obj.hide_render = False
    text_obj.keyframe_insert(data_path="hide_render", frame=180)
    text_obj.hide_render = True
    text_obj.keyframe_insert(data_path="hide_render", frame=181)
    
    # Créer des animations pour l'introduction
    intro_text = script.get("introduction", "Introduction")
    if isinstance(intro_text, dict) and "text" in intro_text:
        intro_text = intro_text["text"]
    
    create_animated_object(
        intro_text[:100] + "...",
        180,
        30 * 30,  # 30 secondes
        (0, 0, 0)
    )
    
    # Créer des animations pour les points clés
    key_points = []
    for i in range(1, 4):
        key = f"key_point_{i}"
        if key in script:
            point_text = script[key]
            if isinstance(point_text, dict) and "text" in point_text:
                point_text = point_text["text"]
            key_points.append(point_text)
    
    # Positionner les points clés
    for i, point in enumerate(key_points):
        start_frame = 30 * 30 + i * 30 * 40  # 30 secondes pour l'intro + 40 secondes par point
        end_frame = start_frame + 30 * 40
        
        y_pos = 0
        z_pos = 0
        
        create_animated_object(
            point[:100] + "...",
            start_frame,
            end_frame,
            (0, y_pos, z_pos)
        )
    
    # Créer une animation pour la conclusion
    conclusion_text = script.get("conclusion", "Conclusion")
    if isinstance(conclusion_text, dict) and "text" in conclusion_text:
        conclusion_text = conclusion_text["text"]
    
    create_animated_object(
        conclusion_text[:100] + "...",
        30 * 150,  # 2:30
        30 * 180,  # 3:00
        (0, 0, 0)
    )

# Fonction pour ajouter l'audio
def add_audio(audio_path):
    # Configurer l'éditeur de séquence
    bpy.context.scene.sequence_editor_create()
    
    # Ajouter la piste audio
    audio_strip = bpy.context.scene.sequence_editor.sequences.new_sound(
        name="Audio",
        filepath=audio_path,
        channel=1,
        frame_start=1
    )
    
    # Ajuster la durée de la scène à la durée de l'audio si nécessaire
    audio_duration_frames = audio_strip.frame_final_duration
    if audio_duration_frames > bpy.context.scene.frame_end:
        bpy.context.scene.frame_end = audio_duration_frames

# Fonction principale
def main():
    # Récupérer les arguments
    args = get_args()
    
    # Nettoyer la scène
    clean_scene()
    
    # Configurer la scène
    text_obj = setup_scene(args.title)
    
    # Créer les animations
    create_animations(args.script, text_obj)
    
    # Ajouter l'audio
    add_audio(args.audio)
    
    # Configurer la sortie
    bpy.context.scene.render.filepath = args.output
    bpy.context.scene.render.image_settings.file_format = 'FFMPEG'
    bpy.context.scene.render.ffmpeg.format = 'MPEG4'
    bpy.context.scene.render.ffmpeg.codec = 'H264'
    bpy.context.scene.render.ffmpeg.constant_rate_factor = 'MEDIUM'
    bpy.context.scene.render.ffmpeg.ffmpeg_preset = 'GOOD'
    
    # Rendre l'animation
    bpy.ops.render.render(animation=True)

if __name__ == "__main__":
    main()
