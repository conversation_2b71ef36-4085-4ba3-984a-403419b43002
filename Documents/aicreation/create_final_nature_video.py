#!/usr/bin/env python3
"""
create_final_nature_video.py - Créer la vidéo nature finale avec Pixabay et musique
"""

import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_final_nature_video():
    """Create the final nature video with Pixabay background and music."""
    print("🌿" * 35)
    print("🎬 CRÉATION VIDÉO NATURE FINALE AVEC PIXABAY 🎬")
    print("🌿" * 35)
    print()
    print("✨ Cette vidéo contiendra :")
    print("   🌿 Arrière-plan: Magnifiques vidéos nature HD depuis Pixabay")
    print("   🎵 Audio: Narration IA sur la nature")
    print("   🎶 Musique: Fréquences apaisantes sans percussion")
    print("   🎯 Durée: Adaptée à la narration (~1m30)")
    print()
    
    try:
        from simple_nature_composer import SimpleNatureComposer
        
        # Initialize composer
        composer = SimpleNatureComposer()
        
        # Check if we have the audio
        audio_path = "output/audio/nature.mp3"
        
        if not Path(audio_path).exists():
            print(f"❌ Audio non trouvé: {audio_path}")
            print("💡 L'audio sera créé automatiquement...")
            
            # Create audio first
            from enhanced_auto_youtube import EnhancedAutoYouTube
            auto_youtube = EnhancedAutoYouTube()
            
            print("🔄 Génération du script et audio...")
            success = auto_youtube.process_keyword("nature")
            
            if not success or not Path(audio_path).exists():
                print("❌ Échec de création de l'audio")
                return
        
        print(f"✅ Audio trouvé: {audio_path}")
        
        # Get audio info
        audio_size = Path(audio_path).stat().st_size / (1024 * 1024)
        print(f"📊 Taille audio: {audio_size:.1f} MB")
        
        print()
        print("🎬 Création de la vidéo finale avec arrière-plan nature...")
        print("⏳ Téléchargement des vidéos Pixabay et composition...")
        print("   (Cela peut prendre 2-3 minutes)")
        
        # Create the final video
        video_path = composer.create_nature_video(audio_path, "nature_final", add_music=True)
        
        if video_path and Path(video_path).exists():
            video_size = Path(video_path).stat().st_size / (1024 * 1024)
            
            print()
            print("🎉 VIDÉO NATURE FINALE CRÉÉE AVEC SUCCÈS!")
            print("=" * 50)
            print(f"📁 Fichier: {video_path}")
            print(f"📊 Taille: {video_size:.1f} MB")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
            print()
            print("🌿 CONTENU DE LA VIDÉO:")
            print("   ✅ Arrière-plan: Vidéos nature HD de Pixabay")
            print("   ✅ Audio: Narration IA sur la nature")
            print("   ✅ Musique: Fréquences apaisantes (741Hz, 852Hz, 963Hz)")
            print("   ✅ Qualité: HD 1920x1080")
            print("   ✅ Format: MP4 optimisé pour YouTube")
            print()
            print("🎵 CARACTÉRISTIQUES MUSICALES:")
            print("   • Style: Prairie paisible")
            print("   • Fréquences d'éveil et de conscience")
            print("   • Volume doux pour ne pas couvrir la narration")
            print("   • Aucune percussion - Pure harmonie")
            print()
            print("🚀 Votre vidéo est prête pour YouTube!")
            
            # Show other generated files
            print("\n📁 AUTRES FICHIERS GÉNÉRÉS:")
            
            script_path = Path("output/scripts/nature.json")
            if script_path.exists():
                print(f"📝 Script: {script_path}")
            
            thumbnail_path = Path("output/thumbnails/nature.jpg")
            if thumbnail_path.exists():
                thumbnail_size = thumbnail_path.stat().st_size / 1024
                print(f"🖼️ Miniature: {thumbnail_path} ({thumbnail_size:.1f} KB)")
            
            transcript_path = Path("output/transcripts/nature.txt")
            if transcript_path.exists():
                print(f"📄 Transcription: {transcript_path}")
            
        else:
            print("❌ Erreur lors de la création de la vidéo")
            print("🔧 Vérifiez les logs pour plus de détails")
            
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        print("💡 Assurez-vous que tous les modules sont installés")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("🔧 Vérifiez la configuration Pixabay et les logs")

def show_pixabay_videos():
    """Show downloaded Pixabay videos."""
    print("\n🌿 VIDÉOS PIXABAY TÉLÉCHARGÉES:")
    print("-" * 40)
    
    temp_dir = Path("output/temp")
    
    if not temp_dir.exists():
        print("❌ Dossier temp non trouvé")
        return
    
    nature_videos = list(temp_dir.glob("nature_*.mp4"))
    
    if not nature_videos:
        print("❌ Aucune vidéo nature trouvée")
        return
    
    total_size = 0
    
    for video in nature_videos:
        size_mb = video.stat().st_size / (1024 * 1024)
        total_size += size_mb
        print(f"🎬 {video.name}: {size_mb:.1f} MB")
    
    print(f"\n📊 Total: {len(nature_videos)} vidéos, {total_size:.1f} MB")

def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] == "show-videos":
        show_pixabay_videos()
    else:
        create_final_nature_video()

if __name__ == "__main__":
    main()
