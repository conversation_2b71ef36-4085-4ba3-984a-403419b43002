#!/usr/bin/env python3
"""
test_piper_simple.py - Test simple de Piper TTS
"""

import subprocess
import os
from pathlib import Path

def test_piper_executable():
    """Test if Piper executable works."""
    print("🎤 TEST SIMPLE DE PIPER TTS")
    print("=" * 40)
    
    # Test different ways to run Piper
    test_commands = [
        ["./piper_wrapper.sh", "--help"],
        ["./piper/piper", "--help"],
    ]
    
    for i, cmd in enumerate(test_commands, 1):
        print(f"\n🧪 Test {i}: {' '.join(cmd)}")
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env['DYLD_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
            env['DYLD_FALLBACK_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
            env['ESPEAK_DATA_PATH'] = "./piper/espeak-ng-data"
            
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  timeout=10, env=env)
            
            if result.returncode == 0:
                print("✅ Succès!")
                print(f"Output: {result.stdout[:200]}...")
                return cmd[0]  # Return working command
            else:
                print(f"❌ Échec (code {result.returncode})")
                print(f"Erreur: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ Timeout")
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return None

def test_piper_with_model():
    """Test Piper with a simple model if available."""
    print("\n🎵 TEST AVEC MODÈLE VOCAL")
    print("-" * 30)
    
    # Check if we have any models
    models_dir = Path.home() / ".local" / "share" / "piper" / "models"
    
    if not models_dir.exists():
        print(f"❌ Dossier modèles non trouvé: {models_dir}")
        print("💡 Créez le dossier et téléchargez des modèles")
        return False
    
    # Look for any .onnx model
    models = list(models_dir.glob("*.onnx"))
    
    if not models:
        print("❌ Aucun modèle .onnx trouvé")
        print("💡 Téléchargez des modèles depuis:")
        print("   https://huggingface.co/rhasspy/piper-voices")
        return False
    
    print(f"✅ Trouvé {len(models)} modèle(s):")
    for model in models[:3]:  # Show first 3
        print(f"   • {model.name}")
    
    # Test with first model
    test_model = models[0]
    test_text = "Bonjour, ceci est un test."
    output_file = "test_piper_output.wav"
    
    print(f"\n🧪 Test avec modèle: {test_model.name}")
    
    try:
        # Set environment
        env = os.environ.copy()
        env['DYLD_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
        env['DYLD_FALLBACK_LIBRARY_PATH'] = "/opt/homebrew/lib:./piper"
        env['ESPEAK_DATA_PATH'] = "./piper/espeak-ng-data"
        
        # Run Piper TTS
        cmd = ["./piper_wrapper.sh", "--model", str(test_model), "--output_file", output_file]
        
        result = subprocess.run(cmd, input=test_text, text=True, 
                              capture_output=True, timeout=30, env=env)
        
        if result.returncode == 0:
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"✅ Synthèse réussie!")
                print(f"📁 Fichier: {output_file} ({file_size} bytes)")
                
                # Cleanup
                Path(output_file).unlink(missing_ok=True)
                return True
            else:
                print("❌ Fichier de sortie non créé")
        else:
            print(f"❌ Échec (code {result.returncode})")
            print(f"Erreur: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return False

def download_sample_model():
    """Download a sample French model."""
    print("\n📥 TÉLÉCHARGEMENT MODÈLE FRANÇAIS")
    print("-" * 40)
    
    models_dir = Path.home() / ".local" / "share" / "piper" / "models"
    models_dir.mkdir(parents=True, exist_ok=True)
    
    # Download a small French model
    model_url = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/gilles/low/fr_FR-gilles-low.onnx"
    config_url = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/fr/fr_FR/gilles/low/fr_FR-gilles-low.onnx.json"
    
    model_file = models_dir / "fr_FR-gilles-low.onnx"
    config_file = models_dir / "fr_FR-gilles-low.onnx.json"
    
    if model_file.exists():
        print(f"✅ Modèle déjà présent: {model_file}")
        return True
    
    print(f"📥 Téléchargement vers: {models_dir}")
    
    try:
        import urllib.request
        
        print("🔄 Téléchargement du modèle...")
        urllib.request.urlretrieve(model_url, model_file)
        
        print("🔄 Téléchargement de la configuration...")
        urllib.request.urlretrieve(config_url, config_file)
        
        if model_file.exists() and config_file.exists():
            model_size = model_file.stat().st_size / (1024 * 1024)
            print(f"✅ Téléchargement réussi!")
            print(f"📊 Taille modèle: {model_size:.1f} MB")
            return True
        else:
            print("❌ Fichiers non créés")
            return False
            
    except Exception as e:
        print(f"❌ Erreur de téléchargement: {e}")
        return False

def main():
    """Main test function."""
    print("🎤 CONFIGURATION ET TEST PIPER TTS")
    print("=" * 50)
    
    # Test 1: Executable
    working_cmd = test_piper_executable()
    
    if not working_cmd:
        print("\n❌ Aucune commande Piper fonctionnelle trouvée")
        print("\n💡 SOLUTIONS POSSIBLES:")
        print("1. Vérifiez que le dossier ./piper contient l'exécutable")
        print("2. Installez espeak-ng: brew install espeak-ng")
        print("3. Donnez les permissions: chmod +x ./piper/piper")
        print("4. Supprimez la quarantaine: xattr -d com.apple.quarantine ./piper/piper")
        return
    
    print(f"\n✅ Commande fonctionnelle: {working_cmd}")
    
    # Test 2: Download model if needed
    if not test_piper_with_model():
        print("\n📥 Tentative de téléchargement d'un modèle...")
        if download_sample_model():
            print("🔄 Nouveau test avec le modèle téléchargé...")
            test_piper_with_model()
    
    print("\n🎉 TESTS TERMINÉS!")
    print("\n📖 PROCHAINES ÉTAPES:")
    print("1. Si les tests sont réussis, Piper est prêt!")
    print("2. Utilisez: python piper_tts.py pour tester l'intégration")
    print("3. Créez des vidéos avec: python create_nature_video_with_piper.py")

if __name__ == "__main__":
    main()
