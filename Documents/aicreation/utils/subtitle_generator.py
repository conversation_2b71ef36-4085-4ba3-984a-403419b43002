#!/usr/bin/env python3
"""
Générateur de sous-titres automatiques synchronisés.
Crée des fichiers SRT avec timing précis basé sur l'analyse audio.
"""

import re
import math
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import moviepy.editor as mpy
from loguru import logger

try:
    import librosa
    import numpy as np
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    logger.warning("librosa non disponible - timing approximatif utilisé")

class SubtitleStyle(Enum):
    """Styles de sous-titres disponibles"""
    CLASSIC = "classic"
    MODERN = "modern"
    BOLD = "bold"
    MINIMAL = "minimal"

@dataclass
class SubtitleSegment:
    """Segment de sous-titre avec timing"""
    start_time: float
    end_time: float
    text: str
    confidence: float = 1.0

@dataclass
class SubtitleConfig:
    """Configuration pour la génération de sous-titres"""
    style: SubtitleStyle = SubtitleStyle.MODERN
    max_chars_per_line: int = 40
    max_lines: int = 2
    min_duration: float = 1.0
    max_duration: float = 6.0
    words_per_second: float = 2.5  # Vitesse de lecture moyenne
    
    # Styles visuels
    font_size: int = 24
    font_color: str = "white"
    outline_color: str = "black"
    outline_width: int = 2
    background_alpha: float = 0.0

class SubtitleGenerator:
    """Générateur de sous-titres avec synchronisation automatique"""
    
    def __init__(self, config: SubtitleConfig = None):
        self.config = config or SubtitleConfig()
        
    def generate_srt(
        self,
        script_data: Dict,
        audio_path: str,
        output_path: str,
        video_duration: float
    ) -> str:
        """
        Génère un fichier SRT synchronisé
        
        Args:
            script_data: Données du script structuré
            audio_path: Chemin vers le fichier audio
            output_path: Chemin de sortie pour le fichier SRT
            video_duration: Durée totale de la vidéo
            
        Returns:
            Chemin vers le fichier SRT généré
        """
        logger.info(f"Génération des sous-titres: {output_path}")
        
        try:
            # Extraction du texte et segmentation
            segments = self._create_segments_from_script(script_data, video_duration)
            
            # Analyse audio pour timing précis si disponible
            if LIBROSA_AVAILABLE and Path(audio_path).exists():
                segments = self._refine_timing_with_audio(segments, audio_path)
            
            # Génération du fichier SRT
            srt_content = self._generate_srt_content(segments)
            
            # Sauvegarde
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)
            
            logger.info(f"✅ Sous-titres générés: {len(segments)} segments")
            return output_path
            
        except Exception as e:
            logger.error(f"Erreur génération sous-titres: {e}")
            raise
    
    def _create_segments_from_script(self, script_data: Dict, duration: float) -> List[SubtitleSegment]:
        """Crée les segments de base à partir du script"""
        segments = []
        current_time = 0.0
        
        # Ordre des sections dans le script
        sections = [
            ("HOOK", script_data.get("HOOK", "")),
            ("INTRODUCTION", script_data.get("INTRODUCTION", "")),
            ("POINTS_CLES", script_data.get("POINTS_CLES", [])),
            ("CONCLUSION", script_data.get("CONCLUSION", ""))
        ]
        
        for section_name, content in sections:
            if not content:
                continue
                
            # Traitement selon le type de contenu
            if isinstance(content, list):
                # Points clés - un segment par point
                for point in content:
                    if point.strip():
                        segment_duration = self._estimate_duration(point)
                        segments.append(SubtitleSegment(
                            start_time=current_time,
                            end_time=current_time + segment_duration,
                            text=point.strip()
                        ))
                        current_time += segment_duration
            else:
                # Texte simple - découpage en phrases
                sentences = self._split_into_sentences(content)
                for sentence in sentences:
                    if sentence.strip():
                        segment_duration = self._estimate_duration(sentence)
                        segments.append(SubtitleSegment(
                            start_time=current_time,
                            end_time=current_time + segment_duration,
                            text=sentence.strip()
                        ))
                        current_time += segment_duration
        
        # Ajustement pour respecter la durée totale
        if segments and current_time != duration:
            scale_factor = duration / current_time
            for segment in segments:
                segment.start_time *= scale_factor
                segment.end_time *= scale_factor
        
        return segments
    
    def _estimate_duration(self, text: str) -> float:
        """Estime la durée nécessaire pour lire un texte"""
        word_count = len(text.split())
        duration = word_count / self.config.words_per_second
        
        # Respect des limites min/max
        duration = max(self.config.min_duration, duration)
        duration = min(self.config.max_duration, duration)
        
        return duration
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Découpe un texte en phrases"""
        # Nettoyage du texte
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Découpage par ponctuation forte
        sentences = re.split(r'[.!?]+', text)
        
        # Nettoyage et filtrage
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # Fusion des phrases trop courtes
        merged_sentences = []
        current_sentence = ""
        
        for sentence in sentences:
            if len(current_sentence + " " + sentence) <= self.config.max_chars_per_line:
                current_sentence = (current_sentence + " " + sentence).strip()
            else:
                if current_sentence:
                    merged_sentences.append(current_sentence)
                current_sentence = sentence
        
        if current_sentence:
            merged_sentences.append(current_sentence)
        
        return merged_sentences
    
    def _refine_timing_with_audio(self, segments: List[SubtitleSegment], audio_path: str) -> List[SubtitleSegment]:
        """Affine le timing en analysant l'audio"""
        if not LIBROSA_AVAILABLE:
            return segments
        
        try:
            logger.info("Analyse audio pour timing précis...")
            
            # Chargement de l'audio
            y, sr = librosa.load(audio_path)
            
            # Détection des pauses (silence)
            silence_threshold = 0.01
            frame_length = 2048
            hop_length = 512
            
            # Calcul de l'énergie RMS
            rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]
            
            # Détection des segments de parole
            speech_frames = rms > silence_threshold
            
            # Conversion en temps
            times = librosa.frames_to_time(np.arange(len(speech_frames)), sr=sr, hop_length=hop_length)
            
            # Identification des pauses
            pauses = []
            in_pause = False
            pause_start = 0
            
            for i, is_speech in enumerate(speech_frames):
                if not is_speech and not in_pause:
                    # Début d'une pause
                    in_pause = True
                    pause_start = times[i]
                elif is_speech and in_pause:
                    # Fin d'une pause
                    in_pause = False
                    pause_duration = times[i] - pause_start
                    if pause_duration > 0.3:  # Pause significative
                        pauses.append((pause_start, times[i]))
            
            # Ajustement des segments basé sur les pauses
            if pauses:
                segments = self._align_segments_with_pauses(segments, pauses)
            
            logger.info(f"Timing affiné avec {len(pauses)} pauses détectées")
            
        except Exception as e:
            logger.warning(f"Erreur analyse audio: {e} - timing approximatif utilisé")
        
        return segments
    
    def _align_segments_with_pauses(
        self, 
        segments: List[SubtitleSegment], 
        pauses: List[Tuple[float, float]]
    ) -> List[SubtitleSegment]:
        """Aligne les segments avec les pauses détectées"""
        if not pauses:
            return segments
        
        # Création d'une liste des points de coupure naturels
        break_points = [pause[0] for pause in pauses]
        break_points.sort()
        
        # Ajustement des segments
        adjusted_segments = []
        
        for segment in segments:
            # Recherche du point de coupure le plus proche
            best_break = None
            min_distance = float('inf')
            
            for break_point in break_points:
                if segment.start_time <= break_point <= segment.end_time:
                    distance = abs(break_point - (segment.start_time + segment.end_time) / 2)
                    if distance < min_distance:
                        min_distance = distance
                        best_break = break_point
            
            # Ajustement si un point de coupure approprié est trouvé
            if best_break and min_distance < 1.0:  # Seuil de 1 seconde
                # Division du segment si nécessaire
                if best_break - segment.start_time > 0.5:
                    # Premier segment
                    adjusted_segments.append(SubtitleSegment(
                        start_time=segment.start_time,
                        end_time=best_break,
                        text=segment.text
                    ))
                else:
                    adjusted_segments.append(segment)
            else:
                adjusted_segments.append(segment)
        
        return adjusted_segments
    
    def _generate_srt_content(self, segments: List[SubtitleSegment]) -> str:
        """Génère le contenu du fichier SRT"""
        srt_lines = []
        
        for i, segment in enumerate(segments, 1):
            # Numéro du sous-titre
            srt_lines.append(str(i))
            
            # Timing au format SRT
            start_time = self._format_srt_time(segment.start_time)
            end_time = self._format_srt_time(segment.end_time)
            srt_lines.append(f"{start_time} --> {end_time}")
            
            # Texte avec formatage selon le style
            formatted_text = self._format_text_for_style(segment.text)
            srt_lines.append(formatted_text)
            
            # Ligne vide
            srt_lines.append("")
        
        return "\n".join(srt_lines)
    
    def _format_srt_time(self, seconds: float) -> str:
        """Formate le temps au format SRT (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _format_text_for_style(self, text: str) -> str:
        """Formate le texte selon le style choisi"""
        if self.config.style == SubtitleStyle.BOLD:
            return f"<b>{text}</b>"
        elif self.config.style == SubtitleStyle.MODERN:
            # Mise en évidence des mots-clés
            keywords = ["important", "incroyable", "découvrez", "nouveau"]
            for keyword in keywords:
                text = re.sub(
                    f"\\b{keyword}\\b", 
                    f"<i>{keyword}</i>", 
                    text, 
                    flags=re.IGNORECASE
                )
            return text
        else:  # CLASSIC ou MINIMAL
            return text
    
    def create_moviepy_subtitles(
        self, 
        segments: List[SubtitleSegment], 
        video_width: int, 
        video_height: int
    ) -> List[mpy.TextClip]:
        """Crée des clips de sous-titres pour MoviePy"""
        subtitle_clips = []
        
        # Position des sous-titres selon le format
        if video_width > video_height:  # Format paysage
            position = ('center', video_height * 0.85)
            fontsize = self.config.font_size
        else:  # Format portrait
            position = ('center', video_height * 0.8)
            fontsize = self.config.font_size * 0.8
        
        for segment in segments:
            # Création du clip de texte
            txt_clip = mpy.TextClip(
                segment.text,
                fontsize=fontsize,
                color=self.config.font_color,
                stroke_color=self.config.outline_color,
                stroke_width=self.config.outline_width,
                method='caption',
                size=(video_width * 0.8, None)
            )
            
            # Configuration du timing et de la position
            txt_clip = (txt_clip
                       .set_position(position)
                       .set_start(segment.start_time)
                       .set_duration(segment.end_time - segment.start_time)
                       .crossfadein(0.2)
                       .crossfadeout(0.2))
            
            subtitle_clips.append(txt_clip)
        
        return subtitle_clips

# Fonction utilitaire pour génération rapide
def generate_subtitles(
    script_data: Dict,
    audio_path: str,
    output_path: str,
    video_duration: float,
    style: SubtitleStyle = SubtitleStyle.MODERN
) -> str:
    """Fonction utilitaire pour génération rapide de sous-titres"""
    config = SubtitleConfig(style=style)
    generator = SubtitleGenerator(config)
    return generator.generate_srt(script_data, audio_path, output_path, video_duration)

# Exemple d'utilisation
if __name__ == "__main__":
    # Test du générateur de sous-titres
    test_script = {
        "HOOK": "Découvrez l'incroyable !",
        "INTRODUCTION": "Aujourd'hui, nous explorons un sujet fascinant.",
        "POINTS_CLES": [
            "Premier point important à retenir",
            "Deuxième aspect crucial du sujet",
            "Troisième élément essentiel"
        ],
        "CONCLUSION": "N'oubliez pas de vous abonner pour plus de contenu !"
    }
    
    generator = SubtitleGenerator()
    
    # Génération des segments
    segments = generator._create_segments_from_script(test_script, 60.0)
    
    # Affichage des résultats
    for i, segment in enumerate(segments, 1):
        print(f"{i}. {segment.start_time:.1f}s - {segment.end_time:.1f}s: {segment.text}")
    
    # Génération du contenu SRT
    srt_content = generator._generate_srt_content(segments)
    print("\nContenu SRT généré:")
    print(srt_content[:500] + "..." if len(srt_content) > 500 else srt_content)
