#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple Auto YouTube Video Generator

This script automates the creation and publishing of YouTube videos based on trending topics.
It uses minimal dependencies for better compatibility.
"""

import os
import sys
import time
import logging
import random
import json
import datetime
import subprocess
import tempfile
from pathlib import Path
import requests
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("auto_youtube.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
CATEGORIES = ["history", "economy", "news"]
OUTPUT_DIR = Path("output")
CREDENTIALS_FILE = "client_secrets.json"
TOKEN_FILE = "token.json"
SCOPES = ["https://www.googleapis.com/auth/youtube.upload",
          "https://www.googleapis.com/auth/youtube.readonly"]
OLLAMA_MODEL = "phi3:medium"  # Using phi3:medium  model available on your system
PUBLICATION_HOUR = 18  # 6:00 PM

# Check for required modules
REQUIRED_MODULES = {
    "pytrends": False,
    "ollama": False,
    "gtts": False,
    "PIL": False,
    "google.oauth2": False
}

try:
    from pytrends.request import TrendReq
    REQUIRED_MODULES["pytrends"] = True
except ImportError:
    logger.warning("pytrends module not found. Trending keyword functionality will be limited.")

# Check if Ollama is available via command line
try:
    import subprocess
    import tempfile

    # Test if Ollama is available
    result = subprocess.run(["/Applications/Ollama.app/Contents/Resources/ollama", "list"],
                           capture_output=True, text=True)
    if result.returncode == 0:
        REQUIRED_MODULES["ollama"] = True
        logger.info("Ollama command line is available")
    else:
        logger.warning("Ollama command line test failed. Script generation will use fallback methods.")
        REQUIRED_MODULES["ollama"] = False
except Exception as e:
    logger.warning(f"Error checking Ollama availability: {str(e)}")
    logger.warning("Script generation will use fallback methods.")
    REQUIRED_MODULES["ollama"] = False

try:
    from gtts import gTTS
    REQUIRED_MODULES["gtts"] = True
except ImportError:
    logger.warning("gtts module not found. Text-to-speech functionality will be limited.")

try:
    from PIL import Image, ImageDraw, ImageFont
    REQUIRED_MODULES["PIL"] = True
except ImportError:
    logger.warning("PIL module not found. Thumbnail creation will be limited.")

try:
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.auth.transport.requests import Request
    import googleapiclient.discovery
    import googleapiclient.errors
    import googleapiclient.http
    REQUIRED_MODULES["google.oauth2"] = True
except ImportError:
    logger.warning("Google API modules not found. YouTube upload functionality will be disabled.")

class SimpleAutoYouTube:
    """Main class for YouTube video automation with minimal dependencies."""

    def __init__(self):
        """Initialize the SimpleAutoYouTube class."""
        self.pytrends = None
        if REQUIRED_MODULES["pytrends"]:
            self.pytrends = TrendReq(hl='en-US', tz=0)

        self.youtube = None
        self.setup_directories()

    def setup_directories(self):
        """Create necessary directories if they don't exist."""
        OUTPUT_DIR.mkdir(exist_ok=True)
        for subdir in ["scripts", "audio", "videos", "thumbnails"]:
            (OUTPUT_DIR / subdir).mkdir(exist_ok=True)

    def authenticate_youtube(self):
        """Authenticate with YouTube API using OAuth2."""
        if not REQUIRED_MODULES["google.oauth2"]:
            logger.error("Google API modules not available. Cannot authenticate with YouTube.")
            return False

        credentials = None

        # Load saved credentials if they exist
        if os.path.exists(TOKEN_FILE):
            credentials = Credentials.from_authorized_user_info(
                json.load(open(TOKEN_FILE))
            )

        # If credentials are invalid or don't exist, get new ones
        if not credentials or not credentials.valid:
            if credentials and credentials.expired and credentials.refresh_token:
                credentials.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    CREDENTIALS_FILE, SCOPES
                )
                credentials = flow.run_local_server(port=0)

            # Save credentials for future use
            with open(TOKEN_FILE, 'w') as token:
                token.write(credentials.to_json())

        # Build the YouTube API client
        self.youtube = googleapiclient.discovery.build(
            "youtube", "v3", credentials=credentials
        )
        logger.info("Successfully authenticated with YouTube API")
        return True

    def get_trending_keywords(self) -> List[str]:
        """Fetch trending keywords from Google Trends for specified categories."""
        trending_keywords = []

        try:
            if not REQUIRED_MODULES["pytrends"] or not self.pytrends:
                # Fallback to hardcoded keywords
                return ["history facts", "economic trends", "current events"]

            for category in CATEGORIES:
                # Get daily trending searches
                trending_searches = self.pytrends.trending_searches(pn='united_states')
                # Filter by category using realtime_trending_searches
                self.pytrends.build_payload(kw_list=[category])

                # Get top 5 keywords for each category
                for keyword in trending_searches.iloc[:5, 0].tolist():
                    if keyword not in trending_keywords:
                        trending_keywords.append(keyword)

            logger.info(f"Found {len(trending_keywords)} trending keywords")
            return trending_keywords[:5]  # Limit to top 5 overall

        except Exception as e:
            logger.error(f"Error fetching trending keywords: {str(e)}")
            return ["history facts", "economic trends", "current events"]  # Fallback keywords

    def generate_script(self, keyword: str) -> Dict[str, str]:
        """Generate a video script using Ollama or fallback method."""
        prompt = f"""
        Write a 3-minute video script about "{keyword}" with the following structure:
        1. Title: An engaging title for the video
        2. Introduction: Brief introduction to the topic (30 seconds)
        3. 3 Key Points: Three main points about the topic (2 minutes)
        4. Conclusion: A brief conclusion (30 seconds)

        Format the response as a JSON with the following keys:
        "title", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion"

        Make sure to return valid JSON format that can be parsed with json.loads().
        """

        try:
            if not REQUIRED_MODULES["ollama"]:
                logger.warning("Ollama not available. Using fallback script generation.")
                # Fallback to basic script
                return {
                    "title": f"Understanding {keyword.title()}",
                    "introduction": f"Today we're exploring {keyword}.",
                    "key_point_1": f"First point about {keyword}.",
                    "key_point_2": f"Second point about {keyword}.",
                    "key_point_3": f"Third point about {keyword}.",
                    "conclusion": f"That's all about {keyword}. Thanks for watching!"
                }

            logger.info(f"Generating script with Ollama model '{OLLAMA_MODEL}'")

            # Call Ollama via command line
            try:
                logger.info(f"Calling Ollama command line with model '{OLLAMA_MODEL}'")

                # Create a temporary file for the prompt
                with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp:
                    temp.write(prompt)
                    temp_path = temp.name

                # Read the content of the file
                with open(temp_path, 'r') as f:
                    prompt_content = f.read()

                # Call Ollama command
                cmd = ["/Applications/Ollama.app/Contents/Resources/ollama", "run", OLLAMA_MODEL, prompt_content]
                result = subprocess.run(cmd, capture_output=True, text=True)

                # Remove temporary file
                os.unlink(temp_path)

                if result.returncode != 0:
                    logger.error(f"Ollama command failed: {result.stderr}")
                    raise Exception(f"Ollama command failed: {result.stderr}")

                # Extract content from response
                content = result.stdout
                logger.info(f"Received response from Ollama")

                # Find JSON content (might be wrapped in markdown code blocks)
                if "```json" in content:
                    json_str = content.split("```json")[1].split("```")[0].strip()
                    logger.info("Extracted JSON from code block with json tag")
                elif "```" in content:
                    json_str = content.split("```")[1].strip()
                    logger.info("Extracted JSON from generic code block")
                else:
                    json_str = content
                    logger.info("Using full response as JSON")

                # Try to parse JSON
                try:
                    script = json.loads(json_str)

                    # Verify all required keys are present
                    required_keys = ["title", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion"]
                    missing_keys = [key for key in required_keys if key not in script]

                    if missing_keys:
                        logger.warning(f"JSON is missing keys: {missing_keys}")
                        # Add missing keys with default values
                        for key in missing_keys:
                            if key == "title":
                                script[key] = f"Understanding {keyword.title()}"
                            elif key == "introduction":
                                script[key] = f"Today we're exploring {keyword}."
                            elif key.startswith("key_point"):
                                point_num = key.split("_")[-1]
                                script[key] = f"Point {point_num} about {keyword}."
                            elif key == "conclusion":
                                script[key] = f"That's all about {keyword}. Thanks for watching!"

                    # Save script to file
                    script_path = OUTPUT_DIR / "scripts" / f"{keyword.replace(' ', '_')}.json"
                    with open(script_path, 'w', encoding='utf-8') as f:
                        json.dump(script, f, ensure_ascii=False, indent=4)

                    logger.info(f"Generated script for '{keyword}' using Ollama")
                    return script

                except json.JSONDecodeError as je:
                    logger.error(f"Failed to parse JSON: {je}")
                    logger.error(f"Raw JSON string: {json_str[:100]}...")
                    raise

            except Exception as oe:
                logger.error(f"Ollama API error: {str(oe)}")
                raise

        except Exception as e:
            logger.error(f"Error generating script for '{keyword}': {str(e)}")
            # Return a basic script as fallback
            return {
                "title": f"Understanding {keyword.title()}",
                "introduction": f"Today we're exploring {keyword}.",
                "key_point_1": f"First point about {keyword}.",
                "key_point_2": f"Second point about {keyword}.",
                "key_point_3": f"Third point about {keyword}.",
                "conclusion": f"That's all about {keyword}. Thanks for watching!"
            }

    def text_to_speech(self, script: Dict[str, str], keyword: str) -> Optional[str]:
        """Convert script to speech using gTTS or fallback method."""
        # Combine script parts into a single text
        full_text = (
            f"{script['introduction']} "
            f"First key point: {script['key_point_1']} "
            f"Second key point: {script['key_point_2']} "
            f"Third key point: {script['key_point_3']} "
            f"{script['conclusion']}"
        )

        try:
            if not REQUIRED_MODULES["gtts"]:
                logger.error("gTTS module not available. Cannot generate audio.")
                return None

            audio_path = OUTPUT_DIR / "audio" / f"{keyword.replace(' ', '_')}.mp3"

            # Convert text to speech
            tts = gTTS(text=full_text, lang='en', slow=False)
            tts.save(str(audio_path))

            logger.info(f"Generated audio for '{keyword}'")
            return str(audio_path)

        except Exception as e:
            logger.error(f"Error generating audio for '{keyword}': {str(e)}")
            return None

    def create_video(self, script: Dict[str, str], audio_path: Optional[str], keyword: str) -> Optional[str]:
        """Create a simple video using FFmpeg."""
        try:
            if not audio_path:
                logger.error("No audio file available. Cannot create video.")
                return None

            # Create a simple video with FFmpeg
            video_path = OUTPUT_DIR / "videos" / f"{keyword.replace(' ', '_')}.mp4"

            # Create a text file with the script content
            text_file = OUTPUT_DIR / "temp_script.txt"
            with open(text_file, 'w', encoding='utf-8') as f:
                f.write(f"Title: {script['title']}\n\n")
                f.write(f"Introduction:\n{script['introduction']}\n\n")
                f.write(f"Key Point 1:\n{script['key_point_1']}\n\n")
                f.write(f"Key Point 2:\n{script['key_point_2']}\n\n")
                f.write(f"Key Point 3:\n{script['key_point_3']}\n\n")
                f.write(f"Conclusion:\n{script['conclusion']}")

            # Use FFmpeg to create a video with the text and audio
            # This creates a simple video with scrolling text
            cmd = [
                "ffmpeg", "-y",
                "-f", "lavfi", "-i", "color=c=gray:s=1280x720:d=180",  # Background
                "-i", audio_path,  # Audio input
                "-vf", f"drawtext=fontfile=/System/Library/Fonts/Helvetica.ttc:fontsize=24:fontcolor=white:x=(w-text_w)/2:y=h/4:text='{script['title']}':box=1:boxcolor=black@0.5:boxborderw=5",
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-c:a", "aac", "-b:a", "128k",
                "-shortest",
                str(video_path)
            ]

            # Try to run FFmpeg
            try:
                subprocess.run(cmd, check=True, capture_output=True)
                logger.info(f"Created video for '{keyword}'")
                return str(video_path)
            except subprocess.CalledProcessError as e:
                logger.error(f"FFmpeg error: {e.stderr.decode() if hasattr(e, 'stderr') else str(e)}")

                # Fallback: Try a simpler FFmpeg command
                try:
                    simple_cmd = [
                        "ffmpeg", "-y",
                        "-f", "lavfi", "-i", "color=c=blue:s=1280x720:d=180",
                        "-i", audio_path,
                        "-c:v", "libx264", "-c:a", "aac",
                        "-shortest",
                        str(video_path)
                    ]
                    subprocess.run(simple_cmd, check=True, capture_output=True)
                    logger.info(f"Created simple video for '{keyword}'")
                    return str(video_path)
                except subprocess.CalledProcessError as e2:
                    logger.error(f"Simple FFmpeg error: {e2.stderr.decode() if hasattr(e2, 'stderr') else str(e2)}")
                    return None

        except Exception as e:
            logger.error(f"Error creating video for '{keyword}': {str(e)}")
            return None

    def create_thumbnail(self, script: Dict[str, str], keyword: str) -> Optional[str]:
        """Generate a simple thumbnail with the title."""
        try:
            if not REQUIRED_MODULES["PIL"]:
                logger.error("PIL module not available. Cannot create thumbnail.")
                return None

            # Create a blank image with dimensions 1280x720 (YouTube thumbnail size)
            width, height = 1280, 720
            background_color = (33, 33, 33)  # Dark background
            text_color = (255, 255, 255)  # White text

            # Create image
            image = Image.new('RGB', (width, height), background_color)
            draw = ImageDraw.Draw(image)

            # Try to load a font, fall back to default if not available
            try:
                # Try to use a bold font if available
                title_font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 80)
                subtitle_font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 40)
            except IOError:
                # Fall back to default font
                title_font = ImageFont.load_default()
                subtitle_font = title_font

            # Add title text
            title = script['title']
            # Limit title length
            if len(title) > 50:
                title = title[:47] + "..."

            # Calculate text position to center it
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            title_height = title_bbox[3] - title_bbox[1]

            title_position = ((width - title_width) // 2, (height - title_height) // 2 - 50)

            # Draw title
            draw.text(title_position, title, font=title_font, fill=text_color)

            # Add subtitle (keyword)
            subtitle = f"Understanding {keyword}"
            subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
            subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]

            subtitle_position = ((width - subtitle_width) // 2, title_position[1] + title_height + 30)
            draw.text(subtitle_position, subtitle, font=subtitle_font, fill=(200, 200, 200))

            # Save the thumbnail
            thumbnail_path = OUTPUT_DIR / "thumbnails" / f"{keyword.replace(' ', '_')}.jpg"
            image.save(str(thumbnail_path), quality=95)

            logger.info(f"Created thumbnail for '{keyword}'")
            return str(thumbnail_path)

        except Exception as e:
            logger.error(f"Error creating thumbnail for '{keyword}': {str(e)}")
            return None

    def upload_to_youtube(self, video_path: str, thumbnail_path: Optional[str], script: Dict[str, str], keyword: str) -> Optional[str]:
        """Upload video to YouTube and schedule it for publication."""
        if not REQUIRED_MODULES["google.oauth2"]:
            logger.error("Google API modules not available. Cannot upload to YouTube.")
            return None

        if not self.youtube:
            success = self.authenticate_youtube()
            if not success:
                return None

        try:
            # Prepare video metadata
            title = f"{script['title']} - Quick Explanation"
            if len(title) > 100:  # YouTube title limit
                title = title[:97] + "..."

            description = (
                f"{script['introduction']}\n\n"
                f"Key Points:\n"
                f"1. {script['key_point_1']}\n"
                f"2. {script['key_point_2']}\n"
                f"3. {script['key_point_3']}\n\n"
                f"{script['conclusion']}\n\n"
                f"#Trending #{keyword.replace(' ', '')} #Explanation"
            )

            # Generate tags
            tags = [keyword] + keyword.split() + ["explanation", "quick", "trending"]
            tags = list(set([tag.lower() for tag in tags if len(tag) > 2]))[:15]  # Limit to 15 unique tags

            # Set scheduled publication time (18:00 today or tomorrow)
            now = datetime.datetime.now()
            publish_time = now.replace(hour=PUBLICATION_HOUR, minute=0, second=0, microsecond=0)

            # If it's already past publication time, schedule for tomorrow
            if now > publish_time:
                publish_time = publish_time + datetime.timedelta(days=1)

            # Convert to RFC3339 format
            publish_time_rfc3339 = publish_time.isoformat() + "Z"

            # Prepare the request body
            body = {
                "snippet": {
                    "title": title,
                    "description": description,
                    "tags": tags,
                    "categoryId": "27"  # Education category
                },
                "status": {
                    "privacyStatus": "private",  # Start as private
                    "publishAt": publish_time_rfc3339,
                    "selfDeclaredMadeForKids": False
                }
            }

            # Upload the video
            logger.info(f"Starting upload for '{keyword}'")

            # Create upload request
            request = self.youtube.videos().insert(
                part=",".join(body.keys()),
                body=body,
                media_body=googleapiclient.http.MediaFileUpload(
                    video_path,
                    resumable=True
                )
            )

            # Execute the upload
            response = None
            while response is None:
                status, response = request.next_chunk()
                if status:
                    logger.info(f"Uploaded {int(status.progress() * 100)}%")

            video_id = response["id"]
            logger.info(f"Video upload complete. Video ID: {video_id}")

            # Upload thumbnail if available
            if thumbnail_path:
                try:
                    self.youtube.thumbnails().set(
                        videoId=video_id,
                        media_body=googleapiclient.http.MediaFileUpload(thumbnail_path)
                    ).execute()
                    logger.info(f"Thumbnail uploaded for video ID: {video_id}")
                except Exception as e:
                    logger.error(f"Error uploading thumbnail: {str(e)}")

            # Return the video URL
            return f"https://www.youtube.com/watch?v={video_id}"

        except Exception as e:
            logger.error(f"Error uploading video for '{keyword}': {str(e)}")
            return None

    def process_keyword(self, keyword: str) -> bool:
        """Process a single keyword through the entire pipeline."""
        try:
            logger.info(f"Processing keyword: '{keyword}'")

            # Generate script
            script = self.generate_script(keyword)
            if not script:
                return False

            # Convert to speech
            audio_path = self.text_to_speech(script, keyword)
            if not audio_path:
                logger.warning(f"Could not generate audio for '{keyword}', skipping video creation")
                return False

            # Create video
            video_path = self.create_video(script, audio_path, keyword)
            if not video_path:
                logger.warning(f"Could not create video for '{keyword}', skipping upload")
                return False

            # Create thumbnail
            thumbnail_path = self.create_thumbnail(script, keyword)

            # Upload to YouTube if Google API is available
            if REQUIRED_MODULES["google.oauth2"]:
                video_url = self.upload_to_youtube(video_path, thumbnail_path, script, keyword)
                if video_url:
                    logger.info(f"Successfully processed keyword: '{keyword}', Video URL: {video_url}")
                else:
                    logger.warning(f"Video created but upload failed for '{keyword}'")
                    logger.info(f"Video saved at: {video_path}")
            else:
                logger.info(f"YouTube upload skipped (API not available). Video saved at: {video_path}")

            return True

        except Exception as e:
            logger.error(f"Error processing keyword '{keyword}': {str(e)}")
            return False


def main():
    """Main function to run the YouTube automation process."""
    try:
        # Initialize the SimpleAutoYouTube class
        auto_yt = SimpleAutoYouTube()

        # Check which modules are available
        logger.info("Available modules:")
        for module, available in REQUIRED_MODULES.items():
            logger.info(f"  {module}: {'Available' if available else 'Not available'}")

        # Get trending keywords once
        keywords = auto_yt.get_trending_keywords()
        logger.info(f"Found {len(keywords)} trending keywords: {keywords}")

        # Process each keyword
        for keyword in keywords:
            try:
                logger.info(f"Processing keyword: '{keyword}'")
                success = auto_yt.process_keyword(keyword)

                if success:
                    logger.info(f"Successfully processed '{keyword}'")
                else:
                    logger.warning(f"Failed to process '{keyword}'")

                # Add a delay between processing keywords to avoid rate limits
                if keyword != keywords[-1]:  # Skip delay after the last keyword
                    logger.info(f"Waiting 60 seconds before processing next keyword...")
                    time.sleep(60)  # 1 minute delay
            except Exception as e:
                logger.error(f"Error processing keyword '{keyword}': {str(e)}")
                # Continue with the next keyword
                continue

        logger.info("Completed processing all keywords.")

    except KeyboardInterrupt:
        logger.info("Process interrupted by user. Exiting...")
    except Exception as e:
        logger.error(f"Unexpected error in main function: {str(e)}")

if __name__ == "__main__":
    main()