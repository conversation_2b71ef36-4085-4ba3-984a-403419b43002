{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://datalabeling.googleapis.com/", "batchPath": "batch", "canonicalName": "Data Labeling", "description": "Public API for Google Cloud AI Data Labeling Service.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/data-labeling/docs/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "datalabeling:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://datalabeling.mtls.googleapis.com/", "name": "datalabeling", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"annotationSpecSets": {"methods": {"create": {"description": "Creates an annotation spec set by providing a set of labels.", "flatPath": "v1beta1/projects/{projectsId}/annotationSpecSets", "httpMethod": "POST", "id": "datalabeling.projects.annotationSpecSets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. AnnotationSpecSet resource parent, format: projects/{project_id}", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/annotationSpecSets", "request": {"$ref": "GoogleCloudDatalabelingV1beta1CreateAnnotationSpecSetRequest"}, "response": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpecSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an annotation spec set by resource name.", "flatPath": "v1beta1/projects/{projectsId}/annotationSpecSets/{annotationSpecSetsId}", "httpMethod": "DELETE", "id": "datalabeling.projects.annotationSpecSets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. AnnotationSpec resource name, format: `projects/{project_id}/annotationSpecSets/{annotation_spec_set_id}`.", "location": "path", "pattern": "^projects/[^/]+/annotationSpecSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an annotation spec set by resource name.", "flatPath": "v1beta1/projects/{projectsId}/annotationSpecSets/{annotationSpecSetsId}", "httpMethod": "GET", "id": "datalabeling.projects.annotationSpecSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. AnnotationSpecSet resource name, format: projects/{project_id}/annotationSpecSets/{annotation_spec_set_id}", "location": "path", "pattern": "^projects/[^/]+/annotationSpecSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpecSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists annotation spec sets for a project. Pagination is supported.", "flatPath": "v1beta1/projects/{projectsId}/annotationSpecSets", "httpMethod": "GET", "id": "datalabeling.projects.annotationSpecSets.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter is not supported at this moment.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListAnnotationSpecSetsResponse.next_page_token of the previous [DataLabelingService.ListAnnotationSpecSets] call. Return first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent of AnnotationSpecSet resource, format: projects/{project_id}", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/annotationSpecSets", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListAnnotationSpecSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "datasets": {"methods": {"create": {"description": " Creates dataset. If success return a Dataset resource.", "flatPath": "v1beta1/projects/{projectsId}/datasets", "httpMethod": "POST", "id": "datalabeling.projects.datasets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Dataset resource parent, format: projects/{project_id}", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/datasets", "request": {"$ref": "GoogleCloudDatalabelingV1beta1CreateDatasetRequest"}, "response": {"$ref": "GoogleCloudDatalabelingV1beta1Dataset"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a dataset by resource name.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "DELETE", "id": "datalabeling.projects.datasets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Dataset resource name, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportData": {"description": "Exports data and annotations from dataset.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}:exportData", "httpMethod": "POST", "id": "datalabeling.projects.datasets.exportData", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Dataset resource name, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:exportData", "request": {"$ref": "GoogleCloudDatalabelingV1beta1ExportDataRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets dataset by resource name.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "GET", "id": "datalabeling.projects.datasets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Dataset resource name, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1Dataset"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "importData": {"description": "Imports data into dataset based on source locations defined in request. It can be called multiple times for the same dataset. Each dataset can only have one long running operation running on it. For example, no labeling task (also long running operation) can be started while importing is still ongoing. Vice versa.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}:importData", "httpMethod": "POST", "id": "datalabeling.projects.datasets.importData", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Dataset resource name, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:importData", "request": {"$ref": "GoogleCloudDatalabelingV1beta1ImportDataRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists datasets under a project. Pagination is supported.", "flatPath": "v1beta1/projects/{projectsId}/datasets", "httpMethod": "GET", "id": "datalabeling.projects.datasets.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter on dataset is not supported at this moment.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListDatasetsResponse.next_page_token of the previous [DataLabelingService.ListDatasets] call. Returns the first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. Dataset resource parent, format: projects/{project_id}", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/datasets", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListDatasetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"annotatedDatasets": {"methods": {"delete": {"description": "Deletes an annotated dataset by resource name.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}", "httpMethod": "DELETE", "id": "datalabeling.projects.datasets.annotatedDatasets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the annotated dataset to delete, format: projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/ {annotated_dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an annotated dataset by resource name.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the annotated dataset to get, format: projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/ {annotated_dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotatedDataset"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists annotated datasets for a dataset. Pagination is supported.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter is not supported at this moment.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListAnnotatedDatasetsResponse.next_page_token of the previous [DataLabelingService.ListAnnotatedDatasets] call. Return first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the dataset to list annotated datasets, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/annotatedDatasets", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListAnnotatedDatasetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"dataItems": {"methods": {"get": {"description": "Gets a data item in a dataset by resource name. This API can be called after data are imported into dataset.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/dataItems/{dataItemsId}", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.dataItems.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the data item to get, format: projects/{project_id}/datasets/{dataset_id}/dataItems/{data_item_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+/dataItems/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1DataItem"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists data items in a dataset. This API can be called after data are imported into dataset. Pagination is supported.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/dataItems", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.dataItems.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter is not supported at this moment.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListDataItemsResponse.next_page_token of the previous [DataLabelingService.ListDataItems] call. Return first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the dataset to list data items, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/dataItems", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListDataItemsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "examples": {"methods": {"get": {"description": "Gets an example by resource name, including both data and annotation.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/examples/{examplesId}", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.examples.get", "parameterOrder": ["name"], "parameters": {"filter": {"description": "Optional. An expression for filtering Examples. Filter by annotation_spec.display_name is supported. Format \"annotation_spec.display_name = {display_name}\"", "location": "query", "type": "string"}, "name": {"description": "Required. Name of example, format: projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/ {annotated_dataset_id}/examples/{example_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+/examples/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1Example"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists examples in an annotated dataset. Pagination is supported.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/examples", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.examples.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression for filtering Examples. For annotated datasets that have annotation spec set, filter by annotation_spec.display_name is supported. Format \"annotation_spec.display_name = {display_name}\"", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListExamplesResponse.next_page_token of the previous [DataLabelingService.ListExamples] call. Return first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example resource parent.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/examples", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListExamplesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "feedbackThreads": {"methods": {"delete": {"description": "Delete a FeedbackThread.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/feedbackThreads/{feedbackThreadsId}", "httpMethod": "DELETE", "id": "datalabeling.projects.datasets.annotatedDatasets.feedbackThreads.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the FeedbackThread that is going to be deleted. Format: 'projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}/feedbackThreads/{feedback_thread_id}'.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+/feedbackThreads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": " Get a FeedbackThread object.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/feedbackThreads/{feedbackThreadsId}", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.feedbackThreads.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the feedback. Format: 'projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}/feedbackThreads/{feedback_thread_id}'.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+/feedbackThreads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1FeedbackThread"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List FeedbackThreads with pagination.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/feedbackThreads", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.feedbackThreads.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListFeedbackThreads.next_page_token of the previous [DataLabelingService.ListFeedbackThreads] call. Return first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. FeedbackThread resource parent. Format: \"projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}\"", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/feedbackThreads", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListFeedbackThreadsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"feedbackMessages": {"methods": {"create": {"description": "Create a FeedbackMessage object.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/feedbackThreads/{feedbackThreadsId}/feedbackMessages", "httpMethod": "POST", "id": "datalabeling.projects.datasets.annotatedDatasets.feedbackThreads.feedbackMessages.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. FeedbackMessage resource parent, format: projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}/feedbackThreads/{feedback_thread_id}.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+/feedbackThreads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/feedbackMessages", "request": {"$ref": "GoogleCloudDatalabelingV1beta1FeedbackMessage"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a FeedbackMessage.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/feedbackThreads/{feedbackThreadsId}/feedbackMessages/{feedbackMessagesId}", "httpMethod": "DELETE", "id": "datalabeling.projects.datasets.annotatedDatasets.feedbackThreads.feedbackMessages.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the FeedbackMessage that is going to be deleted. Format: 'projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}/feedbackThreads/{feedback_thread_id}/feedbackMessages/{feedback_message_id}'.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+/feedbackThreads/[^/]+/feedbackMessages/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a FeedbackMessage object.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/feedbackThreads/{feedbackThreadsId}/feedbackMessages/{feedbackMessagesId}", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.feedbackThreads.feedbackMessages.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the feedback. Format: 'projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}/feedbackThreads/{feedback_thread_id}/feedbackMessages/{feedback_message_id}'.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+/feedbackThreads/[^/]+/feedbackMessages/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1FeedbackMessage"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List FeedbackMessages with pagination.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/annotatedDatasets/{annotatedDatasetsId}/feedbackThreads/{feedbackThreadsId}/feedbackMessages", "httpMethod": "GET", "id": "datalabeling.projects.datasets.annotatedDatasets.feedbackThreads.feedbackMessages.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListFeedbackMessages.next_page_token of the previous [DataLabelingService.ListFeedbackMessages] call. Return first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. FeedbackMessage resource parent. Format: \"projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}/feedbackThreads/{feedback_thread_id}\"", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/annotatedDatasets/[^/]+/feedbackThreads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/feedbackMessages", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListFeedbackMessagesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "dataItems": {"methods": {"get": {"description": "Gets a data item in a dataset by resource name. This API can be called after data are imported into dataset.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/dataItems/{dataItemsId}", "httpMethod": "GET", "id": "datalabeling.projects.datasets.dataItems.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the data item to get, format: projects/{project_id}/datasets/{dataset_id}/dataItems/{data_item_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/dataItems/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1DataItem"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists data items in a dataset. This API can be called after data are imported into dataset. Pagination is supported.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/dataItems", "httpMethod": "GET", "id": "datalabeling.projects.datasets.dataItems.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter is not supported at this moment.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListDataItemsResponse.next_page_token of the previous [DataLabelingService.ListDataItems] call. Return first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the dataset to list data items, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/dataItems", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListDataItemsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "evaluations": {"methods": {"get": {"description": " Gets an evaluation by resource name (to search, use projects.evaluations.search).", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/evaluations/{evaluationsId}", "httpMethod": "GET", "id": "datalabeling.projects.datasets.evaluations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the evaluation. Format: \"projects/{project_id}/datasets/ {dataset_id}/evaluations/{evaluation_id}'", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/evaluations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1Evaluation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"exampleComparisons": {"methods": {"search": {"description": "Searches example comparisons from an evaluation. The return format is a list of example comparisons that show ground truth and prediction(s) for a single input. Search by providing an evaluation ID.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/evaluations/{evaluationsId}/exampleComparisons:search", "httpMethod": "POST", "id": "datalabeling.projects.datasets.evaluations.exampleComparisons.search", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the Evaluation resource to search for example comparisons from. Format: \"projects/{project_id}/datasets/{dataset_id}/evaluations/ {evaluation_id}\"", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/evaluations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/exampleComparisons:search", "request": {"$ref": "GoogleCloudDatalabelingV1beta1SearchExampleComparisonsRequest"}, "response": {"$ref": "GoogleCloudDatalabelingV1beta1SearchExampleComparisonsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "image": {"methods": {"label": {"description": " Starts a labeling task for image. The type of image labeling task is configured by feature in the request.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/image:label", "httpMethod": "POST", "id": "datalabeling.projects.datasets.image.label", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the dataset to request labeling task, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/image:label", "request": {"$ref": "GoogleCloudDatalabelingV1beta1LabelImageRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "text": {"methods": {"label": {"description": "Starts a labeling task for text. The type of text labeling task is configured by feature in the request.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/text:label", "httpMethod": "POST", "id": "datalabeling.projects.datasets.text.label", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the data set to request labeling task, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/text:label", "request": {"$ref": "GoogleCloudDatalabelingV1beta1LabelTextRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "video": {"methods": {"label": {"description": "Starts a labeling task for video. The type of video labeling task is configured by feature in the request.", "flatPath": "v1beta1/projects/{projectsId}/datasets/{datasetsId}/video:label", "httpMethod": "POST", "id": "datalabeling.projects.datasets.video.label", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the dataset to request labeling task, format: projects/{project_id}/datasets/{dataset_id}", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/video:label", "request": {"$ref": "GoogleCloudDatalabelingV1beta1LabelVideoRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "evaluationJobs": {"methods": {"create": {"description": " Creates an evaluation job.", "flatPath": "v1beta1/projects/{projectsId}/evaluationJobs", "httpMethod": "POST", "id": "datalabeling.projects.evaluationJobs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Evaluation job resource parent. Format: \"projects/{project_id}\"", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/evaluationJobs", "request": {"$ref": "GoogleCloudDatalabelingV1beta1CreateEvaluationJobRequest"}, "response": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationJob"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Stops and deletes an evaluation job.", "flatPath": "v1beta1/projects/{projectsId}/evaluationJobs/{evaluationJobsId}", "httpMethod": "DELETE", "id": "datalabeling.projects.evaluationJobs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the evaluation job that is going to be deleted. Format: \"projects/{project_id}/evaluationJobs/{evaluation_job_id}\"", "location": "path", "pattern": "^projects/[^/]+/evaluationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an evaluation job by resource name.", "flatPath": "v1beta1/projects/{projectsId}/evaluationJobs/{evaluationJobsId}", "httpMethod": "GET", "id": "datalabeling.projects.evaluationJobs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the evaluation job. Format: \"projects/{project_id} /evaluationJobs/{evaluation_job_id}\"", "location": "path", "pattern": "^projects/[^/]+/evaluationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationJob"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all evaluation jobs within a project with possible filters. Pagination is supported.", "flatPath": "v1beta1/projects/{projectsId}/evaluationJobs", "httpMethod": "GET", "id": "datalabeling.projects.evaluationJobs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. You can filter the jobs to list by model_id (also known as model_name, as described in EvaluationJob.modelVersion) or by evaluation job state (as described in EvaluationJob.state). To filter by both criteria, use the `AND` operator or the `OR` operator. For example, you can use the following string for your filter: \"evaluation_job.model_id = {model_name} AND evaluation_job.state = {evaluation_job_state}\"", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by the nextPageToken in the response to the previous request. The request returns the first page if this is empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. Evaluation job resource parent. Format: \"projects/{project_id}\"", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/evaluationJobs", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListEvaluationJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an evaluation job. You can only update certain fields of the job's EvaluationJobConfig: `humanAnnotationConfig.instruction`, `exampleCount`, and `exampleSamplePercentage`. If you want to change any other aspect of the evaluation job, you must delete the job and create a new one.", "flatPath": "v1beta1/projects/{projectsId}/evaluationJobs/{evaluationJobsId}", "httpMethod": "PATCH", "id": "datalabeling.projects.evaluationJobs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. After you create a job, Data Labeling Service assigns a name to the job with the following format: \"projects/{project_id}/evaluationJobs/ {evaluation_job_id}\"", "location": "path", "pattern": "^projects/[^/]+/evaluationJobs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Mask for which fields to update. You can only provide the following fields: * `evaluationJobConfig.humanAnnotationConfig.instruction` * `evaluationJobConfig.exampleCount` * `evaluationJobConfig.exampleSamplePercentage` You can provide more than one of these fields by separating them with commas.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationJob"}, "response": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationJob"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "pause": {"description": "Pauses an evaluation job. Pausing an evaluation job that is already in a `PAUSED` state is a no-op.", "flatPath": "v1beta1/projects/{projectsId}/evaluationJobs/{evaluationJobsId}:pause", "httpMethod": "POST", "id": "datalabeling.projects.evaluationJobs.pause", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the evaluation job that is going to be paused. Format: \"projects/{project_id}/evaluationJobs/{evaluation_job_id}\"", "location": "path", "pattern": "^projects/[^/]+/evaluationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:pause", "request": {"$ref": "GoogleCloudDatalabelingV1beta1PauseEvaluationJobRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resume": {"description": "Resumes a paused evaluation job. A deleted evaluation job can't be resumed. Resuming a running or scheduled evaluation job is a no-op.", "flatPath": "v1beta1/projects/{projectsId}/evaluationJobs/{evaluationJobsId}:resume", "httpMethod": "POST", "id": "datalabeling.projects.evaluationJobs.resume", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the evaluation job that is going to be resumed. Format: \"projects/{project_id}/evaluationJobs/{evaluation_job_id}\"", "location": "path", "pattern": "^projects/[^/]+/evaluationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:resume", "request": {"$ref": "GoogleCloudDatalabelingV1beta1ResumeEvaluationJobRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "evaluations": {"methods": {"search": {"description": "Searches evaluations within a project.", "flatPath": "v1beta1/projects/{projectsId}/evaluations:search", "httpMethod": "GET", "id": "datalabeling.projects.evaluations.search", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. To search evaluations, you can filter by the following: * evaluation_job.evaluation_job_id (the last part of EvaluationJob.name) * evaluation_job.model_id (the {model_name} portion of EvaluationJob.modelVersion) * evaluation_job.evaluation_job_run_time_start (Minimum threshold for the evaluationJobRunTime that created the evaluation) * evaluation_job.evaluation_job_run_time_end (Maximum threshold for the evaluationJobRunTime that created the evaluation) * evaluation_job.job_state (EvaluationJob.state) * annotation_spec.display_name (the Evaluation contains a metric for the annotation spec with this displayName) To filter by multiple critiera, use the `AND` operator or the `OR` operator. The following examples shows a string that filters by several critiera: \"evaluation_job.evaluation_job_id = {evaluation_job_id} AND evaluation_job.model_id = {model_name} AND evaluation_job.evaluation_job_run_time_start = {timestamp_1} AND evaluation_job.evaluation_job_run_time_end = {timestamp_2} AND annotation_spec.display_name = {display_name}\"", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by the nextPageToken of the response to a previous search request. If you don't specify this field, the API call requests the first page of the search.", "location": "query", "type": "string"}, "parent": {"description": "Required. Evaluation search parent (project ID). Format: \"projects/ {project_id}\"", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/evaluations:search", "response": {"$ref": "GoogleCloudDatalabelingV1beta1SearchEvaluationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "instructions": {"methods": {"create": {"description": "Creates an instruction for how data should be labeled.", "flatPath": "v1beta1/projects/{projectsId}/instructions", "httpMethod": "POST", "id": "datalabeling.projects.instructions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Instruction resource parent, format: projects/{project_id}", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/instructions", "request": {"$ref": "GoogleCloudDatalabelingV1beta1CreateInstructionRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an instruction object by resource name.", "flatPath": "v1beta1/projects/{projectsId}/instructions/{instructionsId}", "httpMethod": "DELETE", "id": "datalabeling.projects.instructions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Instruction resource name, format: projects/{project_id}/instructions/{instruction_id}", "location": "path", "pattern": "^projects/[^/]+/instructions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an instruction by resource name.", "flatPath": "v1beta1/projects/{projectsId}/instructions/{instructionsId}", "httpMethod": "GET", "id": "datalabeling.projects.instructions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Instruction resource name, format: projects/{project_id}/instructions/{instruction_id}", "location": "path", "pattern": "^projects/[^/]+/instructions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatalabelingV1beta1Instruction"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists instructions for a project. Pagination is supported.", "flatPath": "v1beta1/projects/{projectsId}/instructions", "httpMethod": "GET", "id": "datalabeling.projects.instructions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter is not supported at this moment.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by ListInstructionsResponse.next_page_token of the previous [DataLabelingService.ListInstructions] call. Return first page if empty.", "location": "query", "type": "string"}, "parent": {"description": "Required. Instruction resource parent, format: projects/{project_id}", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/instructions", "response": {"$ref": "GoogleCloudDatalabelingV1beta1ListInstructionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1beta1/projects/{projectsId}/operations/{operationsId}:cancel", "httpMethod": "GET", "id": "datalabeling.projects.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:cancel", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "datalabeling.projects.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/operations/{operationsId}", "httpMethod": "GET", "id": "datalabeling.projects.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/operations", "httpMethod": "GET", "id": "datalabeling.projects.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}, "revision": "20250127", "rootUrl": "https://datalabeling.googleapis.com/", "schemas": {"GoogleCloudDatalabelingV1alpha1CreateInstructionMetadata": {"description": "Metadata of a CreateInstruction operation.", "id": "GoogleCloudDatalabelingV1alpha1CreateInstructionMetadata", "properties": {"createTime": {"description": "Timestamp when create instruction request was created.", "format": "google-datetime", "type": "string"}, "instruction": {"description": "The name of the created Instruction. projects/{project_id}/instructions/{instruction_id}", "type": "string"}, "partialFailures": {"description": "Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1ExportDataOperationMetadata": {"description": "Metadata of an ExportData operation.", "id": "GoogleCloudDatalabelingV1alpha1ExportDataOperationMetadata", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "createTime": {"description": "Output only. Timestamp when export dataset request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of dataset to be exported. \"projects/*/datasets/*\"", "type": "string"}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1ExportDataOperationResponse": {"description": "Response used for ExportDataset longrunning operation.", "id": "GoogleCloudDatalabelingV1alpha1ExportDataOperationResponse", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "dataset": {"description": "Ouptut only. The name of dataset. \"projects/*/datasets/*\"", "type": "string"}, "exportCount": {"description": "Output only. Number of examples exported successfully.", "format": "int32", "type": "integer"}, "labelStats": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelStats", "description": "Output only. Statistic infos of labels in the exported dataset."}, "outputConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1OutputConfig", "description": "Output only. output_config in the ExportData request."}, "totalCount": {"description": "Output only. Total number of examples requested to export", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1GcsDestination": {"description": "Export destination of the data.Only gcs path is allowed in output_uri.", "id": "GoogleCloudDatalabelingV1alpha1GcsDestination", "properties": {"mimeType": {"description": "Required. The format of the gcs destination. Only \"text/csv\" and \"application/json\" are supported.", "type": "string"}, "outputUri": {"description": "Required. The output uri of destination file.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1GcsFolderDestination": {"description": "Export folder destination of the data.", "id": "GoogleCloudDatalabelingV1alpha1GcsFolderDestination", "properties": {"outputFolderUri": {"description": "Required. Cloud Storage directory to export data to.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig": {"description": "Configuration for how human labeling task should be done.", "id": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "properties": {"annotatedDatasetDescription": {"description": "Optional. A human-readable description for AnnotatedDataset. The description can be up to 10000 characters long.", "type": "string"}, "annotatedDatasetDisplayName": {"description": "Required. A human-readable name for AnnotatedDataset defined by users. Maximum of 64 characters .", "type": "string"}, "contributorEmails": {"description": "Optional. If you want your own labeling contributors to manage and work on this labeling request, you can set these contributors here. We will give them access to the question types in crowdcompute. Note that these emails must be registered in crowdcompute worker UI: https://crowd-compute.appspot.com/", "items": {"type": "string"}, "type": "array"}, "instruction": {"description": "Required. Instruction resource name.", "type": "string"}, "labelGroup": {"description": "Optional. A human-readable label used to logically group labeling tasks. This string must match the regular expression `[a-zA-Z\\\\d_-]{0,128}`.", "type": "string"}, "languageCode": {"description": "Optional. The Language of this question, as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). Default value is en-US. Only need to set this when task is language related. For example, French text classification.", "type": "string"}, "questionDuration": {"description": "Optional. Maximum duration for contributors to answer a question. Maximum is 3600 seconds. Default is 3600 seconds.", "format": "google-duration", "type": "string"}, "replicaCount": {"description": "Optional. Replication of questions. Each question will be sent to up to this number of contributors to label. Aggregated answers will be returned. Default is set to 1. For image related labeling, valid values are 1, 3, 5.", "format": "int32", "type": "integer"}, "userEmailAddress": {"description": "Email of the user who started the labeling task and should be notified by email. If empty no notification will be sent.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1ImportDataOperationMetadata": {"description": "Metadata of an ImportData operation.", "id": "GoogleCloudDatalabelingV1alpha1ImportDataOperationMetadata", "properties": {"createTime": {"description": "Output only. Timestamp when import dataset request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of imported dataset. \"projects/*/datasets/*\"", "type": "string"}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1ImportDataOperationResponse": {"description": "Response used for ImportData longrunning operation.", "id": "GoogleCloudDatalabelingV1alpha1ImportDataOperationResponse", "properties": {"dataset": {"description": "Ouptut only. The name of imported dataset.", "type": "string"}, "importCount": {"description": "Output only. Number of examples imported successfully.", "format": "int32", "type": "integer"}, "totalCount": {"description": "Output only. Total number of examples requested to import", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelImageBoundingBoxOperationMetadata": {"description": "Details of a LabelImageBoundingBox operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelImageBoundingBoxOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelImageBoundingPolyOperationMetadata": {"description": "Details of LabelImageBoundingPoly operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelImageBoundingPolyOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelImageClassificationOperationMetadata": {"description": "Metadata of a LabelImageClassification operation.", "id": "GoogleCloudDatalabelingV1alpha1LabelImageClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelImageOrientedBoundingBoxOperationMetadata": {"description": "Details of a LabelImageOrientedBoundingBox operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelImageOrientedBoundingBoxOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelImagePolylineOperationMetadata": {"description": "Details of LabelImagePolyline operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelImagePolylineOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelImageSegmentationOperationMetadata": {"description": "Details of a LabelImageSegmentation operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelImageSegmentationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelOperationMetadata": {"description": "Metadata of a labeling operation, such as LabelImage or LabelVideo. Next tag: 23", "id": "GoogleCloudDatalabelingV1alpha1LabelOperationMetadata", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "createTime": {"description": "Output only. Timestamp when labeling request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of dataset to be labeled. \"projects/*/datasets/*\"", "type": "string"}, "imageBoundingBoxDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelImageBoundingBoxOperationMetadata", "description": "Details of label image bounding box operation."}, "imageBoundingPolyDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelImageBoundingPolyOperationMetadata", "description": "Details of label image bounding poly operation."}, "imageClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelImageClassificationOperationMetadata", "description": "Details of label image classification operation."}, "imageOrientedBoundingBoxDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelImageOrientedBoundingBoxOperationMetadata", "description": "Details of label image oriented bounding box operation."}, "imagePolylineDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelImagePolylineOperationMetadata", "description": "Details of label image polyline operation."}, "imageSegmentationDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelImageSegmentationOperationMetadata", "description": "Details of label image segmentation operation."}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "progressPercent": {"description": "Output only. Progress of label operation. Range: [0, 100].", "format": "int32", "type": "integer"}, "textClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelTextClassificationOperationMetadata", "description": "Details of label text classification operation."}, "textEntityExtractionDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelTextEntityExtractionOperationMetadata", "description": "Details of label text entity extraction operation."}, "videoClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelVideoClassificationOperationMetadata", "description": "Details of label video classification operation."}, "videoEventDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelVideoEventOperationMetadata", "description": "Details of label video event operation."}, "videoObjectDetectionDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelVideoObjectDetectionOperationMetadata", "description": "Details of label video object detection operation."}, "videoObjectTrackingDetails": {"$ref": "GoogleCloudDatalabelingV1alpha1LabelVideoObjectTrackingOperationMetadata", "description": "Details of label video object tracking operation."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelStats": {"description": "Statistics about annotation specs.", "id": "GoogleCloudDatalabelingV1alpha1LabelStats", "properties": {"exampleCount": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Map of each annotation spec's example count. Key is the annotation spec name and value is the number of examples for that annotation spec. If the annotated dataset does not have annotation spec, the map will return a pair where the key is empty string and value is the total number of annotations.", "type": "object"}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelTextClassificationOperationMetadata": {"description": "Details of a LabelTextClassification operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelTextClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelTextEntityExtractionOperationMetadata": {"description": "Details of a LabelTextEntityExtraction operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelTextEntityExtractionOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelVideoClassificationOperationMetadata": {"description": "Details of a LabelVideoClassification operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelVideoClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelVideoEventOperationMetadata": {"description": "Details of a LabelVideoEvent operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelVideoEventOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelVideoObjectDetectionOperationMetadata": {"description": "Details of a LabelVideoObjectDetection operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelVideoObjectDetectionOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1LabelVideoObjectTrackingOperationMetadata": {"description": "Details of a LabelVideoObjectTracking operation metadata.", "id": "GoogleCloudDatalabelingV1alpha1LabelVideoObjectTrackingOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1alpha1OutputConfig": {"description": "The configuration of output data.", "id": "GoogleCloudDatalabelingV1alpha1OutputConfig", "properties": {"gcsDestination": {"$ref": "GoogleCloudDatalabelingV1alpha1GcsDestination", "description": "Output to a file in Cloud Storage. Should be used for labeling output other than image segmentation."}, "gcsFolderDestination": {"$ref": "GoogleCloudDatalabelingV1alpha1GcsFolderDestination", "description": "Output to a folder in Cloud Storage. Should be used for image segmentation or document de-identification labeling outputs."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1AnnotatedDataset": {"description": "AnnotatedDataset is a set holding annotations for data in a Dataset. Each labeling task will generate an AnnotatedDataset under the Dataset that the task is requested for.", "id": "GoogleCloudDatalabelingV1beta1AnnotatedDataset", "properties": {"annotationSource": {"description": "Output only. Source of the annotation.", "enum": ["ANNOTATION_SOURCE_UNSPECIFIED", "OPERATOR"], "enumDescriptions": ["", "Answer is provided by a human contributor."], "type": "string"}, "annotationType": {"description": "Output only. Type of the annotation. It is specified when starting labeling task.", "enum": ["ANNOTATION_TYPE_UNSPECIFIED", "IMAGE_CLASSIFICATION_ANNOTATION", "IMAGE_BOUNDING_BOX_ANNOTATION", "IMAGE_ORIENTED_BOUNDING_BOX_ANNOTATION", "IMAGE_BOUNDING_POLY_ANNOTATION", "IMAGE_POLYLINE_ANNOTATION", "IMAGE_SEGMENTATION_ANNOTATION", "VIDEO_SHOTS_CLASSIFICATION_ANNOTATION", "VIDEO_OBJECT_TRACKING_ANNOTATION", "VIDEO_OBJECT_DETECTION_ANNOTATION", "VIDEO_EVENT_ANNOTATION", "TEXT_CLASSIFICATION_ANNOTATION", "TEXT_ENTITY_EXTRACTION_ANNOTATION", "GENERAL_CLASSIFICATION_ANNOTATION"], "enumDescriptions": ["", "Classification annotations in an image. Allowed for continuous evaluation.", "Bounding box annotations in an image. A form of image object detection. Allowed for continuous evaluation.", "Oriented bounding box. The box does not have to be parallel to horizontal line.", "Bounding poly annotations in an image.", "Polyline annotations in an image.", "Segmentation annotations in an image.", "Classification annotations in video shots.", "Video object tracking annotation.", "Video object detection annotation.", "Video event annotation.", "Classification for text. Allowed for continuous evaluation.", "Entity extraction for text.", "General classification. Allowed for continuous evaluation."], "type": "string"}, "blockingResources": {"description": "Output only. The names of any related resources that are blocking changes to the annotated dataset.", "items": {"type": "string"}, "type": "array"}, "completedExampleCount": {"description": "Output only. Number of examples that have annotation in the annotated dataset.", "format": "int64", "type": "string"}, "createTime": {"description": "Output only. Time the AnnotatedDataset was created.", "format": "google-datetime", "type": "string"}, "description": {"description": "Output only. The description of the AnnotatedDataset. It is specified in HumanAnnotationConfig when user starts a labeling task. Maximum of 10000 characters.", "type": "string"}, "displayName": {"description": "Output only. The display name of the AnnotatedDataset. It is specified in HumanAnnotationConfig when user starts a labeling task. Maximum of 64 characters.", "type": "string"}, "exampleCount": {"description": "Output only. Number of examples in the annotated dataset.", "format": "int64", "type": "string"}, "labelStats": {"$ref": "GoogleCloudDatalabelingV1beta1LabelStats", "description": "Output only. Per label statistics."}, "metadata": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotatedDatasetMetadata", "description": "Output only. Additional information about AnnotatedDataset."}, "name": {"description": "Output only. AnnotatedDataset resource name in format of: projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/ {annotated_dataset_id}", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1AnnotatedDatasetMetadata": {"description": "Metadata on AnnotatedDataset.", "id": "GoogleCloudDatalabelingV1beta1AnnotatedDatasetMetadata", "properties": {"boundingPolyConfig": {"$ref": "GoogleCloudDatalabelingV1beta1BoundingPolyConfig", "description": "Configuration for image bounding box and bounding poly task."}, "eventConfig": {"$ref": "GoogleCloudDatalabelingV1beta1EventConfig", "description": "Configuration for video event labeling task."}, "humanAnnotationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "HumanAnnotationConfig used when requesting the human labeling task for this AnnotatedDataset."}, "imageClassificationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1ImageClassificationConfig", "description": "Configuration for image classification task."}, "objectDetectionConfig": {"$ref": "GoogleCloudDatalabelingV1beta1ObjectDetectionConfig", "description": "Configuration for video object detection task."}, "objectTrackingConfig": {"$ref": "GoogleCloudDatalabelingV1beta1ObjectTrackingConfig", "description": "Configuration for video object tracking task."}, "polylineConfig": {"$ref": "GoogleCloudDatalabelingV1beta1PolylineConfig", "description": "Configuration for image polyline task."}, "segmentationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1SegmentationConfig", "description": "Configuration for image segmentation task."}, "textClassificationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1TextClassificationConfig", "description": "Configuration for text classification task."}, "textEntityExtractionConfig": {"$ref": "GoogleCloudDatalabelingV1beta1TextEntityExtractionConfig", "description": "Configuration for text entity extraction task."}, "videoClassificationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1VideoClassificationConfig", "description": "Configuration for video classification task."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Annotation": {"description": "Annotation for Example. Each example may have one or more annotations. For example in image classification problem, each image might have one or more labels. We call labels binded with this image an Annotation.", "id": "GoogleCloudDatalabelingV1beta1Annotation", "properties": {"annotationMetadata": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationMetadata", "description": "Output only. Annotation metadata, including information like votes for labels."}, "annotationSentiment": {"description": "Output only. Sentiment for this annotation.", "enum": ["ANNOTATION_SENTIMENT_UNSPECIFIED", "NEGATIVE", "POSITIVE"], "enumDescriptions": ["", "This annotation describes negatively about the data.", "This label describes positively about the data."], "type": "string"}, "annotationSource": {"description": "Output only. The source of the annotation.", "enum": ["ANNOTATION_SOURCE_UNSPECIFIED", "OPERATOR"], "enumDescriptions": ["", "Answer is provided by a human contributor."], "type": "string"}, "annotationValue": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationValue", "description": "Output only. This is the actual annotation value, e.g classification, bounding box values are stored here."}, "name": {"description": "Output only. Unique name of this annotation, format is: projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset}/examples/{example_id}/annotations/{annotation_id}", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1AnnotationMetadata": {"description": "Additional information associated with the annotation.", "id": "GoogleCloudDatalabelingV1beta1AnnotationMetadata", "properties": {"operatorMetadata": {"$ref": "GoogleCloudDatalabelingV1beta1OperatorMetadata", "description": "Metadata related to human labeling."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1AnnotationSpec": {"description": "Container of information related to one possible annotation that can be used in a labeling task. For example, an image classification task where images are labeled as `dog` or `cat` must reference an AnnotationSpec for `dog` and an AnnotationSpec for `cat`.", "id": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "properties": {"description": {"description": "Optional. User-provided description of the annotation specification. The description can be up to 10,000 characters long.", "type": "string"}, "displayName": {"description": "Required. The display name of the AnnotationSpec. Maximum of 64 characters.", "type": "string"}, "index": {"description": "Output only. This is the integer index of the AnnotationSpec. The index for the whole AnnotationSpecSet is sequential starting from 0. For example, an AnnotationSpecSet with classes `dog` and `cat`, might contain one AnnotationSpec with `{ display_name: \"dog\", index: 0 }` and one AnnotationSpec with `{ display_name: \"cat\", index: 1 }`. This is especially useful for model training as it encodes the string labels into numeric values.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1AnnotationSpecSet": {"description": "An AnnotationSpecSet is a collection of label definitions. For example, in image classification tasks, you define a set of possible labels for images as an AnnotationSpecSet. An AnnotationSpecSet is immutable upon creation.", "id": "GoogleCloudDatalabelingV1beta1AnnotationSpecSet", "properties": {"annotationSpecs": {"description": "Required. The array of AnnotationSpecs that you define when you create the AnnotationSpecSet. These are the possible labels for the labeling task.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec"}, "type": "array"}, "blockingResources": {"description": "Output only. The names of any related resources that are blocking changes to the annotation spec set.", "items": {"type": "string"}, "type": "array"}, "description": {"description": "Optional. User-provided description of the annotation specification set. The description can be up to 10,000 characters long.", "type": "string"}, "displayName": {"description": "Required. The display name for AnnotationSpecSet that you define when you create it. Maximum of 64 characters.", "type": "string"}, "name": {"description": "Output only. The AnnotationSpecSet resource name in the following format: \"projects/{project_id}/annotationSpecSets/{annotation_spec_set_id}\"", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1AnnotationSpecSetConfig": {"description": "Annotation spec set with the setting of allowing multi labels or not.", "id": "GoogleCloudDatalabelingV1beta1AnnotationSpecSetConfig", "properties": {"allowMultiLabel": {"description": "Optional. If allow_multi_label is true, contributors are able to choose multiple labels from one annotation spec set.", "type": "boolean"}, "annotationSpecSet": {"description": "Required. Annotation spec set resource name.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1AnnotationValue": {"description": "Annotation value for an example.", "id": "GoogleCloudDatalabelingV1beta1AnnotationValue", "properties": {"imageBoundingPolyAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1ImageBoundingPolyAnnotation", "description": "Annotation value for image bounding box, oriented bounding box and polygon cases."}, "imageClassificationAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1ImageClassificationAnnotation", "description": "Annotation value for image classification case."}, "imagePolylineAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1ImagePolylineAnnotation", "description": "Annotation value for image polyline cases. Polyline here is different from BoundingPoly. It is formed by line segments connected to each other but not closed form(Bounding Poly). The line segments can cross each other."}, "imageSegmentationAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1ImageSegmentationAnnotation", "description": "Annotation value for image segmentation."}, "textClassificationAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1TextClassificationAnnotation", "description": "Annotation value for text classification case."}, "textEntityExtractionAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1TextEntityExtractionAnnotation", "description": "Annotation value for text entity extraction case."}, "videoClassificationAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1VideoClassificationAnnotation", "description": "Annotation value for video classification case."}, "videoEventAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1VideoEventAnnotation", "description": "Annotation value for video event case."}, "videoObjectTrackingAnnotation": {"$ref": "GoogleCloudDatalabelingV1beta1VideoObjectTrackingAnnotation", "description": "Annotation value for video object detection and tracking case."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Attempt": {"description": "Records a failed evaluation job run.", "id": "GoogleCloudDatalabelingV1beta1Attempt", "properties": {"attemptTime": {"format": "google-datetime", "type": "string"}, "partialFailures": {"description": "Details of errors that occurred.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1BigQuerySource": {"description": "The BigQuery location for input data. If used in an EvaluationJob, this is where the service saves the prediction input and output sampled from the model version.", "id": "GoogleCloudDatalabelingV1beta1BigQuerySource", "properties": {"inputUri": {"description": "Required. BigQuery URI to a table, up to 2,000 characters long. If you specify the URI of a table that does not exist, Data Labeling Service creates a table at the URI with the correct schema when you create your EvaluationJob. If you specify the URI of a table that already exists, it must have the [correct schema](/ml-engine/docs/continuous-evaluation/create-job#table-schema). Provide the table URI in the following format: \"bq://{your_project_id}/ {your_dataset_name}/{your_table_name}\" [Learn more](/ml-engine/docs/continuous-evaluation/create-job#table-schema).", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1BoundingBoxEvaluationOptions": {"description": "Options regarding evaluation between bounding boxes.", "id": "GoogleCloudDatalabelingV1beta1BoundingBoxEvaluationOptions", "properties": {"iouThreshold": {"description": "Minimum [intersection-over-union (IOU)](/vision/automl/object-detection/docs/evaluate#intersection-over-union) required for 2 bounding boxes to be considered a match. This must be a number between 0 and 1.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1BoundingPoly": {"description": "A bounding polygon in the image.", "id": "GoogleCloudDatalabelingV1beta1BoundingPoly", "properties": {"vertices": {"description": "The bounding polygon vertices.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Vertex"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1BoundingPolyConfig": {"description": "Config for image bounding poly (and bounding box) human labeling task.", "id": "GoogleCloudDatalabelingV1beta1BoundingPolyConfig", "properties": {"annotationSpecSet": {"description": "Required. Annotation spec set resource name.", "type": "string"}, "instructionMessage": {"description": "Optional. Instruction message showed on contributors UI.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ClassificationMetadata": {"description": "Metadata for classification annotations.", "id": "GoogleCloudDatalabelingV1beta1ClassificationMetadata", "properties": {"isMultiLabel": {"description": "Whether the classification task is multi-label or not.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ClassificationMetrics": {"description": "Metrics calculated for a classification model.", "id": "GoogleCloudDatalabelingV1beta1ClassificationMetrics", "properties": {"confusionMatrix": {"$ref": "GoogleCloudDatalabelingV1beta1ConfusionMatrix", "description": "Confusion matrix of predicted labels vs. ground truth labels."}, "prCurve": {"$ref": "GoogleCloudDatalabelingV1beta1PrCurve", "description": "Precision-recall curve based on ground truth labels, predicted labels, and scores for the predicted labels."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ConfidenceMetricsEntry": {"id": "GoogleCloudDatalabelingV1beta1ConfidenceMetricsEntry", "properties": {"confidenceThreshold": {"description": "Threshold used for this entry. For classification tasks, this is a classification threshold: a predicted label is categorized as positive or negative (in the context of this point on the PR curve) based on whether the label's score meets this threshold. For image object detection (bounding box) tasks, this is the [intersection-over-union (IOU)](/vision/automl/object-detection/docs/evaluate#intersection-over-union) threshold for the context of this point on the PR curve.", "format": "float", "type": "number"}, "f1Score": {"description": "Harmonic mean of recall and precision.", "format": "float", "type": "number"}, "f1ScoreAt1": {"description": "The harmonic mean of recall_at1 and precision_at1.", "format": "float", "type": "number"}, "f1ScoreAt5": {"description": "The harmonic mean of recall_at5 and precision_at5.", "format": "float", "type": "number"}, "precision": {"description": "Precision value.", "format": "float", "type": "number"}, "precisionAt1": {"description": "Precision value for entries with label that has highest score.", "format": "float", "type": "number"}, "precisionAt5": {"description": "Precision value for entries with label that has highest 5 scores.", "format": "float", "type": "number"}, "recall": {"description": "Recall value.", "format": "float", "type": "number"}, "recallAt1": {"description": "Recall value for entries with label that has highest score.", "format": "float", "type": "number"}, "recallAt5": {"description": "Recall value for entries with label that has highest 5 scores.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ConfusionMatrix": {"description": "Confusion matrix of the model running the classification. Only applicable when the metrics entry aggregates multiple labels. Not applicable when the entry is for a single label.", "id": "GoogleCloudDatalabelingV1beta1ConfusionMatrix", "properties": {"row": {"items": {"$ref": "GoogleCloudDatalabelingV1beta1Row"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ConfusionMatrixEntry": {"id": "GoogleCloudDatalabelingV1beta1ConfusionMatrixEntry", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "The annotation spec of a predicted label."}, "itemCount": {"description": "Number of items predicted to have this label. (The ground truth label for these items is the `Row.annotationSpec` of this entry's parent.)", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1CreateAnnotationSpecSetRequest": {"description": "Request message for CreateAnnotationSpecSet.", "id": "GoogleCloudDatalabelingV1beta1CreateAnnotationSpecSetRequest", "properties": {"annotationSpecSet": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpecSet", "description": "Required. Annotation spec set to create. Annotation specs must be included. Only one annotation spec will be accepted for annotation specs with same display_name."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1CreateDatasetRequest": {"description": "Request message for CreateDataset.", "id": "GoogleCloudDatalabelingV1beta1CreateDatasetRequest", "properties": {"dataset": {"$ref": "GoogleCloudDatalabelingV1beta1Dataset", "description": "Required. The dataset to be created."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1CreateEvaluationJobRequest": {"description": " Request message for CreateEvaluationJob.", "id": "GoogleCloudDatalabelingV1beta1CreateEvaluationJobRequest", "properties": {"job": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationJob", "description": "Required. The evaluation job to create."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1CreateInstructionMetadata": {"description": "Metadata of a CreateInstruction operation.", "id": "GoogleCloudDatalabelingV1beta1CreateInstructionMetadata", "properties": {"createTime": {"description": "Timestamp when create instruction request was created.", "format": "google-datetime", "type": "string"}, "instruction": {"description": "The name of the created Instruction. projects/{project_id}/instructions/{instruction_id}", "type": "string"}, "partialFailures": {"description": "Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1CreateInstructionRequest": {"description": "Request message for CreateInstruction.", "id": "GoogleCloudDatalabelingV1beta1CreateInstructionRequest", "properties": {"instruction": {"$ref": "GoogleCloudDatalabelingV1beta1Instruction", "description": "Required. Instruction of how to perform the labeling task."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1CsvInstruction": {"description": "Deprecated: this instruction format is not supported any more. Instruction from a CSV file.", "id": "GoogleCloudDatalabelingV1beta1CsvInstruction", "properties": {"gcsFileUri": {"description": "CSV file for the instruction. Only gcs path is allowed.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1DataItem": {"description": "DataItem is a piece of data, without annotation. For example, an image.", "id": "GoogleCloudDatalabelingV1beta1DataItem", "properties": {"imagePayload": {"$ref": "GoogleCloudDatalabelingV1beta1ImagePayload", "description": "The image payload, a container of the image bytes/uri."}, "name": {"description": "Output only. Name of the data item, in format of: projects/{project_id}/datasets/{dataset_id}/dataItems/{data_item_id}", "type": "string"}, "textPayload": {"$ref": "GoogleCloudDatalabelingV1beta1TextPayload", "description": "The text payload, a container of text content."}, "videoPayload": {"$ref": "GoogleCloudDatalabelingV1beta1VideoPayload", "description": "The video payload, a container of the video uri."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Dataset": {"description": "Dataset is the resource to hold your data. You can request multiple labeling tasks for a dataset while each one will generate an AnnotatedDataset.", "id": "GoogleCloudDatalabelingV1beta1Dataset", "properties": {"blockingResources": {"description": "Output only. The names of any related resources that are blocking changes to the dataset.", "items": {"type": "string"}, "type": "array"}, "createTime": {"description": "Output only. Time the dataset is created.", "format": "google-datetime", "type": "string"}, "dataItemCount": {"description": "Output only. The number of data items in the dataset.", "format": "int64", "type": "string"}, "description": {"description": "Optional. User-provided description of the annotation specification set. The description can be up to 10000 characters long.", "type": "string"}, "displayName": {"description": "Required. The display name of the dataset. Maximum of 64 characters.", "type": "string"}, "inputConfigs": {"description": "Output only. This is populated with the original input configs where ImportData is called. It is available only after the clients import data to this dataset.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1InputConfig"}, "type": "array"}, "lastMigrateTime": {"description": "Last time that the Dataset is migrated to AI Platform V2. If any of the AnnotatedDataset is migrated, the last_migration_time in Dataset is also updated.", "format": "google-datetime", "type": "string"}, "name": {"description": "Output only. Dataset resource name, format is: projects/{project_id}/datasets/{dataset_id}", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Evaluation": {"description": "Describes an evaluation between a machine learning model's predictions and ground truth labels. Created when an EvaluationJob runs successfully.", "id": "GoogleCloudDatalabelingV1beta1Evaluation", "properties": {"annotationType": {"description": "Output only. Type of task that the model version being evaluated performs, as defined in the evaluationJobConfig.inputConfig.annotationType field of the evaluation job that created this evaluation.", "enum": ["ANNOTATION_TYPE_UNSPECIFIED", "IMAGE_CLASSIFICATION_ANNOTATION", "IMAGE_BOUNDING_BOX_ANNOTATION", "IMAGE_ORIENTED_BOUNDING_BOX_ANNOTATION", "IMAGE_BOUNDING_POLY_ANNOTATION", "IMAGE_POLYLINE_ANNOTATION", "IMAGE_SEGMENTATION_ANNOTATION", "VIDEO_SHOTS_CLASSIFICATION_ANNOTATION", "VIDEO_OBJECT_TRACKING_ANNOTATION", "VIDEO_OBJECT_DETECTION_ANNOTATION", "VIDEO_EVENT_ANNOTATION", "TEXT_CLASSIFICATION_ANNOTATION", "TEXT_ENTITY_EXTRACTION_ANNOTATION", "GENERAL_CLASSIFICATION_ANNOTATION"], "enumDescriptions": ["", "Classification annotations in an image. Allowed for continuous evaluation.", "Bounding box annotations in an image. A form of image object detection. Allowed for continuous evaluation.", "Oriented bounding box. The box does not have to be parallel to horizontal line.", "Bounding poly annotations in an image.", "Polyline annotations in an image.", "Segmentation annotations in an image.", "Classification annotations in video shots.", "Video object tracking annotation.", "Video object detection annotation.", "Video event annotation.", "Classification for text. Allowed for continuous evaluation.", "Entity extraction for text.", "General classification. Allowed for continuous evaluation."], "type": "string"}, "config": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationConfig", "description": "Output only. Options used in the evaluation job that created this evaluation."}, "createTime": {"description": "Output only. Timestamp for when this evaluation was created.", "format": "google-datetime", "type": "string"}, "evaluatedItemCount": {"description": "Output only. The number of items in the ground truth dataset that were used for this evaluation. Only populated when the evaulation is for certain AnnotationTypes.", "format": "int64", "type": "string"}, "evaluationJobRunTime": {"description": "Output only. Timestamp for when the evaluation job that created this evaluation ran.", "format": "google-datetime", "type": "string"}, "evaluationMetrics": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationMetrics", "description": "Output only. Metrics comparing predictions to ground truth labels."}, "name": {"description": "Output only. Resource name of an evaluation. The name has the following format: \"projects/{project_id}/datasets/{dataset_id}/evaluations/ {evaluation_id}'", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1EvaluationConfig": {"description": "Configuration details used for calculating evaluation metrics and creating an Evaluation.", "id": "GoogleCloudDatalabelingV1beta1EvaluationConfig", "properties": {"boundingBoxEvaluationOptions": {"$ref": "GoogleCloudDatalabelingV1beta1BoundingBoxEvaluationOptions", "description": "Only specify this field if the related model performs image object detection (`IMAGE_BOUNDING_BOX_ANNOTATION`). Describes how to evaluate bounding boxes."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1EvaluationJob": {"description": "Defines an evaluation job that runs periodically to generate Evaluations. [Creating an evaluation job](/ml-engine/docs/continuous-evaluation/create-job) is the starting point for using continuous evaluation.", "id": "GoogleCloudDatalabelingV1beta1EvaluationJob", "properties": {"annotationSpecSet": {"description": "Required. Name of the AnnotationSpecSet describing all the labels that your machine learning model outputs. You must create this resource before you create an evaluation job and provide its name in the following format: \"projects/{project_id}/annotationSpecSets/{annotation_spec_set_id}\"", "type": "string"}, "attempts": {"description": "Output only. Every time the evaluation job runs and an error occurs, the failed attempt is appended to this array.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Attempt"}, "type": "array"}, "createTime": {"description": "Output only. Timestamp of when this evaluation job was created.", "format": "google-datetime", "type": "string"}, "description": {"description": "Required. Description of the job. The description can be up to 25,000 characters long.", "type": "string"}, "evaluationJobConfig": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationJobConfig", "description": "Required. Configuration details for the evaluation job."}, "labelMissingGroundTruth": {"description": "Required. Whether you want Data Labeling Service to provide ground truth labels for prediction input. If you want the service to assign human labelers to annotate your data, set this to `true`. If you want to provide your own ground truth labels in the evaluation job's BigQuery table, set this to `false`.", "type": "boolean"}, "modelVersion": {"description": "Required. The [AI Platform Prediction model version](/ml-engine/docs/prediction-overview) to be evaluated. Prediction input and output is sampled from this model version. When creating an evaluation job, specify the model version in the following format: \"projects/{project_id}/models/{model_name}/versions/{version_name}\" There can only be one evaluation job per model version.", "type": "string"}, "name": {"description": "Output only. After you create a job, Data Labeling Service assigns a name to the job with the following format: \"projects/{project_id}/evaluationJobs/ {evaluation_job_id}\"", "type": "string"}, "schedule": {"description": "Required. Describes the interval at which the job runs. This interval must be at least 1 day, and it is rounded to the nearest day. For example, if you specify a 50-hour interval, the job runs every 2 days. You can provide the schedule in [crontab format](/scheduler/docs/configuring/cron-job-schedules) or in an [English-like format](/appengine/docs/standard/python/config/cronref#schedule_format). Regardless of what you specify, the job will run at 10:00 AM UTC. Only the interval from this schedule is used, not the specific time of day.", "type": "string"}, "state": {"description": "Output only. Describes the current state of the job.", "enum": ["STATE_UNSPECIFIED", "SCHEDULED", "RUNNING", "PAUSED", "STOPPED"], "enumDescriptions": ["", "The job is scheduled to run at the configured interval. You can pause or delete the job. When the job is in this state, it samples prediction input and output from your model version into your BigQuery table as predictions occur.", "The job is currently running. When the job runs, Data Labeling Service does several things: 1. If you have configured your job to use Data Labeling Service for ground truth labeling, the service creates a Dataset and a labeling task for all data sampled since the last time the job ran. Human labelers provide ground truth labels for your data. Human labeling may take hours, or even days, depending on how much data has been sampled. The job remains in the `RUNNING` state during this time, and it can even be running multiple times in parallel if it gets triggered again (for example 24 hours later) before the earlier run has completed. When human labelers have finished labeling the data, the next step occurs. If you have configured your job to provide your own ground truth labels, Data Labeling Service still creates a Dataset for newly sampled data, but it expects that you have already added ground truth labels to the BigQuery table by this time. The next step occurs immediately. 2. Data Labeling Service creates an Evaluation by comparing your model version's predictions with the ground truth labels. If the job remains in this state for a long time, it continues to sample prediction data into your BigQuery table and will run again at the next interval, even if it causes the job to run multiple times in parallel.", "The job is not sampling prediction input and output into your BigQuery table and it will not run according to its schedule. You can resume the job.", "The job has this state right before it is deleted."], "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1EvaluationJobAlertConfig": {"description": "Provides details for how an evaluation job sends email alerts based on the results of a run.", "id": "GoogleCloudDatalabelingV1beta1EvaluationJobAlertConfig", "properties": {"email": {"description": "Required. An email address to send alerts to.", "type": "string"}, "minAcceptableMeanAveragePrecision": {"description": "Required. A number between 0 and 1 that describes a minimum mean average precision threshold. When the evaluation job runs, if it calculates that your model version's predictions from the recent interval have meanAveragePrecision below this threshold, then it sends an alert to your specified email.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1EvaluationJobConfig": {"description": "Configures specific details of how a continuous evaluation job works. Provide this configuration when you create an EvaluationJob.", "id": "GoogleCloudDatalabelingV1beta1EvaluationJobConfig", "properties": {"bigqueryImportKeys": {"additionalProperties": {"type": "string"}, "description": "Required. Prediction keys that tell Data Labeling Service where to find the data for evaluation in your BigQuery table. When the service samples prediction input and output from your model version and saves it to BigQuery, the data gets stored as JSON strings in the BigQuery table. These keys tell Data Labeling Service how to parse the JSON. You can provide the following entries in this field: * `data_json_key`: the data key for prediction input. You must provide either this key or `reference_json_key`. * `reference_json_key`: the data reference key for prediction input. You must provide either this key or `data_json_key`. * `label_json_key`: the label key for prediction output. Required. * `label_score_json_key`: the score key for prediction output. Required. * `bounding_box_json_key`: the bounding box key for prediction output. Required if your model version perform image object detection. Learn [how to configure prediction keys](/ml-engine/docs/continuous-evaluation/create-job#prediction-keys).", "type": "object"}, "boundingPolyConfig": {"$ref": "GoogleCloudDatalabelingV1beta1BoundingPolyConfig", "description": "Specify this field if your model version performs image object detection (bounding box detection). `annotationSpecSet` in this configuration must match EvaluationJob.annotationSpecSet."}, "evaluationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationConfig", "description": "Required. Details for calculating evaluation metrics and creating Evaulations. If your model version performs image object detection, you must specify the `boundingBoxEvaluationOptions` field within this configuration. Otherwise, provide an empty object for this configuration."}, "evaluationJobAlertConfig": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationJobAlertConfig", "description": "Optional. Configuration details for evaluation job alerts. Specify this field if you want to receive email alerts if the evaluation job finds that your predictions have low mean average precision during a run."}, "exampleCount": {"description": "Required. The maximum number of predictions to sample and save to BigQuery during each evaluation interval. This limit overrides `example_sample_percentage`: even if the service has not sampled enough predictions to fulfill `example_sample_perecentage` during an interval, it stops sampling predictions when it meets this limit.", "format": "int32", "type": "integer"}, "exampleSamplePercentage": {"description": "Required. Fraction of predictions to sample and save to BigQuery during each evaluation interval. For example, 0.1 means 10% of predictions served by your model version get saved to BigQuery.", "format": "double", "type": "number"}, "humanAnnotationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Optional. Details for human annotation of your data. If you set labelMissingGroundTruth to `true` for this evaluation job, then you must specify this field. If you plan to provide your own ground truth labels, then omit this field. Note that you must create an Instruction resource before you can specify this field. Provide the name of the instruction resource in the `instruction` field within this configuration."}, "imageClassificationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1ImageClassificationConfig", "description": "Specify this field if your model version performs image classification or general classification. `annotationSpecSet` in this configuration must match EvaluationJob.annotationSpecSet. `allowMultiLabel` in this configuration must match `classificationMetadata.isMultiLabel` in input_config."}, "inputConfig": {"$ref": "GoogleCloudDatalabelingV1beta1InputConfig", "description": "Rquired. Details for the sampled prediction input. Within this configuration, there are requirements for several fields: * `dataType` must be one of `IMAGE`, `TEXT`, or `GENERAL_DATA`. * `annotationType` must be one of `IMAGE_CLASSIFICATION_ANNOTATION`, `TEXT_CLASSIFICATION_ANNOTATION`, `GENERAL_CLASSIFICATION_ANNOTATION`, or `IMAGE_BOUNDING_BOX_ANNOTATION` (image object detection). * If your machine learning model performs classification, you must specify `classificationMetadata.isMultiLabel`. * You must specify `bigquerySource` (not `gcsSource`)."}, "textClassificationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1TextClassificationConfig", "description": "Specify this field if your model version performs text classification. `annotationSpecSet` in this configuration must match EvaluationJob.annotationSpecSet. `allowMultiLabel` in this configuration must match `classificationMetadata.isMultiLabel` in input_config."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1EvaluationMetrics": {"id": "GoogleCloudDatalabelingV1beta1EvaluationMetrics", "properties": {"classificationMetrics": {"$ref": "GoogleCloudDatalabelingV1beta1ClassificationMetrics"}, "objectDetectionMetrics": {"$ref": "GoogleCloudDatalabelingV1beta1ObjectDetectionMetrics"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1EventConfig": {"description": "Config for video event human labeling task.", "id": "GoogleCloudDatalabelingV1beta1EventConfig", "properties": {"annotationSpecSets": {"description": "Required. The list of annotation spec set resource name. Similar to video classification, we support selecting event from multiple AnnotationSpecSet at the same time.", "items": {"type": "string"}, "type": "array"}, "clipLength": {"description": "Videos will be cut to smaller clips to make it easier for labelers to work on. Users can configure is field in seconds, if not set, default value is 60s.", "format": "int32", "type": "integer"}, "overlapLength": {"description": "The overlap length between different video clips. Users can configure is field in seconds, if not set, default value is 1s.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Example": {"description": "An Example is a piece of data and its annotation. For example, an image with label \"house\".", "id": "GoogleCloudDatalabelingV1beta1Example", "properties": {"annotations": {"description": "Output only. Annotations for the piece of data in Example. One piece of data can have multiple annotations.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Annotation"}, "type": "array"}, "imagePayload": {"$ref": "GoogleCloudDatalabelingV1beta1ImagePayload", "description": "The image payload, a container of the image bytes/uri."}, "name": {"description": "Output only. Name of the example, in format of: projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/ {annotated_dataset_id}/examples/{example_id}", "type": "string"}, "textPayload": {"$ref": "GoogleCloudDatalabelingV1beta1TextPayload", "description": "The text payload, a container of the text content."}, "videoPayload": {"$ref": "GoogleCloudDatalabelingV1beta1VideoPayload", "description": "The video payload, a container of the video uri."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ExampleComparison": {"description": "Example comparisons comparing ground truth output and predictions for a specific input.", "id": "GoogleCloudDatalabelingV1beta1ExampleComparison", "properties": {"groundTruthExample": {"$ref": "GoogleCloudDatalabelingV1beta1Example", "description": "The ground truth output for the input."}, "modelCreatedExamples": {"description": "Predictions by the model for the input.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Example"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ExportDataOperationMetadata": {"description": "Metadata of an ExportData operation.", "id": "GoogleCloudDatalabelingV1beta1ExportDataOperationMetadata", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "createTime": {"description": "Output only. Timestamp when export dataset request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of dataset to be exported. \"projects/*/datasets/*\"", "type": "string"}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ExportDataOperationResponse": {"description": "Response used for ExportDataset longrunning operation.", "id": "GoogleCloudDatalabelingV1beta1ExportDataOperationResponse", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "dataset": {"description": "Ouptut only. The name of dataset. \"projects/*/datasets/*\"", "type": "string"}, "exportCount": {"description": "Output only. Number of examples exported successfully.", "format": "int32", "type": "integer"}, "labelStats": {"$ref": "GoogleCloudDatalabelingV1beta1LabelStats", "description": "Output only. Statistic infos of labels in the exported dataset."}, "outputConfig": {"$ref": "GoogleCloudDatalabelingV1beta1OutputConfig", "description": "Output only. output_config in the ExportData request."}, "totalCount": {"description": "Output only. Total number of examples requested to export", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ExportDataRequest": {"description": "Request message for ExportData API.", "id": "GoogleCloudDatalabelingV1beta1ExportDataRequest", "properties": {"annotatedDataset": {"description": "Required. Annotated dataset resource name. DataItem in Dataset and their annotations in specified annotated dataset will be exported. It's in format of projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/ {annotated_dataset_id}", "type": "string"}, "filter": {"description": "Optional. Filter is not supported at this moment.", "type": "string"}, "outputConfig": {"$ref": "GoogleCloudDatalabelingV1beta1OutputConfig", "description": "Required. Specify the output destination."}, "userEmailAddress": {"description": "Email of the user who started the export task and should be notified by email. If empty no notification will be sent.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1FeedbackMessage": {"description": "A feedback message inside a feedback thread.", "id": "GoogleCloudDatalabelingV1beta1FeedbackMessage", "properties": {"body": {"description": "String content of the feedback. Maximum of 10000 characters.", "type": "string"}, "createTime": {"description": "Create time.", "format": "google-datetime", "type": "string"}, "image": {"description": "The image storing this feedback if the feedback is an image representing operator's comments.", "format": "byte", "type": "string"}, "name": {"description": "Name of the feedback message in a feedback thread. Format: 'project/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}/feedbackThreads/{feedback_thread_id}/feedbackMessage/{feedback_message_id}'", "type": "string"}, "operatorFeedbackMetadata": {"$ref": "GoogleCloudDatalabelingV1beta1OperatorFeedbackMetadata"}, "requesterFeedbackMetadata": {"$ref": "GoogleCloudDatalabelingV1beta1RequesterFeedbackMetadata"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1FeedbackThread": {"description": "A feedback thread of a certain labeling task on a certain annotated dataset.", "id": "GoogleCloudDatalabelingV1beta1FeedbackThread", "properties": {"feedbackThreadMetadata": {"$ref": "GoogleCloudDatalabelingV1beta1FeedbackThreadMetadata", "description": "<PERSON><PERSON><PERSON> regarding the feedback thread."}, "name": {"description": "Name of the feedback thread. Format: 'project/{project_id}/datasets/{dataset_id}/annotatedDatasets/{annotated_dataset_id}/feedbackThreads/{feedback_thread_id}'", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1FeedbackThreadMetadata": {"id": "GoogleCloudDatalabelingV1beta1FeedbackThreadMetadata", "properties": {"createTime": {"description": "When the thread is created", "format": "google-datetime", "type": "string"}, "lastUpdateTime": {"description": "When the thread is last updated.", "format": "google-datetime", "type": "string"}, "status": {"enum": ["FEEDBACK_THREAD_STATUS_UNSPECIFIED", "NEW", "REPLIED"], "enumDescriptions": ["", "Feedback thread is created with no reply;", "Feedback thread is replied at least once;"], "type": "string"}, "thumbnail": {"description": "An image thumbnail of this thread.", "format": "byte", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1GcsDestination": {"description": "Export destination of the data.Only gcs path is allowed in output_uri.", "id": "GoogleCloudDatalabelingV1beta1GcsDestination", "properties": {"mimeType": {"description": "Required. The format of the gcs destination. Only \"text/csv\" and \"application/json\" are supported.", "type": "string"}, "outputUri": {"description": "Required. The output uri of destination file.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1GcsFolderDestination": {"description": "Export folder destination of the data.", "id": "GoogleCloudDatalabelingV1beta1GcsFolderDestination", "properties": {"outputFolderUri": {"description": "Required. Cloud Storage directory to export data to.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1GcsSource": {"description": "Source of the Cloud Storage file to be imported.", "id": "GoogleCloudDatalabelingV1beta1GcsSource", "properties": {"inputUri": {"description": "Required. The input URI of source file. This must be a Cloud Storage path (`gs://...`).", "type": "string"}, "mimeType": {"description": "Required. The format of the source file. Only \"text/csv\" is supported.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig": {"description": "Configuration for how human labeling task should be done.", "id": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "properties": {"annotatedDatasetDescription": {"description": "Optional. A human-readable description for AnnotatedDataset. The description can be up to 10000 characters long.", "type": "string"}, "annotatedDatasetDisplayName": {"description": "Required. A human-readable name for AnnotatedDataset defined by users. Maximum of 64 characters .", "type": "string"}, "contributorEmails": {"description": "Optional. If you want your own labeling contributors to manage and work on this labeling request, you can set these contributors here. We will give them access to the question types in crowdcompute. Note that these emails must be registered in crowdcompute worker UI: https://crowd-compute.appspot.com/", "items": {"type": "string"}, "type": "array"}, "instruction": {"description": "Required. Instruction resource name.", "type": "string"}, "labelGroup": {"description": "Optional. A human-readable label used to logically group labeling tasks. This string must match the regular expression `[a-zA-Z\\\\d_-]{0,128}`.", "type": "string"}, "languageCode": {"description": "Optional. The Language of this question, as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). Default value is en-US. Only need to set this when task is language related. For example, French text classification.", "type": "string"}, "questionDuration": {"description": "Optional. Maximum duration for contributors to answer a question. Maximum is 3600 seconds. Default is 3600 seconds.", "format": "google-duration", "type": "string"}, "replicaCount": {"description": "Optional. Replication of questions. Each question will be sent to up to this number of contributors to label. Aggregated answers will be returned. Default is set to 1. For image related labeling, valid values are 1, 3, 5.", "format": "int32", "type": "integer"}, "userEmailAddress": {"description": "Email of the user who started the labeling task and should be notified by email. If empty no notification will be sent.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImageBoundingPolyAnnotation": {"description": "Image bounding poly annotation. It represents a polygon including bounding box in the image.", "id": "GoogleCloudDatalabelingV1beta1ImageBoundingPolyAnnotation", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "Label of object in this bounding polygon."}, "boundingPoly": {"$ref": "GoogleCloudDatalabelingV1beta1BoundingPoly"}, "normalizedBoundingPoly": {"$ref": "GoogleCloudDatalabelingV1beta1NormalizedBoundingPoly"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImageClassificationAnnotation": {"description": "Image classification annotation definition.", "id": "GoogleCloudDatalabelingV1beta1ImageClassificationAnnotation", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "Label of image."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImageClassificationConfig": {"description": "Config for image classification human labeling task.", "id": "GoogleCloudDatalabelingV1beta1ImageClassificationConfig", "properties": {"allowMultiLabel": {"description": "Optional. If allow_multi_label is true, contributors are able to choose multiple labels for one image.", "type": "boolean"}, "annotationSpecSet": {"description": "Required. Annotation spec set resource name.", "type": "string"}, "answerAggregationType": {"description": "Optional. The type of how to aggregate answers.", "enum": ["STRING_AGGREGATION_TYPE_UNSPECIFIED", "MAJORITY_VOTE", "UNANIMOUS_VOTE", "NO_AGGREGATION"], "enumDescriptions": ["", "Majority vote to aggregate answers.", "Unanimous answers will be adopted.", "Preserve all answers by crowd compute."], "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImagePayload": {"description": "Container of information about an image.", "id": "GoogleCloudDatalabelingV1beta1ImagePayload", "properties": {"imageThumbnail": {"description": "A byte string of a thumbnail image.", "format": "byte", "type": "string"}, "imageUri": {"description": "Image uri from the user bucket.", "type": "string"}, "mimeType": {"description": "Image format.", "type": "string"}, "signedUri": {"description": "Signed uri of the image file in the service bucket.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImagePolylineAnnotation": {"description": "A polyline for the image annotation.", "id": "GoogleCloudDatalabelingV1beta1ImagePolylineAnnotation", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "Label of this polyline."}, "normalizedPolyline": {"$ref": "GoogleCloudDatalabelingV1beta1NormalizedPolyline"}, "polyline": {"$ref": "GoogleCloudDatalabelingV1beta1Polyline"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImageSegmentationAnnotation": {"description": "Image segmentation annotation.", "id": "GoogleCloudDatalabelingV1beta1ImageSegmentationAnnotation", "properties": {"annotationColors": {"additionalProperties": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec"}, "description": "The mapping between rgb color and annotation spec. The key is the rgb color represented in format of rgb(0, 0, 0). The value is the AnnotationSpec.", "type": "object"}, "imageBytes": {"description": "A byte string of a full image's color map.", "format": "byte", "type": "string"}, "mimeType": {"description": "Image format.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImportDataOperationMetadata": {"description": "Metadata of an ImportData operation.", "id": "GoogleCloudDatalabelingV1beta1ImportDataOperationMetadata", "properties": {"createTime": {"description": "Output only. Timestamp when import dataset request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of imported dataset. \"projects/*/datasets/*\"", "type": "string"}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImportDataOperationResponse": {"description": "Response used for ImportData longrunning operation.", "id": "GoogleCloudDatalabelingV1beta1ImportDataOperationResponse", "properties": {"dataset": {"description": "Ouptut only. The name of imported dataset.", "type": "string"}, "importCount": {"description": "Output only. Number of examples imported successfully.", "format": "int32", "type": "integer"}, "totalCount": {"description": "Output only. Total number of examples requested to import", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ImportDataRequest": {"description": "Request message for ImportData API.", "id": "GoogleCloudDatalabelingV1beta1ImportDataRequest", "properties": {"inputConfig": {"$ref": "GoogleCloudDatalabelingV1beta1InputConfig", "description": "Required. Specify the input source of the data."}, "userEmailAddress": {"description": "Email of the user who started the import task and should be notified by email. If empty no notification will be sent.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1InputConfig": {"description": "The configuration of input data, including data type, location, etc.", "id": "GoogleCloudDatalabelingV1beta1InputConfig", "properties": {"annotationType": {"description": "Optional. The type of annotation to be performed on this data. You must specify this field if you are using this InputConfig in an EvaluationJob.", "enum": ["ANNOTATION_TYPE_UNSPECIFIED", "IMAGE_CLASSIFICATION_ANNOTATION", "IMAGE_BOUNDING_BOX_ANNOTATION", "IMAGE_ORIENTED_BOUNDING_BOX_ANNOTATION", "IMAGE_BOUNDING_POLY_ANNOTATION", "IMAGE_POLYLINE_ANNOTATION", "IMAGE_SEGMENTATION_ANNOTATION", "VIDEO_SHOTS_CLASSIFICATION_ANNOTATION", "VIDEO_OBJECT_TRACKING_ANNOTATION", "VIDEO_OBJECT_DETECTION_ANNOTATION", "VIDEO_EVENT_ANNOTATION", "TEXT_CLASSIFICATION_ANNOTATION", "TEXT_ENTITY_EXTRACTION_ANNOTATION", "GENERAL_CLASSIFICATION_ANNOTATION"], "enumDescriptions": ["", "Classification annotations in an image. Allowed for continuous evaluation.", "Bounding box annotations in an image. A form of image object detection. Allowed for continuous evaluation.", "Oriented bounding box. The box does not have to be parallel to horizontal line.", "Bounding poly annotations in an image.", "Polyline annotations in an image.", "Segmentation annotations in an image.", "Classification annotations in video shots.", "Video object tracking annotation.", "Video object detection annotation.", "Video event annotation.", "Classification for text. Allowed for continuous evaluation.", "Entity extraction for text.", "General classification. Allowed for continuous evaluation."], "type": "string"}, "bigquerySource": {"$ref": "GoogleCloudDatalabelingV1beta1BigQuerySource", "description": "Source located in BigQuery. You must specify this field if you are using this InputConfig in an EvaluationJob."}, "classificationMetadata": {"$ref": "GoogleCloudDatalabelingV1beta1ClassificationMetadata", "description": "Optional. Metadata about annotations for the input. You must specify this field if you are using this InputConfig in an EvaluationJob for a model version that performs classification."}, "dataType": {"description": "Required. Data type must be specifed when user tries to import data.", "enum": ["DATA_TYPE_UNSPECIFIED", "IMAGE", "VIDEO", "TEXT", "GENERAL_DATA"], "enumDescriptions": ["Data type is unspecified.", "Allowed for continuous evaluation.", "Video data type.", "Allowed for continuous evaluation.", "Allowed for continuous evaluation."], "type": "string"}, "gcsSource": {"$ref": "GoogleCloudDatalabelingV1beta1GcsSource", "description": "Source located in Cloud Storage."}, "textMetadata": {"$ref": "GoogleCloudDatalabelingV1beta1TextMetadata", "description": "Required for text import, as language code must be specified."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Instruction": {"description": "Instruction of how to perform the labeling task for human operators. Currently only PDF instruction is supported.", "id": "GoogleCloudDatalabelingV1beta1Instruction", "properties": {"blockingResources": {"description": "Output only. The names of any related resources that are blocking changes to the instruction.", "items": {"type": "string"}, "type": "array"}, "createTime": {"description": "Output only. Creation time of instruction.", "format": "google-datetime", "type": "string"}, "csvInstruction": {"$ref": "GoogleCloudDatalabelingV1beta1CsvInstruction", "deprecated": true, "description": "Deprecated: this instruction format is not supported any more. Instruction from a CSV file, such as for classification task. The CSV file should have exact two columns, in the following format: * The first column is labeled data, such as an image reference, text. * The second column is comma separated labels associated with data."}, "dataType": {"description": "Required. The data type of this instruction.", "enum": ["DATA_TYPE_UNSPECIFIED", "IMAGE", "VIDEO", "TEXT", "GENERAL_DATA"], "enumDescriptions": ["Data type is unspecified.", "Allowed for continuous evaluation.", "Video data type.", "Allowed for continuous evaluation.", "Allowed for continuous evaluation."], "type": "string"}, "description": {"description": "Optional. User-provided description of the instruction. The description can be up to 10000 characters long.", "type": "string"}, "displayName": {"description": "Required. The display name of the instruction. Maximum of 64 characters.", "type": "string"}, "name": {"description": "Output only. Instruction resource name, format: projects/{project_id}/instructions/{instruction_id}", "type": "string"}, "pdfInstruction": {"$ref": "GoogleCloudDatalabelingV1beta1PdfInstruction", "description": "Instruction from a PDF document. The PDF should be in a Cloud Storage bucket."}, "updateTime": {"description": "Output only. Last update time of instruction.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelImageBoundingBoxOperationMetadata": {"description": "Details of a LabelImageBoundingBox operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelImageBoundingBoxOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelImageBoundingPolyOperationMetadata": {"description": "Details of LabelImageBoundingPoly operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelImageBoundingPolyOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelImageClassificationOperationMetadata": {"description": "Metadata of a LabelImageClassification operation.", "id": "GoogleCloudDatalabelingV1beta1LabelImageClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelImageOrientedBoundingBoxOperationMetadata": {"description": "Details of a LabelImageOrientedBoundingBox operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelImageOrientedBoundingBoxOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelImagePolylineOperationMetadata": {"description": "Details of LabelImagePolyline operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelImagePolylineOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelImageRequest": {"description": " Request message for starting an image labeling task.", "id": "GoogleCloudDatalabelingV1beta1LabelImageRequest", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Required. Basic human annotation config."}, "boundingPolyConfig": {"$ref": "GoogleCloudDatalabelingV1beta1BoundingPolyConfig", "description": "Configuration for bounding box and bounding poly task. One of image_classification_config, bounding_poly_config, polyline_config and segmentation_config are required."}, "feature": {"description": "Required. The type of image labeling task.", "enum": ["FEATURE_UNSPECIFIED", "CLASSIFICATION", "BOUNDING_BOX", "ORIENTED_BOUNDING_BOX", "BOUNDING_POLY", "POLYLINE", "SEGMENTATION"], "enumDescriptions": ["", "Label whole image with one or more of labels.", "Label image with bounding boxes for labels.", "Label oriented bounding box. The box does not have to be parallel to horizontal line.", "Label images with bounding poly. A bounding poly is a plane figure that is bounded by a finite chain of straight line segments closing in a loop.", "Label images with polyline. Polyline is formed by connected line segments which are not in closed form.", "Label images with segmentation. Segmentation is different from bounding poly since it is more fine-grained, pixel level annotation."], "type": "string"}, "imageClassificationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1ImageClassificationConfig", "description": "Configuration for image classification task. One of image_classification_config, bounding_poly_config, polyline_config and segmentation_config are required."}, "polylineConfig": {"$ref": "GoogleCloudDatalabelingV1beta1PolylineConfig", "description": "Configuration for polyline task. One of image_classification_config, bounding_poly_config, polyline_config and segmentation_config are required."}, "segmentationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1SegmentationConfig", "description": "Configuration for segmentation task. One of image_classification_config, bounding_poly_config, polyline_config and segmentation_config are required."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelImageSegmentationOperationMetadata": {"description": "Details of a LabelImageSegmentation operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelImageSegmentationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelOperationMetadata": {"description": "Metadata of a labeling operation, such as LabelImage or LabelVideo. Next tag: 23", "id": "GoogleCloudDatalabelingV1beta1LabelOperationMetadata", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "createTime": {"description": "Output only. Timestamp when labeling request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of dataset to be labeled. \"projects/*/datasets/*\"", "type": "string"}, "imageBoundingBoxDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelImageBoundingBoxOperationMetadata", "description": "Details of label image bounding box operation."}, "imageBoundingPolyDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelImageBoundingPolyOperationMetadata", "description": "Details of label image bounding poly operation."}, "imageClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelImageClassificationOperationMetadata", "description": "Details of label image classification operation."}, "imageOrientedBoundingBoxDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelImageOrientedBoundingBoxOperationMetadata", "description": "Details of label image oriented bounding box operation."}, "imagePolylineDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelImagePolylineOperationMetadata", "description": "Details of label image polyline operation."}, "imageSegmentationDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelImageSegmentationOperationMetadata", "description": "Details of label image segmentation operation."}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "progressPercent": {"description": "Output only. Progress of label operation. Range: [0, 100].", "format": "int32", "type": "integer"}, "textClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelTextClassificationOperationMetadata", "description": "Details of label text classification operation."}, "textEntityExtractionDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelTextEntityExtractionOperationMetadata", "description": "Details of label text entity extraction operation."}, "videoClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelVideoClassificationOperationMetadata", "description": "Details of label video classification operation."}, "videoEventDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelVideoEventOperationMetadata", "description": "Details of label video event operation."}, "videoObjectDetectionDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelVideoObjectDetectionOperationMetadata", "description": "Details of label video object detection operation."}, "videoObjectTrackingDetails": {"$ref": "GoogleCloudDatalabelingV1beta1LabelVideoObjectTrackingOperationMetadata", "description": "Details of label video object tracking operation."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelStats": {"description": "Statistics about annotation specs.", "id": "GoogleCloudDatalabelingV1beta1LabelStats", "properties": {"exampleCount": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Map of each annotation spec's example count. Key is the annotation spec name and value is the number of examples for that annotation spec. If the annotated dataset does not have annotation spec, the map will return a pair where the key is empty string and value is the total number of annotations.", "type": "object"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelTextClassificationOperationMetadata": {"description": "Details of a LabelTextClassification operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelTextClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelTextEntityExtractionOperationMetadata": {"description": "Details of a LabelTextEntityExtraction operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelTextEntityExtractionOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelTextRequest": {"description": "Request message for LabelText.", "id": "GoogleCloudDatalabelingV1beta1LabelTextRequest", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Required. Basic human annotation config."}, "feature": {"description": "Required. The type of text labeling task.", "enum": ["FEATURE_UNSPECIFIED", "TEXT_CLASSIFICATION", "TEXT_ENTITY_EXTRACTION"], "enumDescriptions": ["", "Label text content to one of more labels.", "Label entities and their span in text."], "type": "string"}, "textClassificationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1TextClassificationConfig", "description": "Configuration for text classification task. One of text_classification_config and text_entity_extraction_config is required."}, "textEntityExtractionConfig": {"$ref": "GoogleCloudDatalabelingV1beta1TextEntityExtractionConfig", "description": "Configuration for entity extraction task. One of text_classification_config and text_entity_extraction_config is required."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelVideoClassificationOperationMetadata": {"description": "Details of a LabelVideoClassification operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelVideoClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelVideoEventOperationMetadata": {"description": "Details of a LabelVideoEvent operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelVideoEventOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelVideoObjectDetectionOperationMetadata": {"description": "Details of a LabelVideoObjectDetection operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelVideoObjectDetectionOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelVideoObjectTrackingOperationMetadata": {"description": "Details of a LabelVideoObjectTracking operation metadata.", "id": "GoogleCloudDatalabelingV1beta1LabelVideoObjectTrackingOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1LabelVideoRequest": {"description": " Request message for LabelVideo.", "id": "GoogleCloudDatalabelingV1beta1LabelVideoRequest", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1beta1HumanAnnotationConfig", "description": "Required. Basic human annotation config."}, "eventConfig": {"$ref": "GoogleCloudDatalabelingV1beta1EventConfig", "description": "Configuration for video event task. One of video_classification_config, object_detection_config, object_tracking_config and event_config is required."}, "feature": {"description": "Required. The type of video labeling task.", "enum": ["FEATURE_UNSPECIFIED", "CLASSIFICATION", "OBJECT_DETECTION", "OBJECT_TRACKING", "EVENT"], "enumDescriptions": ["", "Label whole video or video segment with one or more labels.", "Label objects with bounding box on image frames extracted from the video.", "Label and track objects in video.", "Label the range of video for the specified events."], "type": "string"}, "objectDetectionConfig": {"$ref": "GoogleCloudDatalabelingV1beta1ObjectDetectionConfig", "description": "Configuration for video object detection task. One of video_classification_config, object_detection_config, object_tracking_config and event_config is required."}, "objectTrackingConfig": {"$ref": "GoogleCloudDatalabelingV1beta1ObjectTrackingConfig", "description": "Configuration for video object tracking task. One of video_classification_config, object_detection_config, object_tracking_config and event_config is required."}, "videoClassificationConfig": {"$ref": "GoogleCloudDatalabelingV1beta1VideoClassificationConfig", "description": "Configuration for video classification task. One of video_classification_config, object_detection_config, object_tracking_config and event_config is required."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListAnnotatedDatasetsResponse": {"description": "Results of listing annotated datasets for a dataset.", "id": "GoogleCloudDatalabelingV1beta1ListAnnotatedDatasetsResponse", "properties": {"annotatedDatasets": {"description": "The list of annotated datasets to return.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotatedDataset"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListAnnotationSpecSetsResponse": {"description": "Results of listing annotation spec set under a project.", "id": "GoogleCloudDatalabelingV1beta1ListAnnotationSpecSetsResponse", "properties": {"annotationSpecSets": {"description": "The list of annotation spec sets.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpecSet"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListDataItemsResponse": {"description": "Results of listing data items in a dataset.", "id": "GoogleCloudDatalabelingV1beta1ListDataItemsResponse", "properties": {"dataItems": {"description": "The list of data items to return.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1DataItem"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListDatasetsResponse": {"description": "Results of listing datasets within a project.", "id": "GoogleCloudDatalabelingV1beta1ListDatasetsResponse", "properties": {"datasets": {"description": "The list of datasets to return.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Dataset"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListEvaluationJobsResponse": {"description": "Results for listing evaluation jobs.", "id": "GoogleCloudDatalabelingV1beta1ListEvaluationJobsResponse", "properties": {"evaluationJobs": {"description": "The list of evaluation jobs to return.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1EvaluationJob"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListExamplesResponse": {"description": "Results of listing Examples in and annotated dataset.", "id": "GoogleCloudDatalabelingV1beta1ListExamplesResponse", "properties": {"examples": {"description": "The list of examples to return.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Example"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListFeedbackMessagesResponse": {"description": "Results for listing FeedbackMessages.", "id": "GoogleCloudDatalabelingV1beta1ListFeedbackMessagesResponse", "properties": {"feedbackMessages": {"description": "The list of feedback messages to return.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1FeedbackMessage"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListFeedbackThreadsResponse": {"description": "Results for listing FeedbackThreads.", "id": "GoogleCloudDatalabelingV1beta1ListFeedbackThreadsResponse", "properties": {"feedbackThreads": {"description": "The list of feedback threads to return.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1FeedbackThread"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ListInstructionsResponse": {"description": "Results of listing instructions under a project.", "id": "GoogleCloudDatalabelingV1beta1ListInstructionsResponse", "properties": {"instructions": {"description": "The list of Instructions to return.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Instruction"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1NormalizedBoundingPoly": {"description": "Normalized bounding polygon.", "id": "GoogleCloudDatalabelingV1beta1NormalizedBoundingPoly", "properties": {"normalizedVertices": {"description": "The bounding polygon normalized vertices.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1NormalizedVertex"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1NormalizedPolyline": {"description": "Normalized polyline.", "id": "GoogleCloudDatalabelingV1beta1NormalizedPolyline", "properties": {"normalizedVertices": {"description": "The normalized polyline vertices.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1NormalizedVertex"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1NormalizedVertex": {"description": "A vertex represents a 2D point in the image. NOTE: the normalized vertex coordinates are relative to the original image and range from 0 to 1.", "id": "GoogleCloudDatalabelingV1beta1NormalizedVertex", "properties": {"x": {"description": "X coordinate.", "format": "float", "type": "number"}, "y": {"description": "Y coordinate.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ObjectDetectionConfig": {"description": "Config for video object detection human labeling task. Object detection will be conducted on the images extracted from the video, and those objects will be labeled with bounding boxes. User need to specify the number of images to be extracted per second as the extraction frame rate.", "id": "GoogleCloudDatalabelingV1beta1ObjectDetectionConfig", "properties": {"annotationSpecSet": {"description": "Required. Annotation spec set resource name.", "type": "string"}, "extractionFrameRate": {"description": "Required. Number of frames per second to be extracted from the video.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ObjectDetectionMetrics": {"description": "Metrics calculated for an image object detection (bounding box) model.", "id": "GoogleCloudDatalabelingV1beta1ObjectDetectionMetrics", "properties": {"prCurve": {"$ref": "GoogleCloudDatalabelingV1beta1PrCurve", "description": "Precision-recall curve."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ObjectTrackingConfig": {"description": "Config for video object tracking human labeling task.", "id": "GoogleCloudDatalabelingV1beta1ObjectTrackingConfig", "properties": {"annotationSpecSet": {"description": "Required. Annotation spec set resource name.", "type": "string"}, "clipLength": {"description": "Videos will be cut to smaller clips to make it easier for labelers to work on. Users can configure is field in seconds, if not set, default value is 20s.", "format": "int32", "type": "integer"}, "overlapLength": {"description": "The overlap length between different video clips. Users can configure is field in seconds, if not set, default value is 0.3s.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ObjectTrackingFrame": {"description": "Video frame level annotation for object detection and tracking.", "id": "GoogleCloudDatalabelingV1beta1ObjectTrackingFrame", "properties": {"boundingPoly": {"$ref": "GoogleCloudDatalabelingV1beta1BoundingPoly"}, "normalizedBoundingPoly": {"$ref": "GoogleCloudDatalabelingV1beta1NormalizedBoundingPoly"}, "timeOffset": {"description": "The time offset of this frame relative to the beginning of the video.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1OperatorFeedbackMetadata": {"description": "Metadata describing the feedback from the operator.", "id": "GoogleCloudDatalabelingV1beta1OperatorFeedbackMetadata", "properties": {}, "type": "object"}, "GoogleCloudDatalabelingV1beta1OperatorMetadata": {"description": "General information useful for labels coming from contributors.", "id": "GoogleCloudDatalabelingV1beta1OperatorMetadata", "properties": {"comments": {"description": "Comments from contributors.", "items": {"type": "string"}, "type": "array"}, "labelVotes": {"description": "The total number of contributors that choose this label.", "format": "int32", "type": "integer"}, "score": {"description": "Confidence score corresponding to a label. For examle, if 3 contributors have answered the question and 2 of them agree on the final label, the confidence score will be 0.67 (2/3).", "format": "float", "type": "number"}, "totalVotes": {"description": "The total number of contributors that answer this question.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1OutputConfig": {"description": "The configuration of output data.", "id": "GoogleCloudDatalabelingV1beta1OutputConfig", "properties": {"gcsDestination": {"$ref": "GoogleCloudDatalabelingV1beta1GcsDestination", "description": "Output to a file in Cloud Storage. Should be used for labeling output other than image segmentation."}, "gcsFolderDestination": {"$ref": "GoogleCloudDatalabelingV1beta1GcsFolderDestination", "description": "Output to a folder in Cloud Storage. Should be used for image segmentation or document de-identification labeling outputs."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1PauseEvaluationJobRequest": {"description": "Request message for PauseEvaluationJob.", "id": "GoogleCloudDatalabelingV1beta1PauseEvaluationJobRequest", "properties": {}, "type": "object"}, "GoogleCloudDatalabelingV1beta1PdfInstruction": {"description": "Instruction from a PDF file.", "id": "GoogleCloudDatalabelingV1beta1PdfInstruction", "properties": {"gcsFileUri": {"description": "PDF file for the instruction. Only gcs path is allowed.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Polyline": {"description": "A line with multiple line segments.", "id": "GoogleCloudDatalabelingV1beta1Polyline", "properties": {"vertices": {"description": "The polyline vertices.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Vertex"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1PolylineConfig": {"description": "Config for image polyline human labeling task.", "id": "GoogleCloudDatalabelingV1beta1PolylineConfig", "properties": {"annotationSpecSet": {"description": "Required. Annotation spec set resource name.", "type": "string"}, "instructionMessage": {"description": "Optional. Instruction message showed on contributors UI.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1PrCurve": {"id": "GoogleCloudDatalabelingV1beta1PrCurve", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "The annotation spec of the label for which the precision-recall curve calculated. If this field is empty, that means the precision-recall curve is an aggregate curve for all labels."}, "areaUnderCurve": {"description": "Area under the precision-recall curve. Not to be confused with area under a receiver operating characteristic (ROC) curve.", "format": "float", "type": "number"}, "confidenceMetricsEntries": {"description": "Entries that make up the precision-recall graph. Each entry is a \"point\" on the graph drawn for a different `confidence_threshold`.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1ConfidenceMetricsEntry"}, "type": "array"}, "meanAveragePrecision": {"description": "Mean average prcision of this curve.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1RequesterFeedbackMetadata": {"description": "Metadata describing the feedback from the labeling task requester.", "id": "GoogleCloudDatalabelingV1beta1RequesterFeedbackMetadata", "properties": {}, "type": "object"}, "GoogleCloudDatalabelingV1beta1ResumeEvaluationJobRequest": {"description": "Request message ResumeEvaluationJob.", "id": "GoogleCloudDatalabelingV1beta1ResumeEvaluationJobRequest", "properties": {}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Row": {"description": "A row in the confusion matrix. Each entry in this row has the same ground truth label.", "id": "GoogleCloudDatalabelingV1beta1Row", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "The annotation spec of the ground truth label for this row."}, "entries": {"description": "A list of the confusion matrix entries. One entry for each possible predicted label.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1ConfusionMatrixEntry"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1SearchEvaluationsResponse": {"description": "Results of searching evaluations.", "id": "GoogleCloudDatalabelingV1beta1SearchEvaluationsResponse", "properties": {"evaluations": {"description": "The list of evaluations matching the search.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1Evaluation"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1SearchExampleComparisonsRequest": {"description": "Request message of SearchExampleComparisons.", "id": "GoogleCloudDatalabelingV1beta1SearchExampleComparisonsRequest", "properties": {"pageSize": {"description": "Optional. Requested page size. Server may return fewer results than requested. Default value is 100.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results for the server to return. Typically obtained by the nextPageToken of the response to a previous search rquest. If you don't specify this field, the API call requests the first page of the search.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1SearchExampleComparisonsResponse": {"description": "Results of searching example comparisons.", "id": "GoogleCloudDatalabelingV1beta1SearchExampleComparisonsResponse", "properties": {"exampleComparisons": {"description": "A list of example comparisons matching the search criteria.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1ExampleComparison"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1SegmentationConfig": {"description": "Config for image segmentation", "id": "GoogleCloudDatalabelingV1beta1SegmentationConfig", "properties": {"annotationSpecSet": {"description": "Required. Annotation spec set resource name. format: projects/{project_id}/annotationSpecSets/{annotation_spec_set_id}", "type": "string"}, "instructionMessage": {"description": "Instruction message showed on labelers UI.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1SentimentConfig": {"description": "Config for setting up sentiments.", "id": "GoogleCloudDatalabelingV1beta1SentimentConfig", "properties": {"enableLabelSentimentSelection": {"description": "If set to true, contributors will have the option to select sentiment of the label they selected, to mark it as negative or positive label. Default is false.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1SequentialSegment": {"description": "Start and end position in a sequence (e.g. text segment).", "id": "GoogleCloudDatalabelingV1beta1SequentialSegment", "properties": {"end": {"description": "End position (exclusive).", "format": "int32", "type": "integer"}, "start": {"description": "Start position (inclusive).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1TextClassificationAnnotation": {"description": "Text classification annotation.", "id": "GoogleCloudDatalabelingV1beta1TextClassificationAnnotation", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "Label of the text."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1TextClassificationConfig": {"description": "Config for text classification human labeling task.", "id": "GoogleCloudDatalabelingV1beta1TextClassificationConfig", "properties": {"allowMultiLabel": {"description": "Optional. If allow_multi_label is true, contributors are able to choose multiple labels for one text segment.", "type": "boolean"}, "annotationSpecSet": {"description": "Required. Annotation spec set resource name.", "type": "string"}, "sentimentConfig": {"$ref": "GoogleCloudDatalabelingV1beta1SentimentConfig", "deprecated": true, "description": "Optional. Configs for sentiment selection. We deprecate sentiment analysis in data labeling side as it is incompatible with uCAIP."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1TextEntityExtractionAnnotation": {"description": "Text entity extraction annotation.", "id": "GoogleCloudDatalabelingV1beta1TextEntityExtractionAnnotation", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "Label of the text entities."}, "sequentialSegment": {"$ref": "GoogleCloudDatalabelingV1beta1SequentialSegment", "description": "Position of the entity."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1TextEntityExtractionConfig": {"description": "Config for text entity extraction human labeling task.", "id": "GoogleCloudDatalabelingV1beta1TextEntityExtractionConfig", "properties": {"annotationSpecSet": {"description": "Required. Annotation spec set resource name.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1TextMetadata": {"description": "<PERSON><PERSON><PERSON> for the text.", "id": "GoogleCloudDatalabelingV1beta1TextMetadata", "properties": {"languageCode": {"description": "The language of this text, as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). Default value is en-US.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1TextPayload": {"description": "Container of information about a piece of text.", "id": "GoogleCloudDatalabelingV1beta1TextPayload", "properties": {"textContent": {"description": "Text content.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1TimeSegment": {"description": "A time period inside of an example that has a time dimension (e.g. video).", "id": "GoogleCloudDatalabelingV1beta1TimeSegment", "properties": {"endTimeOffset": {"description": "End of the time segment (exclusive), represented as the duration since the example start.", "format": "google-duration", "type": "string"}, "startTimeOffset": {"description": "Start of the time segment (inclusive), represented as the duration since the example start.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1Vertex": {"description": "A vertex represents a 2D point in the image. NOTE: the vertex coordinates are in the same scale as the original image.", "id": "GoogleCloudDatalabelingV1beta1Vertex", "properties": {"x": {"description": "X coordinate.", "format": "int32", "type": "integer"}, "y": {"description": "Y coordinate.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1VideoClassificationAnnotation": {"description": "Video classification annotation.", "id": "GoogleCloudDatalabelingV1beta1VideoClassificationAnnotation", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "Label of the segment specified by time_segment."}, "timeSegment": {"$ref": "GoogleCloudDatalabelingV1beta1TimeSegment", "description": "The time segment of the video to which the annotation applies."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1VideoClassificationConfig": {"description": "Config for video classification human labeling task. Currently two types of video classification are supported: 1. Assign labels on the entire video. 2. Split the video into multiple video clips based on camera shot, and assign labels on each video clip.", "id": "GoogleCloudDatalabelingV1beta1VideoClassificationConfig", "properties": {"annotationSpecSetConfigs": {"description": "Required. The list of annotation spec set configs. Since watching a video clip takes much longer time than an image, we support label with multiple AnnotationSpecSet at the same time. Labels in each AnnotationSpecSet will be shown in a group to contributors. Contributors can select one or more (depending on whether to allow multi label) from each group.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpecSetConfig"}, "type": "array"}, "applyShotDetection": {"description": "Optional. Option to apply shot detection on the video.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1VideoEventAnnotation": {"description": "Video event annotation.", "id": "GoogleCloudDatalabelingV1beta1VideoEventAnnotation", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "Label of the event in this annotation."}, "timeSegment": {"$ref": "GoogleCloudDatalabelingV1beta1TimeSegment", "description": "The time segment of the video to which the annotation applies."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1VideoObjectTrackingAnnotation": {"description": "Video object tracking annotation.", "id": "GoogleCloudDatalabelingV1beta1VideoObjectTrackingAnnotation", "properties": {"annotationSpec": {"$ref": "GoogleCloudDatalabelingV1beta1AnnotationSpec", "description": "Label of the object tracked in this annotation."}, "objectTrackingFrames": {"description": "The list of frames where this object track appears.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1ObjectTrackingFrame"}, "type": "array"}, "timeSegment": {"$ref": "GoogleCloudDatalabelingV1beta1TimeSegment", "description": "The time segment of the video to which object tracking applies."}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1VideoPayload": {"description": "Container of information of a video.", "id": "GoogleCloudDatalabelingV1beta1VideoPayload", "properties": {"frameRate": {"description": "FPS of the video.", "format": "float", "type": "number"}, "mimeType": {"description": "Video format.", "type": "string"}, "signedUri": {"description": "Signed uri of the video file in the service bucket.", "type": "string"}, "videoThumbnails": {"description": "The list of video thumbnails.", "items": {"$ref": "GoogleCloudDatalabelingV1beta1VideoThumbnail"}, "type": "array"}, "videoUri": {"description": "Video uri from the user bucket.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1beta1VideoThumbnail": {"description": "Container of information of a video thumbnail.", "id": "GoogleCloudDatalabelingV1beta1VideoThumbnail", "properties": {"thumbnail": {"description": "A byte string of the video frame.", "format": "byte", "type": "string"}, "timeOffset": {"description": "Time offset relative to the beginning of the video, corresponding to the video frame where the thumbnail has been extracted from.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1CreateInstructionMetadata": {"description": "Metadata of a CreateInstruction operation.", "id": "GoogleCloudDatalabelingV1p1alpha1CreateInstructionMetadata", "properties": {"createTime": {"description": "Timestamp when create instruction request was created.", "format": "google-datetime", "type": "string"}, "instruction": {"description": "The name of the created Instruction. projects/{project_id}/instructions/{instruction_id}", "type": "string"}, "partialFailures": {"description": "Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1ExportDataOperationMetadata": {"description": "Metadata of an ExportData operation.", "id": "GoogleCloudDatalabelingV1p1alpha1ExportDataOperationMetadata", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "createTime": {"description": "Output only. Timestamp when export dataset request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of dataset to be exported. \"projects/*/datasets/*\"", "type": "string"}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1ExportDataOperationResponse": {"description": "Response used for ExportDataset longrunning operation.", "id": "GoogleCloudDatalabelingV1p1alpha1ExportDataOperationResponse", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "dataset": {"description": "Ouptut only. The name of dataset. \"projects/*/datasets/*\"", "type": "string"}, "exportCount": {"description": "Output only. Number of examples exported successfully.", "format": "int32", "type": "integer"}, "labelStats": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelStats", "description": "Output only. Statistic infos of labels in the exported dataset."}, "outputConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1OutputConfig", "description": "Output only. output_config in the ExportData request."}, "totalCount": {"description": "Output only. Total number of examples requested to export", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1GcsDestination": {"description": "Export destination of the data.Only gcs path is allowed in output_uri.", "id": "GoogleCloudDatalabelingV1p1alpha1GcsDestination", "properties": {"mimeType": {"description": "Required. The format of the gcs destination. Only \"text/csv\" and \"application/json\" are supported.", "type": "string"}, "outputUri": {"description": "Required. The output uri of destination file.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1GcsFolderDestination": {"description": "Export folder destination of the data.", "id": "GoogleCloudDatalabelingV1p1alpha1GcsFolderDestination", "properties": {"outputFolderUri": {"description": "Required. Cloud Storage directory to export data to.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1GenerateAnalysisReportOperationMetadata": {"description": "Metadata of an GenerateAnalysisReport operation.", "id": "GoogleCloudDatalabelingV1p1alpha1GenerateAnalysisReportOperationMetadata", "properties": {"createTime": {"description": "Timestamp when generate report request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "The name of the dataset for which the analysis report is generated. Format: \"projects/*/datasets/*\"", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig": {"description": "Configuration for how human labeling task should be done.", "id": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "properties": {"annotatedDatasetDescription": {"description": "Optional. A human-readable description for AnnotatedDataset. The description can be up to 10000 characters long.", "type": "string"}, "annotatedDatasetDisplayName": {"description": "Required. A human-readable name for AnnotatedDataset defined by users. Maximum of 64 characters .", "type": "string"}, "contributorEmails": {"description": "Optional. If you want your own labeling contributors to manage and work on this labeling request, you can set these contributors here. We will give them access to the question types in crowdcompute. Note that these emails must be registered in crowdcompute worker UI: https://crowd-compute.appspot.com/", "items": {"type": "string"}, "type": "array"}, "instruction": {"description": "Required. Instruction resource name.", "type": "string"}, "labelGroup": {"description": "Optional. A human-readable label used to logically group labeling tasks. This string must match the regular expression `[a-zA-Z\\\\d_-]{0,128}`.", "type": "string"}, "languageCode": {"description": "Optional. The Language of this question, as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). Default value is en-US. Only need to set this when task is language related. For example, French text classification.", "type": "string"}, "questionDuration": {"description": "Optional. Maximum duration for contributors to answer a question. Maximum is 3600 seconds. Default is 3600 seconds.", "format": "google-duration", "type": "string"}, "replicaCount": {"description": "Optional. Replication of questions. Each question will be sent to up to this number of contributors to label. Aggregated answers will be returned. Default is set to 1. For image related labeling, valid values are 1, 3, 5.", "format": "int32", "type": "integer"}, "userEmailAddress": {"description": "Email of the user who started the labeling task and should be notified by email. If empty no notification will be sent.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1ImportDataOperationMetadata": {"description": "Metadata of an ImportData operation.", "id": "GoogleCloudDatalabelingV1p1alpha1ImportDataOperationMetadata", "properties": {"createTime": {"description": "Output only. Timestamp when import dataset request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of imported dataset. \"projects/*/datasets/*\"", "type": "string"}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1ImportDataOperationResponse": {"description": "Response used for ImportData longrunning operation.", "id": "GoogleCloudDatalabelingV1p1alpha1ImportDataOperationResponse", "properties": {"dataset": {"description": "Ouptut only. The name of imported dataset.", "type": "string"}, "importCount": {"description": "Output only. Number of examples imported successfully.", "format": "int32", "type": "integer"}, "totalCount": {"description": "Output only. Total number of examples requested to import", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelImageBoundingBoxOperationMetadata": {"description": "Details of a LabelImageBoundingBox operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelImageBoundingBoxOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelImageBoundingPolyOperationMetadata": {"description": "Details of LabelImageBoundingPoly operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelImageBoundingPolyOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelImageClassificationOperationMetadata": {"description": "Metadata of a LabelImageClassification operation.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelImageClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelImageOrientedBoundingBoxOperationMetadata": {"description": "Details of a LabelImageOrientedBoundingBox operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelImageOrientedBoundingBoxOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelImagePolylineOperationMetadata": {"description": "Details of LabelImagePolyline operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelImagePolylineOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelImageSegmentationOperationMetadata": {"description": "Details of a LabelImageSegmentation operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelImageSegmentationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelOperationMetadata": {"description": "Metadata of a labeling operation, such as LabelImage or LabelVideo. Next tag: 23", "id": "GoogleCloudDatalabelingV1p1alpha1LabelOperationMetadata", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "createTime": {"description": "Output only. Timestamp when labeling request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of dataset to be labeled. \"projects/*/datasets/*\"", "type": "string"}, "imageBoundingBoxDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelImageBoundingBoxOperationMetadata", "description": "Details of label image bounding box operation."}, "imageBoundingPolyDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelImageBoundingPolyOperationMetadata", "description": "Details of label image bounding poly operation."}, "imageClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelImageClassificationOperationMetadata", "description": "Details of label image classification operation."}, "imageOrientedBoundingBoxDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelImageOrientedBoundingBoxOperationMetadata", "description": "Details of label image oriented bounding box operation."}, "imagePolylineDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelImagePolylineOperationMetadata", "description": "Details of label image polyline operation."}, "imageSegmentationDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelImageSegmentationOperationMetadata", "description": "Details of label image segmentation operation."}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "progressPercent": {"description": "Output only. Progress of label operation. Range: [0, 100].", "format": "int32", "type": "integer"}, "textClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelTextClassificationOperationMetadata", "description": "Details of label text classification operation."}, "textEntityExtractionDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelTextEntityExtractionOperationMetadata", "description": "Details of label text entity extraction operation."}, "videoClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelVideoClassificationOperationMetadata", "description": "Details of label video classification operation."}, "videoEventDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelVideoEventOperationMetadata", "description": "Details of label video event operation."}, "videoObjectDetectionDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelVideoObjectDetectionOperationMetadata", "description": "Details of label video object detection operation."}, "videoObjectTrackingDetails": {"$ref": "GoogleCloudDatalabelingV1p1alpha1LabelVideoObjectTrackingOperationMetadata", "description": "Details of label video object tracking operation."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelStats": {"description": "Statistics about annotation specs.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelStats", "properties": {"exampleCount": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Map of each annotation spec's example count. Key is the annotation spec name and value is the number of examples for that annotation spec. If the annotated dataset does not have annotation spec, the map will return a pair where the key is empty string and value is the total number of annotations.", "type": "object"}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelTextClassificationOperationMetadata": {"description": "Details of a LabelTextClassification operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelTextClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelTextEntityExtractionOperationMetadata": {"description": "Details of a LabelTextEntityExtraction operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelTextEntityExtractionOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelVideoClassificationOperationMetadata": {"description": "Details of a LabelVideoClassification operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelVideoClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelVideoEventOperationMetadata": {"description": "Details of a LabelVideoEvent operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelVideoEventOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelVideoObjectDetectionOperationMetadata": {"description": "Details of a LabelVideoObjectDetection operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelVideoObjectDetectionOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1LabelVideoObjectTrackingOperationMetadata": {"description": "Details of a LabelVideoObjectTracking operation metadata.", "id": "GoogleCloudDatalabelingV1p1alpha1LabelVideoObjectTrackingOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p1alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p1alpha1OutputConfig": {"description": "The configuration of output data.", "id": "GoogleCloudDatalabelingV1p1alpha1OutputConfig", "properties": {"gcsDestination": {"$ref": "GoogleCloudDatalabelingV1p1alpha1GcsDestination", "description": "Output to a file in Cloud Storage. Should be used for labeling output other than image segmentation."}, "gcsFolderDestination": {"$ref": "GoogleCloudDatalabelingV1p1alpha1GcsFolderDestination", "description": "Output to a folder in Cloud Storage. Should be used for image segmentation or document de-identification labeling outputs."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1CreateInstructionMetadata": {"description": "Metadata of a CreateInstruction operation.", "id": "GoogleCloudDatalabelingV1p2alpha1CreateInstructionMetadata", "properties": {"createTime": {"description": "Timestamp when create instruction request was created.", "format": "google-datetime", "type": "string"}, "instruction": {"description": "The name of the created Instruction. projects/{project_id}/instructions/{instruction_id}", "type": "string"}, "partialFailures": {"description": "Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1ExportDataOperationMetadata": {"description": "Metadata of an ExportData operation.", "id": "GoogleCloudDatalabelingV1p2alpha1ExportDataOperationMetadata", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "createTime": {"description": "Output only. Timestamp when export dataset request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of dataset to be exported. \"projects/*/datasets/*\"", "type": "string"}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1ExportDataOperationResponse": {"description": "Response used for ExportDataset longrunning operation.", "id": "GoogleCloudDatalabelingV1p2alpha1ExportDataOperationResponse", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "dataset": {"description": "Ouptut only. The name of dataset. \"projects/*/datasets/*\"", "type": "string"}, "exportCount": {"description": "Output only. Number of examples exported successfully.", "format": "int32", "type": "integer"}, "labelStats": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelStats", "description": "Output only. Statistic infos of labels in the exported dataset."}, "outputConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1OutputConfig", "description": "Output only. output_config in the ExportData request."}, "totalCount": {"description": "Output only. Total number of examples requested to export", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1GcsDestination": {"description": "Export destination of the data.Only gcs path is allowed in output_uri.", "id": "GoogleCloudDatalabelingV1p2alpha1GcsDestination", "properties": {"mimeType": {"description": "Required. The format of the gcs destination. Only \"text/csv\" and \"application/json\" are supported.", "type": "string"}, "outputUri": {"description": "Required. The output uri of destination file.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1GcsFolderDestination": {"description": "Export folder destination of the data.", "id": "GoogleCloudDatalabelingV1p2alpha1GcsFolderDestination", "properties": {"outputFolderUri": {"description": "Required. Cloud Storage directory to export data to.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig": {"description": "Configuration for how human labeling task should be done.", "id": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "properties": {"annotatedDatasetDescription": {"description": "Optional. A human-readable description for AnnotatedDataset. The description can be up to 10000 characters long.", "type": "string"}, "annotatedDatasetDisplayName": {"description": "Required. A human-readable name for AnnotatedDataset defined by users. Maximum of 64 characters .", "type": "string"}, "contributorEmails": {"description": "Optional. If you want your own labeling contributors to manage and work on this labeling request, you can set these contributors here. We will give them access to the question types in crowdcompute. Note that these emails must be registered in crowdcompute worker UI: https://crowd-compute.appspot.com/", "items": {"type": "string"}, "type": "array"}, "instruction": {"description": "Required. Instruction resource name.", "type": "string"}, "labelGroup": {"description": "Optional. A human-readable label used to logically group labeling tasks. This string must match the regular expression `[a-zA-Z\\\\d_-]{0,128}`.", "type": "string"}, "languageCode": {"description": "Optional. The Language of this question, as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). Default value is en-US. Only need to set this when task is language related. For example, French text classification.", "type": "string"}, "questionDuration": {"description": "Optional. Maximum duration for contributors to answer a question. Maximum is 3600 seconds. Default is 3600 seconds.", "format": "google-duration", "type": "string"}, "replicaCount": {"description": "Optional. Replication of questions. Each question will be sent to up to this number of contributors to label. Aggregated answers will be returned. Default is set to 1. For image related labeling, valid values are 1, 3, 5.", "format": "int32", "type": "integer"}, "userEmailAddress": {"description": "Email of the user who started the labeling task and should be notified by email. If empty no notification will be sent.", "type": "string"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1ImportDataOperationMetadata": {"description": "Metadata of an ImportData operation.", "id": "GoogleCloudDatalabelingV1p2alpha1ImportDataOperationMetadata", "properties": {"createTime": {"description": "Output only. Timestamp when import dataset request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of imported dataset. \"projects/*/datasets/*\"", "type": "string"}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1ImportDataOperationResponse": {"description": "Response used for ImportData longrunning operation.", "id": "GoogleCloudDatalabelingV1p2alpha1ImportDataOperationResponse", "properties": {"dataset": {"description": "Ouptut only. The name of imported dataset.", "type": "string"}, "importCount": {"description": "Output only. Number of examples imported successfully.", "format": "int32", "type": "integer"}, "totalCount": {"description": "Output only. Total number of examples requested to import", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelImageBoundingBoxOperationMetadata": {"description": "Details of a LabelImageBoundingBox operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelImageBoundingBoxOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelImageBoundingPolyOperationMetadata": {"description": "Details of LabelImageBoundingPoly operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelImageBoundingPolyOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelImageClassificationOperationMetadata": {"description": "Metadata of a LabelImageClassification operation.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelImageClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelImageOrientedBoundingBoxOperationMetadata": {"description": "Details of a LabelImageOrientedBoundingBox operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelImageOrientedBoundingBoxOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelImagePolylineOperationMetadata": {"description": "Details of LabelImagePolyline operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelImagePolylineOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelImageSegmentationOperationMetadata": {"description": "Details of a LabelImageSegmentation operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelImageSegmentationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelOperationMetadata": {"description": "Metadata of a labeling operation, such as LabelImage or LabelVideo. Next tag: 23", "id": "GoogleCloudDatalabelingV1p2alpha1LabelOperationMetadata", "properties": {"annotatedDataset": {"description": "Output only. The name of annotated dataset in format \"projects/*/datasets/*/annotatedDatasets/*\".", "type": "string"}, "createTime": {"description": "Output only. Timestamp when labeling request was created.", "format": "google-datetime", "type": "string"}, "dataset": {"description": "Output only. The name of dataset to be labeled. \"projects/*/datasets/*\"", "type": "string"}, "imageBoundingBoxDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelImageBoundingBoxOperationMetadata", "description": "Details of label image bounding box operation."}, "imageBoundingPolyDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelImageBoundingPolyOperationMetadata", "description": "Details of label image bounding poly operation."}, "imageClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelImageClassificationOperationMetadata", "description": "Details of label image classification operation."}, "imageOrientedBoundingBoxDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelImageOrientedBoundingBoxOperationMetadata", "description": "Details of label image oriented bounding box operation."}, "imagePolylineDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelImagePolylineOperationMetadata", "description": "Details of label image polyline operation."}, "imageSegmentationDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelImageSegmentationOperationMetadata", "description": "Details of label image segmentation operation."}, "partialFailures": {"description": "Output only. Partial failures encountered. E.g. single files that couldn't be read. Status details field will contain standard GCP error details.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "progressPercent": {"description": "Output only. Progress of label operation. Range: [0, 100].", "format": "int32", "type": "integer"}, "textClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelTextClassificationOperationMetadata", "description": "Details of label text classification operation."}, "textEntityExtractionDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelTextEntityExtractionOperationMetadata", "description": "Details of label text entity extraction operation."}, "videoClassificationDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelVideoClassificationOperationMetadata", "description": "Details of label video classification operation."}, "videoEventDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelVideoEventOperationMetadata", "description": "Details of label video event operation."}, "videoObjectDetectionDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelVideoObjectDetectionOperationMetadata", "description": "Details of label video object detection operation."}, "videoObjectTrackingDetails": {"$ref": "GoogleCloudDatalabelingV1p2alpha1LabelVideoObjectTrackingOperationMetadata", "description": "Details of label video object tracking operation."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelStats": {"description": "Statistics about annotation specs.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelStats", "properties": {"exampleCount": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Map of each annotation spec's example count. Key is the annotation spec name and value is the number of examples for that annotation spec. If the annotated dataset does not have annotation spec, the map will return a pair where the key is empty string and value is the total number of annotations.", "type": "object"}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelTextClassificationOperationMetadata": {"description": "Details of a LabelTextClassification operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelTextClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelTextEntityExtractionOperationMetadata": {"description": "Details of a LabelTextEntityExtraction operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelTextEntityExtractionOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelVideoClassificationOperationMetadata": {"description": "Details of a LabelVideoClassification operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelVideoClassificationOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelVideoEventOperationMetadata": {"description": "Details of a LabelVideoEvent operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelVideoEventOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelVideoObjectDetectionOperationMetadata": {"description": "Details of a LabelVideoObjectDetection operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelVideoObjectDetectionOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1LabelVideoObjectTrackingOperationMetadata": {"description": "Details of a LabelVideoObjectTracking operation metadata.", "id": "GoogleCloudDatalabelingV1p2alpha1LabelVideoObjectTrackingOperationMetadata", "properties": {"basicConfig": {"$ref": "GoogleCloudDatalabelingV1p2alpha1HumanAnnotationConfig", "description": "Basic human annotation config used in labeling request."}}, "type": "object"}, "GoogleCloudDatalabelingV1p2alpha1OutputConfig": {"description": "The configuration of output data.", "id": "GoogleCloudDatalabelingV1p2alpha1OutputConfig", "properties": {"gcsDestination": {"$ref": "GoogleCloudDatalabelingV1p2alpha1GcsDestination", "description": "Output to a file in Cloud Storage. Should be used for labeling output other than image segmentation."}, "gcsFolderDestination": {"$ref": "GoogleCloudDatalabelingV1p2alpha1GcsFolderDestination", "description": "Output to a folder in Cloud Storage. Should be used for image segmentation or document de-identification labeling outputs."}}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Data Labeling API", "version": "v1beta1", "version_module": true}