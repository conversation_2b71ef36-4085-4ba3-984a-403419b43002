#!/usr/bin/env python3
"""
Création simple d'une vidéo de 8 secondes pour SkyReels-A1
Version simplifiée sans dépendances complexes
"""

import os
import sys
import time
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import subprocess

def create_frames_for_video():
    """Crée les frames pour une vidéo de 8 secondes"""
    print("🎬 Création des frames pour vidéo 8 secondes")
    print("=" * 50)
    
    # Paramètres
    duration = 8  # secondes
    fps = 12  # FPS réduit pour simplicité
    total_frames = duration * fps  # 96 frames
    width, height = 512, 512
    
    print(f"🎯 Génération de {total_frames} frames à {fps} FPS")
    print(f"📐 Résolution: {width}x{height}")
    
    # Créer le dossier de sortie
    output_dir = Path("output/video_frames")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Nettoyer les anciennes frames
    for old_frame in output_dir.glob("frame_*.png"):
        old_frame.unlink()
    
    # Générer les frames
    for frame_num in range(total_frames):
        progress = frame_num / total_frames
        
        # Créer la frame
        frame = create_animated_frame(frame_num, progress, width, height)
        
        # Sauvegarder
        frame_path = output_dir / f"frame_{frame_num:04d}.png"
        frame.save(frame_path)
        
        if frame_num % 12 == 0:  # Affichage toutes les secondes
            print(f"   📹 Frame {frame_num}/{total_frames} ({frame_num//12}s) - {frame_path.name}")
    
    print(f"✅ {total_frames} frames créées dans {output_dir}")
    return output_dir, fps

def create_animated_frame(frame_num, progress, width, height):
    """Crée une frame animée avec effets SkyReels"""
    
    # Couleurs animées
    r = int(30 + 100 * (0.5 + 0.5 * np.sin(progress * 4 * np.pi)))
    g = int(60 + 120 * (0.5 + 0.5 * np.cos(progress * 3 * np.pi)))
    b = int(120 + 80 * (0.5 + 0.5 * np.sin(progress * 5 * np.pi)))
    
    # Créer l'image avec dégradé radial
    img = Image.new('RGB', (width, height))
    
    center_x, center_y = width // 2, height // 2
    max_radius = min(width, height) // 2
    
    for y in range(height):
        for x in range(width):
            # Distance du centre
            dx = x - center_x
            dy = y - center_y
            distance = np.sqrt(dx*dx + dy*dy)
            
            # Normaliser
            norm_distance = min(distance / max_radius, 1.0)
            
            # Effet d'onde temporelle
            wave = np.sin(progress * 8 * np.pi + norm_distance * 6 * np.pi)
            
            # Couleurs finales
            final_r = max(0, min(255, int(r + wave * 40)))
            final_g = max(0, min(255, int(g + wave * 50)))
            final_b = max(0, min(255, int(b + wave * 30)))
            
            img.putpixel((x, y), (final_r, final_g, final_b))
    
    # Ajouter du texte
    draw = ImageDraw.Draw(img)
    
    try:
        font_large = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 48)
        font_small = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
    except:
        try:
            font_large = ImageFont.truetype("Arial.ttf", 48)
            font_small = ImageFont.truetype("Arial.ttf", 24)
        except:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
    
    # Texte principal
    main_text = "SkyReels-A1"
    try:
        bbox = draw.textbbox((0, 0), main_text, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
    except:
        text_width = len(main_text) * 30
        text_height = 48
    
    text_x = (width - text_width) // 2
    text_y = (height - text_height) // 2 - 40
    
    # Ombre
    draw.text((text_x + 3, text_y + 3), main_text, fill=(0, 0, 0), font=font_large)
    # Texte
    draw.text((text_x, text_y), main_text, fill=(255, 255, 255), font=font_large)
    
    # Sous-texte avec timing
    current_time = progress * 8
    sub_text = f"Vidéo Démonstration • {current_time:.1f}s / 8.0s"
    
    try:
        sub_bbox = draw.textbbox((0, 0), sub_text, font=font_small)
        sub_width = sub_bbox[2] - sub_bbox[0]
    except:
        sub_width = len(sub_text) * 12
    
    sub_x = (width - sub_width) // 2
    sub_y = text_y + 80
    
    draw.text((sub_x + 2, sub_y + 2), sub_text, fill=(0, 0, 0), font=font_small)
    draw.text((sub_x, sub_y), sub_text, fill=(200, 200, 200), font=font_small)
    
    # Barre de progression
    bar_width = 300
    bar_height = 10
    bar_x = (width - bar_width) // 2
    bar_y = height - 100
    
    # Fond de la barre
    draw.rectangle([bar_x, bar_y, bar_x + bar_width, bar_y + bar_height], 
                  fill=(40, 40, 40), outline=(100, 100, 100))
    
    # Progression
    progress_width = int(bar_width * progress)
    if progress_width > 0:
        draw.rectangle([bar_x, bar_y, bar_x + progress_width, bar_y + bar_height], 
                      fill=(0, 255, 150))
    
    # Particules animées
    for i in range(15):
        particle_progress = (progress * 2 + i * 0.1) % 1.0
        px = int(50 + particle_progress * (width - 100))
        py = int(100 + i * 25 + 20 * np.sin(progress * 6 * np.pi + i))
        
        size = int(2 + 3 * np.sin(progress * 10 * np.pi + i))
        alpha = int(100 + 100 * np.sin(progress * 8 * np.pi + i))
        
        if 0 <= px < width and 0 <= py < height and size > 0:
            color = (
                min(255, int(150 + alpha)),
                min(255, int(200 + alpha * 0.5)),
                255
            )
            draw.ellipse([px-size, py-size, px+size, py+size], fill=color)
    
    return img

def create_video_with_ffmpeg(frames_dir, fps):
    """Crée une vidéo MP4 avec FFmpeg"""
    print("\n🎬 Création de la vidéo avec FFmpeg...")
    
    output_dir = Path("output/test_videos")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    video_path = output_dir / f"skyreels_demo_{int(time.time())}.mp4"
    
    # Commande FFmpeg
    ffmpeg_cmd = [
        "ffmpeg",
        "-y",  # Overwrite output file
        "-framerate", str(fps),
        "-i", str(frames_dir / "frame_%04d.png"),
        "-c:v", "libx264",
        "-pix_fmt", "yuv420p",
        "-crf", "23",
        "-preset", "medium",
        str(video_path)
    ]
    
    try:
        print(f"🔧 Commande: {' '.join(ffmpeg_cmd)}")
        result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            file_size = video_path.stat().st_size / (1024 * 1024)
            print(f"✅ Vidéo créée avec succès!")
            print(f"📁 Fichier: {video_path}")
            print(f"📦 Taille: {file_size:.1f} MB")
            print(f"⏱️ Durée: 8 secondes")
            print(f"🎞️ FPS: {fps}")
            return str(video_path)
        else:
            print(f"❌ Erreur FFmpeg: {result.stderr}")
            return None
            
    except FileNotFoundError:
        print("❌ FFmpeg non trouvé. Installation requise:")
        print("   brew install ffmpeg  # macOS")
        return None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def create_video_alternative():
    """Méthode alternative sans FFmpeg"""
    print("\n🔄 Méthode alternative sans FFmpeg...")
    
    try:
        import moviepy.editor as mpy
        print("✅ MoviePy disponible")
        
        # Créer les frames
        frames_dir, fps = create_frames_for_video()
        
        # Charger les frames avec MoviePy
        frame_files = sorted(frames_dir.glob("frame_*.png"))
        
        clips = []
        for frame_file in frame_files:
            clip = mpy.ImageClip(str(frame_file), duration=1/fps)
            clips.append(clip)
        
        # Concaténer
        final_video = mpy.concatenate_videoclips(clips, method="compose")
        
        # Export
        output_dir = Path("output/test_videos")
        output_dir.mkdir(parents=True, exist_ok=True)
        video_path = output_dir / f"skyreels_moviepy_{int(time.time())}.mp4"
        
        final_video.write_videofile(
            str(video_path),
            fps=fps,
            codec='libx264',
            audio=False,
            verbose=False,
            logger=None
        )
        
        print(f"✅ Vidéo MoviePy créée: {video_path}")
        
        # Nettoyage
        final_video.close()
        for clip in clips:
            clip.close()
        
        return str(video_path)
        
    except Exception as e:
        print(f"❌ MoviePy échoué: {e}")
        return None

def main():
    """Fonction principale"""
    print("🎬 CRÉATION VIDÉO SKYREELS-A1 - 8 SECONDES")
    print("=" * 60)
    print("🎯 Objectif: Créer une vraie vidéo de 8 secondes")
    print("📱 Format: 512x512, 12 FPS")
    print("⚠️ Démonstration en attendant compatibilité CogVideoX")
    print()
    
    # Créer les frames
    frames_dir, fps = create_frames_for_video()
    
    # Essayer FFmpeg d'abord
    video_path = create_video_with_ffmpeg(frames_dir, fps)
    
    # Si FFmpeg échoue, essayer MoviePy
    if not video_path:
        video_path = create_video_alternative()
    
    print("\n" + "=" * 60)
    if video_path:
        print("🎉 SUCCÈS - Vidéo de 8 secondes créée!")
        print(f"📁 Fichier: {video_path}")
        print("✅ Vidéo prête pour intégration")
        
        print("\n📋 Caractéristiques:")
        print(f"   ⏱️ Durée: 8 secondes exactement")
        print(f"   🎞️ FPS: {fps}")
        print(f"   📐 Résolution: 512x512")
        print(f"   🎨 Animation: Dégradés et particules")
        print(f"   📝 Texte: SkyReels-A1 avec timer")
        
        print("\n🔄 Prochaines étapes:")
        print("   1. Mettre à jour diffusers pour CogVideoX")
        print("   2. Remplacer par vraie génération IA")
        print("   3. Intégrer dans le pipeline principal")
        print("   4. Tester avec prompts variés")
    else:
        print("❌ ÉCHEC - Impossible de créer la vidéo")
        print("🔧 Installez FFmpeg ou vérifiez MoviePy")
        print("   brew install ffmpeg  # macOS")

if __name__ == "__main__":
    main()
