2025-06-10 23:02:00,204 - ollama_manager - INFO - O<PERSON><PERSON> is available and working
2025-06-10 23:02:00,205 - __main__ - INFO - Enhanced Auto YouTube system initialized
2025-06-10 23:02:00,205 - __main__ - INFO - Ollama available: True
2025-06-10 23:02:00,205 - __main__ - INFO - PyTrends available: True
2025-06-10 23:02:00,205 - __main__ - INFO - PIL available: True
2025-06-10 23:02:00,205 - __main__ - INFO - Google API available: True
2025-06-10 23:02:00,205 - __main__ - INFO - Starting Enhanced Auto YouTube system
2025-06-10 23:02:00,205 - __main__ - INFO - Fetching trending keywords from Google Trends
2025-06-10 23:02:00,946 - __main__ - ERROR - Error fetching trending keywords: The request failed: Google returned a response with code 404
2025-06-10 23:02:00,947 - __main__ - INFO - Using fallback keywords: ['artificial intelligence', 'climate change', 'space exploration']
2025-06-10 23:02:00,947 - __main__ - INFO - Processing keyword 1/3: 'artificial intelligence'
2025-06-10 23:02:00,947 - __main__ - INFO - Processing keyword: 'artificial intelligence'
2025-06-10 23:02:00,947 - ollama_manager - INFO - Attempt 1: Generating script with model 'phi3'
2025-06-10 23:02:00,950 - ollama_manager - INFO - Calling Ollama with model 'phi3'
2025-06-10 23:02:34,532 - ollama_manager - INFO - Ollama call completed in 33.58 seconds
2025-06-10 23:02:34,553 - ollama_manager - INFO - Successfully generated script with model 'phi3'
2025-06-10 23:02:34,567 - audio_manager - ERROR - Error generating audio for 'artificial intelligence': sequence item 4: expected str instance, dict found
2025-06-10 23:02:34,568 - __main__ - ERROR - Failed to generate audio for 'artificial intelligence'
2025-06-10 23:02:34,568 - __main__ - WARNING - Failed to process 'artificial intelligence'
2025-06-10 23:02:34,568 - __main__ - INFO - Waiting 60 seconds before next keyword...
2025-06-10 23:03:34,583 - __main__ - INFO - Processing keyword 2/3: 'climate change'
2025-06-10 23:03:34,588 - __main__ - INFO - Processing keyword: 'climate change'
2025-06-10 23:03:34,588 - ollama_manager - INFO - Attempt 1: Generating script with model 'phi3'
2025-06-10 23:03:34,593 - ollama_manager - INFO - Calling Ollama with model 'phi3'
2025-06-10 23:03:56,812 - ollama_manager - INFO - Ollama call completed in 22.22 seconds
2025-06-10 23:03:56,816 - ollama_manager - INFO - Successfully generated script with model 'phi3'
2025-06-10 23:03:56,819 - audio_manager - ERROR - Error generating audio for 'climate change': sequence item 4: expected str instance, dict found
2025-06-10 23:03:56,819 - __main__ - ERROR - Failed to generate audio for 'climate change'
2025-06-10 23:03:56,819 - __main__ - WARNING - Failed to process 'climate change'
2025-06-10 23:03:56,820 - __main__ - INFO - Waiting 60 seconds before next keyword...
2025-06-10 23:04:56,829 - __main__ - INFO - Processing keyword 3/3: 'space exploration'
2025-06-10 23:04:56,833 - __main__ - INFO - Processing keyword: 'space exploration'
2025-06-10 23:04:56,834 - ollama_manager - INFO - Attempt 1: Generating script with model 'phi3'
2025-06-10 23:04:56,838 - ollama_manager - INFO - Calling Ollama with model 'phi3'
2025-06-10 23:05:18,210 - ollama_manager - INFO - Ollama call completed in 21.37 seconds
2025-06-10 23:05:18,216 - ollama_manager - INFO - Successfully generated script with model 'phi3'
2025-06-10 23:05:38,247 - audio_manager - INFO - Generated basic audio: /Users/<USER>/Documents/aicreation/output/temp/space_exploration_temp.mp3
2025-06-10 23:05:45,632 - audio_manager - INFO - Enhanced audio: /Users/<USER>/Documents/aicreation/output/temp/space_exploration_enhanced.mp3
2025-06-10 23:05:45,632 - audio_manager - INFO - Generated audio file: /Users/<USER>/Documents/aicreation/output/audio/space_exploration.mp3 (2118285 bytes)
2025-06-10 23:05:45,699 - audio_manager - INFO - Audio validation passed: /Users/<USER>/Documents/aicreation/output/audio/space_exploration.mp3
2025-06-10 23:05:45,701 - video_manager - INFO - Transcript created: /Users/<USER>/Documents/aicreation/output/transcripts/space_exploration.txt
2025-06-10 23:05:46,364 - video_manager - INFO - Blender is available and working
2025-06-10 23:05:46,365 - video_manager - INFO - Attempting to create professional video with Blender
2025-06-10 23:05:46,365 - video_manager - INFO - Creating video with Blender for 'space exploration'
2025-06-10 23:05:47,570 - video_manager - INFO - Blender rendering completed in 1.20 seconds
2025-06-10 23:05:47,570 - video_manager - ERROR - Blender failed: 
2025-06-10 23:05:47,570 - video_manager - WARNING - Blender failed, falling back to FFmpeg
2025-06-10 23:05:47,570 - video_manager - INFO - Creating video with FFmpeg
2025-06-10 23:05:47,591 - video_manager - INFO - Creating video with FFmpeg for 'space exploration'
2025-06-10 23:05:47,611 - video_manager - ERROR - FFmpeg failed: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers
  built with clang version 12.0.0
  configuration: --prefix=/Users/<USER>/demo/mc3/conda-bld/ffmpeg_1628925491858/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac --cc=arm64-apple-darwin20.0.0-clang --disable-doc --enable-avresample --enable-gmp --enable-hardcoded-tables --enable-libfreetype --enable-libvpx --enable-pthreads --enable-libopus --enable-postproc --enable-pic --enable-pthreads --enable-shared --enable-static --enable-version3 --enable-zlib --enable-libmp3lame --disable-nonfree --enable-gpl --enable-gnutls --disable-openssl --enable-libopenh264 --enable-libx264
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
Input #0, lavfi, from 'color=c=0x1a1a2e:s=1920x1080:d=105.888':
  Duration: N/A, start: 0.000000, bitrate: N/A
    Stream #0:0: Video: rawvideo (I420 / 0x30323449), yuv420p, 1920x1080 [SAR 1:1 DAR 16:9], 25 tbr, 25 tbn, 25 tbc
Input #1, mp3, from '/Users/<USER>/Documents/aicreation/output/audio/space_exploration.mp3':
  Metadata:
    encoder         : Lavf58.29.100
  Duration: 00:01:45.89, start: 0.046042, bitrate: 160 kb/s
    Stream #1:0: Audio: mp3, 24000 Hz, mono, fltp, 160 kb/s
[Parsed_drawtext_0 @ 0x143b2f2f0] No font filename provided
[AVFilterGraph @ 0x143b254f0] Error initializing filter 'drawtext' with args 'fontsize=60:fontcolor=white:x=(w-text_w)/2:y=h/3:text=Beyond Earth\: The Marvels and Missions of Space Exploration:enable=between(t,0,5)'
Error initializing complex filters.
Invalid argument

2025-06-10 23:05:47,611 - video_manager - INFO - Creating simple video with FFmpeg for 'space exploration'
2025-06-10 23:05:49,877 - video_manager - INFO - Simple FFmpeg video created: /Users/<USER>/Documents/aicreation/output/videos/space_exploration.mp4 (1154057 bytes)
2025-06-10 23:05:49,893 - video_manager - INFO - Video validation passed: /Users/<USER>/Documents/aicreation/output/videos/space_exploration.mp4 (108.1s, 1154057 bytes)
2025-06-10 23:05:50,349 - __main__ - INFO - Created thumbnail: /Users/<USER>/Documents/aicreation/output/thumbnails/space_exploration.jpg
2025-06-10 23:05:50,349 - __main__ - INFO - YouTube upload placeholder for 'space exploration'
2025-06-10 23:05:50,349 - __main__ - WARNING - YouTube upload failed for 'space exploration'
2025-06-10 23:05:50,349 - __main__ - INFO - Successfully processed keyword: 'space exploration'
2025-06-10 23:05:50,349 - __main__ - INFO - Successfully processed 'space exploration'
2025-06-10 23:05:50,349 - __main__ - INFO - Completed processing. Success rate: 1/3
2025-06-10 23:05:50,349 - __main__ - INFO - Enhanced Auto YouTube system completed successfully
2025-06-10 23:22:47,971 - ollama_manager - INFO - Ollama is available and working
2025-06-10 23:22:47,972 - enhanced_auto_youtube - INFO - Enhanced Auto YouTube system initialized
2025-06-10 23:22:47,972 - enhanced_auto_youtube - INFO - Ollama available: True
2025-06-10 23:22:47,972 - enhanced_auto_youtube - INFO - PyTrends available: True
2025-06-10 23:22:47,972 - enhanced_auto_youtube - INFO - PIL available: True
2025-06-10 23:22:47,972 - enhanced_auto_youtube - INFO - Google API available: True
2025-06-10 23:22:47,972 - enhanced_auto_youtube - INFO - Processing keyword: 'future of work'
2025-06-10 23:22:47,972 - ollama_manager - INFO - Attempt 1: Generating script with model 'phi3'
2025-06-10 23:22:47,973 - ollama_manager - INFO - Calling Ollama with model 'phi3'
2025-06-10 23:23:09,131 - ollama_manager - INFO - Ollama call completed in 21.15 seconds
2025-06-10 23:23:09,167 - ollama_manager - INFO - Successfully generated script with model 'phi3'
2025-06-10 23:23:26,194 - audio_manager - INFO - Generated basic audio: /Users/<USER>/Documents/aicreation/output/temp/future_of_work_temp.mp3
2025-06-10 23:23:31,913 - audio_manager - INFO - Enhanced audio: /Users/<USER>/Documents/aicreation/output/temp/future_of_work_enhanced.mp3
2025-06-10 23:23:31,914 - audio_manager - INFO - Generated audio file: /Users/<USER>/Documents/aicreation/output/audio/future_of_work.mp3 (1643085 bytes)
2025-06-10 23:23:31,971 - audio_manager - INFO - Audio validation passed: /Users/<USER>/Documents/aicreation/output/audio/future_of_work.mp3
2025-06-10 23:23:31,972 - video_manager - INFO - Transcript created: /Users/<USER>/Documents/aicreation/output/transcripts/future_of_work.txt
2025-06-10 23:23:32,531 - video_manager - INFO - Blender is available and working
2025-06-10 23:23:32,532 - video_manager - INFO - Attempting to create professional video with Blender
2025-06-10 23:23:32,532 - video_manager - INFO - Creating video with Blender for 'future of work'
2025-06-10 23:23:34,840 - video_manager - INFO - Blender rendering completed in 2.31 seconds
2025-06-10 23:23:34,841 - video_manager - ERROR - Blender failed: 
2025-06-10 23:23:34,841 - video_manager - WARNING - Blender failed, falling back to FFmpeg
2025-06-10 23:23:34,841 - video_manager - INFO - Creating video with FFmpeg
2025-06-10 23:23:34,854 - video_manager - INFO - Creating video with FFmpeg for 'future of work'
2025-06-10 23:23:34,869 - video_manager - ERROR - FFmpeg failed: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers
  built with clang version 12.0.0
  configuration: --prefix=/Users/<USER>/demo/mc3/conda-bld/ffmpeg_1628925491858/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac --cc=arm64-apple-darwin20.0.0-clang --disable-doc --enable-avresample --enable-gmp --enable-hardcoded-tables --enable-libfreetype --enable-libvpx --enable-pthreads --enable-libopus --enable-postproc --enable-pic --enable-pthreads --enable-shared --enable-static --enable-version3 --enable-zlib --enable-libmp3lame --disable-nonfree --enable-gpl --enable-gnutls --disable-openssl --enable-libopenh264 --enable-libx264
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
Input #0, lavfi, from 'color=c=0x1a1a2e:s=1920x1080:d=82.128':
  Duration: N/A, start: 0.000000, bitrate: N/A
    Stream #0:0: Video: rawvideo (I420 / 0x30323449), yuv420p, 1920x1080 [SAR 1:1 DAR 16:9], 25 tbr, 25 tbn, 25 tbc
Input #1, mp3, from '/Users/<USER>/Documents/aicreation/output/audio/future_of_work.mp3':
  Metadata:
    encoder         : Lavf58.29.100
  Duration: 00:01:22.13, start: 0.046042, bitrate: 160 kb/s
    Stream #1:0: Audio: mp3, 24000 Hz, mono, fltp, 160 kb/s
[Parsed_drawtext_0 @ 0x12362dc40] No font filename provided
[AVFilterGraph @ 0x12362d580] Error initializing filter 'drawtext' with args 'fontsize=60:fontcolor=white:x=(w-text_w)/2:y=h/3:text=Will robots rule us all? Jobs on Jeopardy!:enable=between(t,0,5)'
Error initializing complex filters.
Invalid argument

2025-06-10 23:23:34,870 - video_manager - INFO - Creating simple video with FFmpeg for 'future of work'
2025-06-10 23:23:36,329 - video_manager - INFO - Simple FFmpeg video created: /Users/<USER>/Documents/aicreation/output/videos/future_of_work.mp4 (918220 bytes)
2025-06-10 23:23:36,346 - video_manager - INFO - Video validation passed: /Users/<USER>/Documents/aicreation/output/videos/future_of_work.mp4 (84.4s, 918220 bytes)
2025-06-10 23:23:36,393 - enhanced_auto_youtube - INFO - Created thumbnail: /Users/<USER>/Documents/aicreation/output/thumbnails/future_of_work.jpg
2025-06-10 23:23:36,393 - enhanced_auto_youtube - INFO - YouTube upload placeholder for 'future of work'
2025-06-10 23:23:36,393 - enhanced_auto_youtube - WARNING - YouTube upload failed for 'future of work'
2025-06-10 23:23:36,393 - enhanced_auto_youtube - INFO - Successfully processed keyword: 'future of work'
