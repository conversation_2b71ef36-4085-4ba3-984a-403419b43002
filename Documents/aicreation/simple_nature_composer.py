#!/usr/bin/env python3
"""
simple_nature_composer.py - Compositeur simplifié pour vidéos nature avec Pixabay
"""

import os
import logging
import subprocess
import random
from pathlib import Path
from typing import Optional, List

from config import get_config
from pixabay_videos import PixabayVideoManager
from music_manager import MusicManager

logger = logging.getLogger(__name__)

class SimpleNatureComposer:
    """Compositeur simplifié pour créer des vidéos avec arrière-plan nature."""
    
    def __init__(self):
        """Initialize simple nature composer."""
        self.config = get_config()
        self.pixabay_manager = PixabayVideoManager()
        self.music_manager = MusicManager()
        
        logger.info("Simple Nature Composer initialized")
    
    def create_nature_video(self, audio_path: str, keyword: str, add_music: bool = True) -> Optional[str]:
        """Create video with multiple nature backgrounds and audio."""
        try:
            logger.info(f"Creating nature video for '{keyword}' with multiple Pixabay videos")

            # Determine video category
            video_category = None
            if hasattr(self.pixabay_manager, 'get_video_category_for_keyword'):
                video_category = self.pixabay_manager.get_video_category_for_keyword(keyword)

            # Get multiple videos (5-8 for variety)
            videos = []
            if self.pixabay_manager.available:
                logger.info(f"🎬 Downloading multiple Pixabay videos for category: {video_category}")
                if hasattr(self.pixabay_manager, 'get_videos_for_keyword'):
                    videos = self.pixabay_manager.get_videos_for_keyword(keyword, video_category, 8)
                else:
                    videos = self.pixabay_manager.get_nature_videos_for_keyword(keyword, 8)

            if not videos:
                logger.warning("No videos available, trying fallback keywords")
                # Try fallback keywords
                fallback_keywords = ["nature", "landscape", "forest", "ocean", "mountains"]
                for fallback in fallback_keywords:
                    if hasattr(self.pixabay_manager, 'get_videos_for_keyword'):
                        videos = self.pixabay_manager.get_videos_for_keyword(fallback, "nature", 5)
                    else:
                        videos = self.pixabay_manager.get_nature_videos_for_keyword(fallback, 5)
                    if videos:
                        logger.info(f"Found videos with fallback keyword: {fallback}")
                        break

            if not videos:
                logger.error("No videos available even with fallbacks")
                return None

            logger.info(f"✅ Downloaded {len(videos)} Pixabay videos for composition")

            # Get audio duration
            audio_duration = self._get_audio_duration(audio_path)
            if not audio_duration:
                audio_duration = 180  # Default 3 minutes

            # Create output path
            output_path = self.config["directories"]["videos"] / f"{keyword.replace(' ', '_')}.mp4"

            # Create video with multiple backgrounds
            video_path = self._create_multi_video_composition(videos, audio_path, audio_duration, output_path)

            if not video_path:
                return None

            # Add music if requested
            if add_music and self.music_manager:
                logger.info("🎵 Adding soft nature music")
                music_style = self.music_manager.get_music_style_for_keyword(keyword)
                video_path = self.music_manager.add_music_to_video(video_path, music_style)

            return video_path

        except Exception as e:
            logger.error(f"Error creating nature video: {e}")
            return None
    
    def _create_multi_video_composition(self, videos: List[str], audio_path: str,
                                       duration: float, output_path: Path) -> Optional[str]:
        """Create video composition with multiple videos."""
        try:
            logger.info(f"Creating composition with {len(videos)} videos")

            # Calculate duration per video segment
            segment_duration = duration / len(videos)
            min_segment_duration = 8  # Minimum 8 seconds per segment

            if segment_duration < min_segment_duration:
                # If too many videos for the duration, use fewer videos
                num_videos = max(1, int(duration / min_segment_duration))
                videos = videos[:num_videos]
                segment_duration = duration / len(videos)
                logger.info(f"Adjusted to {len(videos)} videos with {segment_duration:.1f}s each")

            # Create video segments
            video_segments = []
            for i, video_path in enumerate(videos):
                segment_output = output_path.parent / f"segment_{i}.mp4"

                # Create segment with crossfade transitions
                cmd = [
                    "ffmpeg", "-y",
                    "-i", video_path,
                    "-t", str(segment_duration),
                    "-vf", f"scale=1920:1080:force_original_aspect_ratio=increase,crop=1920:1080,fade=in:0:30,fade=out:{int(segment_duration*30-30)}:30",
                    "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                    str(segment_output)
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

                if result.returncode == 0 and segment_output.exists():
                    video_segments.append(str(segment_output))
                    logger.info(f"✅ Created segment {i+1}/{len(videos)}")
                else:
                    logger.warning(f"⚠️ Failed to create segment {i+1}: {result.stderr}")

            if not video_segments:
                logger.error("No video segments created")
                return None

            # Concatenate all segments
            concat_file = output_path.parent / "concat_list.txt"
            with open(concat_file, 'w') as f:
                for segment in video_segments:
                    f.write(f"file '{segment}'\n")

            # Combine segments and add audio
            cmd = [
                "ffmpeg", "-y",
                "-f", "concat", "-safe", "0", "-i", str(concat_file),
                "-i", audio_path,
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-c:a", "aac", "-b:a", "192k",
                "-shortest",
                str(output_path)
            ]

            logger.info("🎬 Combining all segments with audio...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            # Cleanup temporary files
            for segment in video_segments:
                Path(segment).unlink(missing_ok=True)
            concat_file.unlink(missing_ok=True)

            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"✅ Multi-video composition created: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"Composition failed: {result.stderr}")
                # Fallback to simple single video
                return self._create_simple_nature_video(videos[0], audio_path, duration, output_path)

        except Exception as e:
            logger.error(f"Error creating multi-video composition: {e}")
            # Fallback to simple single video
            if videos:
                return self._create_simple_nature_video(videos[0], audio_path, duration, output_path)
            return None

    def _select_best_video(self, nature_videos: List[str]) -> Optional[str]:
        """Select the best nature video from the list."""
        if not nature_videos:
            return None

        # For now, just select the first one
        # Could add logic to select based on duration, quality, etc.
        return nature_videos[0]
    
    def _create_simple_nature_video(self, nature_video: str, audio_path: str, 
                                  duration: float, output_path: Path) -> Optional[str]:
        """Create simple video with nature background."""
        try:
            # Simple approach: loop nature video and add audio
            cmd = [
                "ffmpeg", "-y",
                "-stream_loop", "-1",  # Loop nature video
                "-i", nature_video,
                "-i", audio_path,
                "-t", str(duration),  # Duration from audio
                "-c:v", "libx264",
                "-c:a", "aac",
                "-b:a", "192k",
                "-shortest",  # Stop when shortest input ends
                str(output_path)
            ]
            
            logger.info(f"Creating simple nature video: {output_path}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"✅ Nature video created: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error(f"FFmpeg failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating simple nature video: {e}")
            return None
    
    def _get_audio_duration(self, audio_path: str) -> Optional[float]:
        """Get audio duration in seconds."""
        try:
            cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        
        return None

class ShortVideoConfig:
    """Configuration pour la génération de vidéos courtes"""
    def __init__(self):
        # Format vertical 9:16
        self.width = 1080
        self.height = 1920
        self.fps = 30
        self.duration = 8  # Durée fixe de 8 secondes
        self.font_size = 72  # Police plus grande pour le format vertical
        self.font = "Arial"
        self.background_color = (20, 20, 20)
        self.text_color = (255, 255, 255)
        self.music_volume = 0.4
        self.output_dir = Path("output/shorts")
        self.output_dir.mkdir(parents=True, exist_ok=True)

class ShortVideoCreator:
    """Crée des vidéos courtes avec effets modernes"""
    
    def __init__(self, config: ShortVideoConfig):
        self.config = config
        self.sky_reels = SkyReelsManager()
    
    def create_short(
        self,
        prompt: str,
        text: str,
        music_path: Optional[str] = None
    ) -> str:
        """
        Crée une vidéo courte avec une image générée et du texte animé.
        
        Args:
            prompt: Description pour la génération d'image
            text: Texte à afficher sur la vidéo
            music_path: Chemin vers la musique de fond (optionnel)
            
        Returns:
            Chemin vers la vidéo générée
        """
        try:
            # Génère l'image de fond avec SkyReels
            background_img = self.sky_reels.generate_image(
                prompt=prompt,
                width=self.config.width,
                height=self.config.height
            )
            
            # Convertit l'image numpy en clip vidéo
            background = mpy.ImageClip(background_img).set_duration(self.config.duration)
            
            # Crée les clips de texte avec animation
            text_clips = self._create_animated_text(text)
            
            # Compose la vidéo
            video = mpy.CompositeVideoClip(
                [background] + text_clips,
                size=(self.config.width, self.config.height)
            )
            
            # Ajoute la musique si fournie
            if music_path and os.path.exists(music_path):
                audio = mpy.AudioFileClip(music_path)
                # Ajuste la durée de la musique
                audio = audio.subclip(0, self.config.duration)
                audio = audio.volumex(self.config.music_volume)
                video = video.set_audio(audio)
            
            # Exporte la vidéo
            output_path = str(self.config.output_dir / f"short_{random.randint(1000, 9999)}.mp4")
            video.write_videofile(
                output_path,
                fps=self.config.fps,
                codec='libx264',
                audio_codec='aac',
                preset='ultrafast'  # Pour un rendu rapide
            )
            
            return output_path
            
        except Exception as e:
            logger.error(f"Erreur création vidéo courte: {e}")
            return ""
            
    def _create_animated_text(self, text: str) -> List[mpy.VideoClip]:
        """Crée des clips de texte avec animation moderne"""
        clips = []
        words = text.split()
        duration_per_word = self.config.duration / (len(words) + 1)
        
        for i, word in enumerate(words):
            # Crée le clip de texte
            txt_clip = mpy.TextClip(
                word,
                font=self.config.font,
                fontsize=self.config.font_size,
                color=self.config.text_color,
                stroke_color='black',
                stroke_width=2
            )
            
            # Position verticale basée sur le nombre de mots
            y_pos = (self.config.height * 0.4) + (i * self.config.font_size * 1.5)
            
            # Animation d'apparition
            txt_clip = (txt_clip
                       .set_position(("center", y_pos))
                       .set_start(i * duration_per_word)
                       .set_duration(self.config.duration - (i * duration_per_word))
                       .crossfadein(0.5)
                       .crossfadeout(0.5))
            
            clips.append(txt_clip)
        
        return clips

# Test function
def test_simple_composer():
    """Test the simple nature composer."""
    print("🌿 TEST DU COMPOSITEUR NATURE SIMPLIFIÉ")
    print("=" * 50)
    
    try:
        composer = SimpleNatureComposer()
        
        # Check if we have audio from previous generation
        audio_path = "output/audio/nature.mp3"
        
        if not Path(audio_path).exists():
            print(f"❌ Audio non trouvé: {audio_path}")
            print("💡 Créez d'abord l'audio avec: python create_nature_video.py")
            return
        
        print(f"✅ Audio trouvé: {audio_path}")
        
        # Test creation
        print("🎬 Création de la vidéo nature avec arrière-plan Pixabay...")
        
        video_path = composer.create_nature_video(audio_path, "nature_test", add_music=True)
        
        if video_path and Path(video_path).exists():
            file_size = Path(video_path).stat().st_size / (1024 * 1024)
            print(f"✅ Vidéo créée: {video_path} ({file_size:.1f} MB)")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
        else:
            print("❌ Échec de création")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Test de génération de vidéo courte"""
    config = ShortVideoConfig()
    creator = ShortVideoCreator(config)
    
    prompt = "Un paysage naturel magnifique avec montagnes majestueuses au coucher du soleil"
    text = "Découvrez la beauté de la nature"
    
    # Utilise une musique de la bibliothèque si disponible
    music_dir = Path("assets/music")
    music_path = None
    if music_dir.exists():
        music_files = list(music_dir.glob("*.mp3"))
        if music_files:
            music_path = str(music_files[0])
    
    video_path = creator.create_short(prompt, text, music_path)
    if video_path:
        logger.info(f"Vidéo courte créée: {video_path}")
    else:
        logger.error("Échec de la création de la vidéo courte")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_simple_composer()
    main()
