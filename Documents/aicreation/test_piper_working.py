#!/usr/bin/env python3
"""
test_piper_working.py - Test Piper TTS avec notre module
"""

import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_piper_module():
    """Test our Piper TTS module."""
    print("🎤 TEST MODULE PIPER TTS")
    print("=" * 40)
    
    try:
        from piper_tts import PiperTTS
        
        # Initialize Piper
        piper = PiperTTS()
        
        print(f"📍 Exécutable: {piper.piper_executable}")
        print(f"📁 Dossier modèles: {piper.models_dir}")
        print(f"✅ Disponible: {piper.available}")
        
        if not piper.available:
            print("❌ Piper TTS non disponible")
            return False
        
        # Test synthesis
        test_text = "Bonjour ! Ceci est un test de Piper TTS intégré dans notre système de création de vidéos YouTube."
        output_file = "test_piper_output.wav"
        
        print(f"\n🧪 Test de synthèse...")
        print(f"📝 Texte: {test_text}")
        
        success = piper.synthesize_speech(test_text, output_file, "test")
        
        if success:
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size / 1024
                print(f"✅ Synthèse réussie!")
                print(f"📁 Fichier: {output_file} ({file_size:.1f} KB)")
                
                # Cleanup
                Path(output_file).unlink(missing_ok=True)
                return True
            else:
                print("❌ Fichier de sortie non créé")
        else:
            print("❌ Synthèse échouée")
        
        return False
        
    except ImportError:
        print("❌ Module piper_tts non trouvé")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_manager_with_piper():
    """Test Piper integration in AudioManager."""
    print("\n🎵 TEST INTÉGRATION AUDIO MANAGER")
    print("-" * 40)
    
    try:
        from audio_manager import AudioManager
        
        # Initialize audio manager
        audio_manager = AudioManager()
        
        print(f"🎤 Systèmes TTS disponibles: {list(audio_manager.tts_systems.keys())}")
        
        if "piper" in audio_manager.tts_systems:
            print("✅ Piper TTS intégré dans AudioManager!")
            
            # Test audio generation
            test_script = {
                "title": "Test Piper TTS",
                "introduction": "Bonjour et bienvenue dans ce test de Piper TTS.",
                "key_point_1": "Piper TTS est un système de synthèse vocale local de haute qualité.",
                "key_point_2": "Il produit des voix très naturelles sans nécessiter de connexion internet.",
                "conclusion": "Merci d'avoir écouté ce test de Piper TTS."
            }
            
            print("🔄 Génération audio avec Piper...")
            audio_path = audio_manager.generate_audio(test_script, "piper_test")
            
            if audio_path and Path(audio_path).exists():
                file_size = Path(audio_path).stat().st_size / (1024 * 1024)
                print(f"✅ Audio généré avec Piper!")
                print(f"📁 Fichier: {audio_path}")
                print(f"📊 Taille: {file_size:.1f} MB")
                return True
            else:
                print("❌ Échec génération audio")
                return False
        else:
            print("⚠️ Piper TTS non intégré dans AudioManager")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_piper_nature_video():
    """Create a nature video with Piper TTS."""
    print("\n🌿 CRÉATION VIDÉO NATURE AVEC PIPER")
    print("-" * 40)
    
    try:
        from simple_nature_composer import SimpleNatureComposer
        from audio_manager import AudioManager
        
        # Force use of Piper by creating a custom audio manager
        audio_manager = AudioManager()
        
        if "piper" not in audio_manager.tts_systems:
            print("❌ Piper TTS non disponible")
            return False
        
        # Create nature script optimized for Piper
        nature_script = {
            "title": "La Beauté de la Nature avec Piper TTS",
            "introduction": "Découvrez la magnificence de la nature à travers cette vidéo générée avec Piper TTS, un système de synthèse vocale local de haute qualité.",
            "key_point_1": "La nature nous offre des paysages à couper le souffle. Des forêts verdoyantes aux océans infinis, chaque élément contribue à la beauté de notre planète.",
            "key_point_2": "Les montagnes majestueuses, les rivières cristallines et les prairies fleuries forment un tableau vivant en constante évolution.",
            "key_point_3": "Préserver cette richesse naturelle est notre responsabilité commune pour les générations futures et la survie de notre écosystème.",
            "conclusion": "Merci d'avoir découvert ces merveilles naturelles avec nous. Cette narration a été générée par Piper TTS, démontrant la qualité exceptionnelle de la synthèse vocale locale."
        }
        
        print("🔄 Génération audio avec Piper TTS...")
        audio_path = audio_manager.generate_audio(nature_script, "nature_piper")
        
        if not audio_path or not Path(audio_path).exists():
            print("❌ Échec génération audio")
            return False
        
        audio_size = Path(audio_path).stat().st_size / (1024 * 1024)
        print(f"✅ Audio Piper généré: {audio_path} ({audio_size:.1f} MB)")
        
        # Create video with nature background
        composer = SimpleNatureComposer()
        
        print("🎬 Création vidéo avec arrière-plan nature...")
        video_path = composer.create_nature_video(audio_path, "nature_piper", add_music=True)
        
        if video_path and Path(video_path).exists():
            video_size = Path(video_path).stat().st_size / (1024 * 1024)
            
            print("\n🎉 VIDÉO NATURE AVEC PIPER CRÉÉE!")
            print("=" * 50)
            print(f"📁 Fichier: {video_path}")
            print(f"📊 Taille: {video_size:.1f} MB")
            print(f"🌐 Voir: file://{Path(video_path).absolute()}")
            print()
            print("🎤 CARACTÉRISTIQUES:")
            print("   ✅ Narration: Piper TTS (local, haute qualité)")
            print("   ✅ Arrière-plan: Vidéos nature HD Pixabay")
            print("   ✅ Musique: Fréquences apaisantes sans percussion")
            print("   ✅ Qualité: HD 1920x1080")
            
            return True
        else:
            print("❌ Échec création vidéo")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🎤 TEST COMPLET PIPER TTS")
    print("=" * 50)
    
    tests = [
        ("Module Piper TTS", test_piper_module),
        ("Intégration AudioManager", test_audio_manager_with_piper),
        ("Vidéo nature avec Piper", create_piper_nature_video)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DES TESTS:")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 PIPER TTS FONCTIONNE PARFAITEMENT!")
        print("🌟 Votre système utilise maintenant Piper TTS local")
        print("🎵 Qualité vocale exceptionnelle sans connexion internet")
    elif passed > 0:
        print(f"\n⚠️ Fonctionnement partiel ({passed}/{total})")
        print("🔧 Certains composants nécessitent des ajustements")
    else:
        print("\n❌ Piper TTS ne fonctionne pas")
        print("💡 Vérifiez l'installation et la configuration")

if __name__ == "__main__":
    main()
