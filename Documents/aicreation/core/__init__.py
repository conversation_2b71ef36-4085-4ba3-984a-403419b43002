"""
Core module for the YouTube Video Creation System.

This module provides the foundational components for a professional-grade
video creation system with enterprise-level architecture patterns.
"""

__version__ = "2.0.0"
__author__ = "Senior Python Developer"
__description__ = "Professional YouTube Video Creation System"

from .exceptions import *
from .models import *
from .interfaces import *

__all__ = [
    # Exceptions
    "VideoCreationError",
    "TTSError", 
    "VideoProcessingError",
    "ConfigurationError",
    "APIError",
    
    # Models
    "VideoRequest",
    "VideoResponse",
    "AudioConfig",
    "VideoConfig",
    "ScriptData",
    
    # Interfaces
    "ITTSProvider",
    "IVideoProvider", 
    "IScriptGenerator",
    "IAudioProcessor",
    "IVideoProcessor"
]
