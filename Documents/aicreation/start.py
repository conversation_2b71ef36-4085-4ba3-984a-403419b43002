#!/usr/bin/env python3
"""
start.py - Script de démarrage simple pour créer des vidéos YouTube
"""

import sys
import os
from pathlib import Path

def print_header():
    """Print startup header."""
    print("🎬" * 25)
    print("🚀 CRÉATEUR DE VIDÉOS YOUTUBE AUTOMATISÉ 🚀")
    print("🎬" * 25)
    print()
    print("✨ Système complet avec IA, voix réalistes et vidéos HD")
    print()

def check_system():
    """Check system requirements."""
    print("🔍 Vérification du système...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ requis")
        return False
    
    # Check essential modules
    try:
        import ollama
        print("✅ Ollama disponible")
    except ImportError:
        print("⚠️ Ollama non installé (pip install ollama)")
    
    try:
        import gtts
        print("✅ gTTS disponible")
    except ImportError:
        print("❌ gTTS manquant (pip install gtts)")
        return False
    
    try:
        import PIL
        print("✅ Pillow disponible")
    except ImportError:
        print("❌ Pillow manquant (pip install Pillow)")
        return False
    
    # Check premium TTS
    premium_count = 0
    
    if os.getenv('OPENAI_API_KEY'):
        print("✅ OpenAI TTS configuré")
        premium_count += 1
    
    if os.getenv('ELEVENLABS_API_KEY'):
        print("✅ ElevenLabs TTS configuré")
        premium_count += 1
    
    if os.getenv('AZURE_SPEECH_KEY'):
        print("✅ Azure TTS configuré")
        premium_count += 1

    # Check Pixabay
    if os.getenv('PIXABAY_API_KEY'):
        print("✅ Pixabay Nature configuré")
        premium_count += 1

    if premium_count == 0:
        print("⚠️ Aucun système premium configuré (utilise fallbacks)")
    else:
        print(f"🌟 {premium_count} système(s) premium configuré(s)")

    print()
    return True

def show_menu():
    """Show main menu."""
    print("🎯 QUE VOULEZ-VOUS FAIRE ?")
    print()
    print("1. 🚀 Créer une vidéo (mot-clé personnalisé)")
    print("2. 🎲 Créer une vidéo (sujet aléatoire)")
    print("3. 🧪 Tester le système")
    print("4. ⚙️ Configuration TTS premium")
    print("5. 📖 Aide et documentation")
    print()

def create_video_custom():
    """Create video with custom keyword."""
    keyword = input("🎯 Entrez votre mot-clé/sujet: ").strip()
    
    if not keyword:
        print("❌ Mot-clé vide")
        return
    
    print(f"🎬 Création d'une vidéo sur: '{keyword}'")
    print("⏳ Cela peut prendre 1-3 minutes...")
    
    try:
        import subprocess
        result = subprocess.run([
            "python", "test_single_keyword.py", keyword
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Vidéo créée avec succès!")
            
            # Show generated files
            video_path = Path("output/videos") / f"{keyword.replace(' ', '_')}.mp4"
            if video_path.exists():
                print(f"📁 Vidéo: {video_path}")
                print(f"🌐 Ouvrir: file://{video_path.absolute()}")
        else:
            print("❌ Erreur lors de la création")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout - Le processus prend trop de temps")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def create_video_random():
    """Create video with random topic."""
    topics = [
        "intelligence artificielle",
        "changement climatique",
        "exploration spatiale",
        "énergie renouvelable",
        "technologie blockchain",
        "réalité virtuelle",
        "voitures électriques",
        "médecine du futur"
    ]
    
    import random
    keyword = random.choice(topics)
    
    print(f"🎲 Sujet sélectionné: '{keyword}'")
    print("🎬 Création de la vidéo...")
    print("⏳ Cela peut prendre 1-3 minutes...")
    
    try:
        import subprocess
        result = subprocess.run([
            "python", "test_single_keyword.py", keyword
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Vidéo créée avec succès!")
            
            # Show generated files
            video_path = Path("output/videos") / f"{keyword.replace(' ', '_')}.mp4"
            if video_path.exists():
                print(f"📁 Vidéo: {video_path}")
                print(f"🌐 Ouvrir: file://{video_path.absolute()}")
        else:
            print("❌ Erreur lors de la création")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_system():
    """Test the system."""
    print("🧪 Test du système avec un exemple simple...")
    
    try:
        import subprocess
        result = subprocess.run([
            "python", "test_single_keyword.py", "test système"
        ], capture_output=True, text=True, timeout=180)
        
        if result.returncode == 0:
            print("✅ Test réussi! Le système fonctionne correctement.")
        else:
            print("❌ Test échoué")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Erreur de test: {e}")

def show_tts_config():
    """Show TTS configuration help."""
    print("⚙️ CONFIGURATION TTS PREMIUM:")
    print()
    print("🤖 OpenAI TTS (Recommandé - $0.075 par vidéo):")
    print("   1. Créez un compte: https://platform.openai.com/")
    print("   2. Générez une API key")
    print("   3. export OPENAI_API_KEY='votre_cle'")
    print()
    print("🌟 ElevenLabs (Qualité maximale - $1.50 par vidéo):")
    print("   1. Créez un compte: https://elevenlabs.io/")
    print("   2. Obtenez votre API key")
    print("   3. export ELEVENLABS_API_KEY='votre_cle'")
    print()
    print()
    print("🌿 Pixabay Nature (Gratuit - Vidéos magnifiques):")
    print("   1. Créez un compte: https://pixabay.com/")
    print("   2. Obtenez votre API key gratuite")
    print("   3. export PIXABAY_API_KEY='votre_cle'")
    print("   4. python test_pixabay.py")
    print()
    print("📖 Guides complets:")
    print("   • TTS_COMPARISON_GUIDE.md - Comparaison des voix")
    print("   • PIXABAY_SETUP.md - Installation Pixabay")

def show_help():
    """Show help and documentation."""
    print("📖 AIDE ET DOCUMENTATION:")
    print()
    print("📁 Fichiers importants:")
    print("   • README.md - Documentation complète")
    print("   • TTS_COMPARISON_GUIDE.md - Guide des voix")
    print("   • requirements.txt - Dépendances")
    print()
    print("🎬 Fichiers générés dans output/:")
    print("   • videos/ - Vidéos MP4")
    print("   • audio/ - Fichiers audio")
    print("   • thumbnails/ - Miniatures")
    print("   • scripts/ - Scripts générés")
    print()
    print("🔧 Scripts utiles:")
    print("   • python start.py - Ce menu")
    print("   • python test_single_keyword.py 'sujet' - Test rapide")
    print("   • python enhanced_auto_youtube.py - Script complet")

def main():
    """Main function."""
    print_header()
    
    if not check_system():
        print("❌ Système non prêt. Installez les dépendances manquantes.")
        print("💡 pip install -r requirements.txt")
        return
    
    while True:
        show_menu()
        
        choice = input("Votre choix (1-5): ").strip()
        print()
        
        if choice == "1":
            create_video_custom()
        elif choice == "2":
            create_video_random()
        elif choice == "3":
            test_system()
        elif choice == "4":
            show_tts_config()
        elif choice == "5":
            show_help()
        else:
            print("❌ Choix invalide")
        
        print()
        continue_choice = input("Continuer? (o/n): ").strip().lower()
        if continue_choice in ['n', 'non', 'no']:
            break
        print()
    
    print("👋 Au revoir! Vos vidéos sont dans le dossier output/")

if __name__ == "__main__":
    main()
