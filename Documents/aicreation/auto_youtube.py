#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Auto YouTube Video Generator

This script automates the creation and publishing of YouTube videos based on trending topics.
It fetches trending keywords, generates scripts using Ollama, converts text to speech,
creates videos with visual elements, and uploads them to YouTube.
"""

import os
import time
import logging
import random
import json
import datetime
from typing import List, Dict, Any, Tuple
import requests
from pathlib import Path

# Third-party imports
import ollama
from pytrends.request import TrendReq
from gtts import gTTS
from moviepy.editor import (
    TextClip, ColorClip, CompositeVideoClip, AudioFileClip,
    concatenate_videoclips, VideoFileClip, CompositeAudioClip
)
from PIL import Image, ImageDraw, ImageFont
import google_auth_oauthlib.flow
import googleapiclient.discovery
import googleapiclient.errors
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("auto_youtube.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
CATEGORIES = ["history", "economy", "news"]
OUTPUT_DIR = Path("output")
CREDENTIALS_FILE = "client_secrets.json"
TOKEN_FILE = "token.json"
SCOPES = ["https://www.googleapis.com/auth/youtube.upload",
          "https://www.googleapis.com/auth/youtube.readonly"]
OLLAMA_MODEL = "llama3"  # Change to your preferred model
PUBLICATION_HOUR = 18  # 6:00 PM

class AutoYouTube:
    """Main class for YouTube video automation."""

    def __init__(self):
        """Initialize the AutoYouTube class."""
        self.pytrends = TrendReq(hl='en-US', tz=0)
        self.youtube = None
        self.setup_directories()

    def setup_directories(self):
        """Create necessary directories if they don't exist."""
        OUTPUT_DIR.mkdir(exist_ok=True)
        for subdir in ["scripts", "audio", "videos", "thumbnails"]:
            (OUTPUT_DIR / subdir).mkdir(exist_ok=True)

    def authenticate_youtube(self):
        """Authenticate with YouTube API using OAuth2."""
        credentials = None

        # Load saved credentials if they exist
        if os.path.exists(TOKEN_FILE):
            credentials = Credentials.from_authorized_user_info(
                json.load(open(TOKEN_FILE))
            )

        # If credentials are invalid or don't exist, get new ones
        if not credentials or not credentials.valid:
            if credentials and credentials.expired and credentials.refresh_token:
                credentials.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    CREDENTIALS_FILE, SCOPES
                )
                credentials = flow.run_local_server(port=0)

            # Save credentials for future use
            with open(TOKEN_FILE, 'w') as token:
                token.write(credentials.to_json())

        # Build the YouTube API client
        self.youtube = googleapiclient.discovery.build(
            "youtube", "v3", credentials=credentials
        )
        logger.info("Successfully authenticated with YouTube API")

    def get_trending_keywords(self) -> List[str]:
        """Fetch trending keywords from Google Trends for specified categories."""
        trending_keywords = []

        try:
            for category in CATEGORIES:
                # Get daily trending searches
                trending_searches = self.pytrends.trending_searches(pn='united_states')
                # Filter by category using realtime_trending_searches
                self.pytrends.build_payload(kw_list=[category])

                # Get top 5 keywords for each category
                for keyword in trending_searches.iloc[:5, 0].tolist():
                    if keyword not in trending_keywords:
                        trending_keywords.append(keyword)

            logger.info(f"Found {len(trending_keywords)} trending keywords")
            return trending_keywords[:5]  # Limit to top 5 overall

        except Exception as e:
            logger.error(f"Error fetching trending keywords: {str(e)}")
            return ["history facts", "economic trends", "current events"]  # Fallback keywords

    def generate_script(self, keyword: str) -> Dict[str, str]:
        """Generate a video script using Ollama."""
        prompt = f"""
        Write a 3-minute video script about "{keyword}" with the following structure:
        1. Title: An engaging title for the video
        2. Introduction: Brief introduction to the topic (30 seconds)
        3. 3 Key Points: Three main points about the topic (2 minutes)
        4. Conclusion: A brief conclusion (30 seconds)

        Format the response as a JSON with the following keys:
        "title", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion"
        """

        try:
            response = ollama.chat(model=OLLAMA_MODEL, messages=[
                {'role': 'user', 'content': prompt}
            ])

            # Extract JSON from response
            content = response['message']['content']
            # Find JSON content (might be wrapped in markdown code blocks)
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_str = content.split("```")[1].strip()
            else:
                json_str = content

            script = json.loads(json_str)

            # Save script to file
            script_path = OUTPUT_DIR / "scripts" / f"{keyword.replace(' ', '_')}.json"
            with open(script_path, 'w', encoding='utf-8') as f:
                json.dump(script, f, ensure_ascii=False, indent=4)

            logger.info(f"Generated script for '{keyword}'")
            return script

        except Exception as e:
            logger.error(f"Error generating script for '{keyword}': {str(e)}")
            # Return a basic script as fallback
            return {
                "title": f"Understanding {keyword.title()}",
                "introduction": f"Today we're exploring {keyword}.",
                "key_point_1": f"First point about {keyword}.",
                "key_point_2": f"Second point about {keyword}.",
                "key_point_3": f"Third point about {keyword}.",
                "conclusion": f"That's all about {keyword}. Thanks for watching!"
            }

    def text_to_speech(self, script: Dict[str, str], keyword: str) -> str:
        """Convert script to speech using gTTS and save as MP3."""
        # Combine script parts into a single text
        full_text = (
            f"{script['introduction']} "
            f"First key point: {script['key_point_1']} "
            f"Second key point: {script['key_point_2']} "
            f"Third key point: {script['key_point_3']} "
            f"{script['conclusion']}"
        )

        try:
            audio_path = OUTPUT_DIR / "audio" / f"{keyword.replace(' ', '_')}.mp3"

            # Convert text to speech
            tts = gTTS(text=full_text, lang='en', slow=False)
            tts.save(str(audio_path))

            logger.info(f"Generated audio for '{keyword}'")
            return str(audio_path)

        except Exception as e:
            logger.error(f"Error generating audio for '{keyword}': {str(e)}")
            return None

    def create_video(self, script: Dict[str, str], audio_path: str, keyword: str) -> str:
        """Create a video with scrolling text and background music."""
        try:
            # Load audio file
            audio_clip = AudioFileClip(audio_path)
            duration = audio_clip.duration

            # Create background (neutral color)
            bg_color = (240, 240, 240)  # Light gray
            background = ColorClip(size=(1280, 720), color=bg_color, duration=duration)

            # Create text clips for each section

            # Title
            title_clip = TextClip(
                script['title'],
                fontsize=60,
                color='black',
                bg_color=bg_color,
                size=(1200, None),
                method='caption',
                align='center'
            ).set_position(('center', 100)).set_duration(duration)

            # Introduction
            intro_clip = TextClip(
                script['introduction'],
                fontsize=36,
                color='black',
                bg_color=bg_color,
                size=(1000, None),
                method='caption',
                align='center'
            ).set_position(('center', 250)).set_duration(duration)

            # Create scrolling text for key points
            key_points_text = (
                f"Key Point 1: {script['key_point_1']}\n\n"
                f"Key Point 2: {script['key_point_2']}\n\n"
                f"Key Point 3: {script['key_point_3']}"
            )

            key_points_clip = TextClip(
                key_points_text,
                fontsize=32,
                color='black',
                bg_color=bg_color,
                size=(1000, None),
                method='caption',
                align='center'
            )

            # Animate the key points to scroll up
            key_points_height = key_points_clip.h
            start_pos = 720  # Start below the screen
            end_pos = 720 - key_points_height - 100  # End position (above conclusion)

            # Create scrolling animation
            def scroll_position(t):
                # Linear interpolation from start_pos to end_pos
                scroll_duration = duration * 0.6  # Use 60% of the video for scrolling
                scroll_start = duration * 0.2  # Start scrolling after 20% of the video

                if t < scroll_start:
                    return ('center', start_pos)
                elif t > scroll_start + scroll_duration:
                    return ('center', end_pos)
                else:
                    progress = (t - scroll_start) / scroll_duration
                    y_pos = start_pos + progress * (end_pos - start_pos)
                    return ('center', y_pos)

            animated_key_points = key_points_clip.set_position(scroll_position).set_duration(duration)

            # Conclusion
            conclusion_clip = TextClip(
                f"Conclusion: {script['conclusion']}",
                fontsize=36,
                color='black',
                bg_color=bg_color,
                size=(1000, None),
                method='caption',
                align='center'
            ).set_position(('center', 600)).set_duration(duration)

            # Combine all clips
            video = CompositeVideoClip([
                background,
                title_clip,
                intro_clip,
                animated_key_points,
                conclusion_clip
            ])

            # Add audio
            video = video.set_audio(audio_clip)

            # Add background music (assuming you have a file named 'background_music.mp3')
            try:
                music_path = "background_music.mp3"
                if os.path.exists(music_path):
                    bg_music = AudioFileClip(music_path).volumex(0.1).set_duration(duration)
                    final_audio = CompositeAudioClip([audio_clip, bg_music])
                    video = video.set_audio(final_audio)
            except Exception as e:
                logger.warning(f"Could not add background music: {str(e)}")

            # Save the video
            video_path = OUTPUT_DIR / "videos" / f"{keyword.replace(' ', '_')}.mp4"
            video.write_videofile(
                str(video_path),
                fps=24,
                codec='libx264',
                audio_codec='aac',
                threads=4
            )

            logger.info(f"Created video for '{keyword}'")
            return str(video_path)

        except Exception as e:
            logger.error(f"Error creating video for '{keyword}': {str(e)}")
            return None

    def create_thumbnail(self, script: Dict[str, str], keyword: str) -> str:
        """Generate a thumbnail with the title."""
        try:
            # Create a blank image with dimensions 1280x720 (YouTube thumbnail size)
            width, height = 1280, 720
            background_color = (33, 33, 33)  # Dark background
            text_color = (255, 255, 255)  # White text

            # Create image
            image = Image.new('RGB', (width, height), background_color)
            draw = ImageDraw.Draw(image)

            # Try to load a font, fall back to default if not available
            try:
                # Try to use a bold font if available
                title_font = ImageFont.truetype("Arial Bold.ttf", 80)
                subtitle_font = ImageFont.truetype("Arial.ttf", 40)
            except IOError:
                # Fall back to default font
                title_font = ImageFont.load_default()
                subtitle_font = title_font

            # Add title text
            title = script['title']
            # Limit title length
            if len(title) > 50:
                title = title[:47] + "..."

            # Calculate text position to center it
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            title_height = title_bbox[3] - title_bbox[1]

            title_position = ((width - title_width) // 2, (height - title_height) // 2 - 50)

            # Draw title with shadow effect
            shadow_offset = 3
            draw.text((title_position[0] + shadow_offset, title_position[1] + shadow_offset),
                     title, font=title_font, fill=(0, 0, 0))
            draw.text(title_position, title, font=title_font, fill=text_color)

            # Add subtitle (keyword)
            subtitle = f"Understanding {keyword}"
            subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
            subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]

            subtitle_position = ((width - subtitle_width) // 2, title_position[1] + title_height + 30)
            draw.text(subtitle_position, subtitle, font=subtitle_font, fill=(200, 200, 200))

            # Add a border
            border_width = 10
            for i in range(border_width):
                draw.rectangle(
                    [(i, i), (width - i - 1, height - i - 1)],
                    outline=(255, 165, 0)  # Orange border
                )

            # Save the thumbnail
            thumbnail_path = OUTPUT_DIR / "thumbnails" / f"{keyword.replace(' ', '_')}.jpg"
            image.save(str(thumbnail_path), quality=95)

            logger.info(f"Created thumbnail for '{keyword}'")
            return str(thumbnail_path)

        except Exception as e:
            logger.error(f"Error creating thumbnail for '{keyword}': {str(e)}")
            return None

    def upload_to_youtube(self, video_path: str, thumbnail_path: str,
                         script: Dict[str, str], keyword: str) -> str:
        """Upload video to YouTube and schedule it for publication."""
        if not self.youtube:
            self.authenticate_youtube()

        try:
            # Prepare video metadata
            title = f"{script['title']} - Quick Explanation"
            if len(title) > 100:  # YouTube title limit
                title = title[:97] + "..."

            description = (
                f"{script['introduction']}\n\n"
                f"Key Points:\n"
                f"1. {script['key_point_1']}\n"
                f"2. {script['key_point_2']}\n"
                f"3. {script['key_point_3']}\n\n"
                f"{script['conclusion']}\n\n"
                f"#Trending #{keyword.replace(' ', '')} #Explanation"
            )

            # Generate tags
            tags = [keyword] + keyword.split() + ["explanation", "quick", "trending"]
            tags = list(set([tag.lower() for tag in tags if len(tag) > 2]))[:15]  # Limit to 15 unique tags

            # Set scheduled publication time (18:00 today or tomorrow)
            now = datetime.datetime.now()
            publish_time = now.replace(hour=PUBLICATION_HOUR, minute=0, second=0, microsecond=0)

            # If it's already past publication time, schedule for tomorrow
            if now > publish_time:
                publish_time = publish_time + datetime.timedelta(days=1)

            # Convert to RFC3339 format
            publish_time_rfc3339 = publish_time.isoformat() + "Z"

            # Prepare the request body
            body = {
                "snippet": {
                    "title": title,
                    "description": description,
                    "tags": tags,
                    "categoryId": "27"  # Education category
                },
                "status": {
                    "privacyStatus": "private",  # Start as private
                    "publishAt": publish_time_rfc3339,
                    "selfDeclaredMadeForKids": False
                }
            }

            # Upload the video
            logger.info(f"Starting upload for '{keyword}'")

            # Create upload request
            request = self.youtube.videos().insert(
                part=",".join(body.keys()),
                body=body,
                media_body=googleapiclient.http.MediaFileUpload(
                    video_path,
                    resumable=True
                )
            )

            # Execute the upload
            response = None
            while response is None:
                status, response = request.next_chunk()
                if status:
                    logger.info(f"Uploaded {int(status.progress() * 100)}%")

            video_id = response["id"]
            logger.info(f"Video upload complete. Video ID: {video_id}")

            # Upload thumbnail
            if thumbnail_path:
                self.youtube.thumbnails().set(
                    videoId=video_id,
                    media_body=googleapiclient.http.MediaFileUpload(thumbnail_path)
                ).execute()
                logger.info(f"Thumbnail uploaded for video ID: {video_id}")

            # Return the video URL
            return f"https://www.youtube.com/watch?v={video_id}"

        except googleapiclient.errors.HttpError as e:
            error_content = json.loads(e.content)
            logger.error(f"YouTube API error: {error_content['error']['message']}")
            return None
        except Exception as e:
            logger.error(f"Error uploading video for '{keyword}': {str(e)}")
            return None

    def optimize_metadata(self, video_id: str, keyword: str) -> bool:
        """Analyze competitors and optimize video metadata."""
        if not self.youtube:
            self.authenticate_youtube()

        try:
            # Search for top videos related to the keyword
            search_response = self.youtube.search().list(
                q=keyword,
                part="id,snippet",
                maxResults=10,
                type="video",
                relevanceLanguage="en",
                safeSearch="none",
                order="viewCount"  # Sort by view count to get popular videos
            ).execute()

            # Extract video IDs
            video_ids = [item["id"]["videoId"] for item in search_response["items"]]

            if not video_ids:
                logger.warning(f"No competitor videos found for '{keyword}'")
                return False

            # Get detailed video information
            videos_response = self.youtube.videos().list(
                id=",".join(video_ids),
                part="snippet,statistics"
            ).execute()

            # Extract tags and descriptions from successful videos
            all_tags = []
            descriptions = []

            for item in videos_response["items"]:
                # Only consider videos with significant views
                view_count = int(item["statistics"].get("viewCount", 0))
                if view_count > 1000:
                    snippet = item["snippet"]
                    if "tags" in snippet:
                        all_tags.extend(snippet["tags"])
                    descriptions.append(snippet.get("description", ""))

            # Find most common tags
            tag_counter = {}
            for tag in all_tags:
                tag_counter[tag] = tag_counter.get(tag, 0) + 1

            # Sort tags by frequency
            sorted_tags = sorted(tag_counter.items(), key=lambda x: x[1], reverse=True)
            optimized_tags = [tag for tag, _ in sorted_tags[:15]]

            # Add the original keyword if not present
            if keyword not in optimized_tags:
                optimized_tags.insert(0, keyword)

            # Extract common phrases from descriptions
            common_phrases = []
            for desc in descriptions:
                # Simple extraction of phrases (could be improved with NLP)
                lines = desc.split("\n")
                for line in lines:
                    if len(line) > 20 and len(line) < 100:  # Reasonable phrase length
                        common_phrases.append(line)

            # Get current video metadata
            current_video = self.youtube.videos().list(
                id=video_id,
                part="snippet"
            ).execute()

            if not current_video["items"]:
                logger.error(f"Could not retrieve metadata for video ID: {video_id}")
                return False

            current_snippet = current_video["items"][0]["snippet"]

            # Update video with optimized metadata
            update_body = {
                "id": video_id,
                "snippet": {
                    "title": current_snippet["title"],
                    "categoryId": current_snippet["categoryId"],
                    "description": current_snippet["description"],
                    "tags": optimized_tags
                }
            }

            # Add a few common phrases to the description if available
            if common_phrases:
                additional_desc = "\n\nRelated Information:\n"
                for i, phrase in enumerate(random.sample(common_phrases, min(3, len(common_phrases)))):
                    additional_desc += f"- {phrase}\n"

                update_body["snippet"]["description"] += additional_desc

            # Update the video
            self.youtube.videos().update(
                part="snippet",
                body=update_body
            ).execute()

            logger.info(f"Metadata optimized for video ID: {video_id}")
            return True

        except googleapiclient.errors.HttpError as e:
            error_content = json.loads(e.content)
            logger.error(f"YouTube API error during optimization: {error_content['error']['message']}")
            return False
        except Exception as e:
            logger.error(f"Error optimizing metadata for '{keyword}': {str(e)}")
            return False

    def process_keyword(self, keyword: str) -> bool:
        """Process a single keyword through the entire pipeline."""
        try:
            logger.info(f"Processing keyword: '{keyword}'")

            # Generate script
            script = self.generate_script(keyword)
            if not script:
                return False

            # Convert to speech
            audio_path = self.text_to_speech(script, keyword)
            if not audio_path:
                return False

            # Create video
            video_path = self.create_video(script, audio_path, keyword)
            if not video_path:
                return False

            # Create thumbnail
            thumbnail_path = self.create_thumbnail(script, keyword)

            # Upload to YouTube
            video_url = self.upload_to_youtube(video_path, thumbnail_path, script, keyword)
            if not video_url:
                return False

            # Extract video ID from URL
            video_id = video_url.split("=")[-1]

            # Optimize metadata
            self.optimize_metadata(video_id, keyword)

            logger.info(f"Successfully processed keyword: '{keyword}', Video URL: {video_url}")
            return True

        except Exception as e:
            logger.error(f"Error processing keyword '{keyword}': {str(e)}")
            return False

def main():
    """Main function to run the YouTube automation process."""
    try:
        # Initialize the AutoYouTube class
        auto_yt = AutoYouTube()

        # Run continuously
        while True:
            try:
                # Get trending keywords
                keywords = auto_yt.get_trending_keywords()
                logger.info(f"Found {len(keywords)} trending keywords: {keywords}")

                # Process each keyword
                for keyword in keywords:
                    success = auto_yt.process_keyword(keyword)
                    if success:
                        logger.info(f"Successfully processed '{keyword}'")
                    else:
                        logger.warning(f"Failed to process '{keyword}'")

                    # Add a delay between processing keywords to avoid rate limits
                    time.sleep(60)  # 1 minute delay

                # Wait for 24 hours before the next batch
                logger.info("Completed processing all keywords. Waiting for 24 hours...")
                time.sleep(24 * 60 * 60)  # 24 hours in seconds

            except Exception as e:
                logger.error(f"Error in main loop: {str(e)}")
                logger.info("Waiting for 1 hour before retrying...")
                time.sleep(60 * 60)  # 1 hour in seconds

    except KeyboardInterrupt:
        logger.info("Process interrupted by user. Exiting...")

if __name__ == "__main__":
    main()