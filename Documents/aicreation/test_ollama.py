#!/usr/bin/env python3
"""
test_ollama.py — Test d'utilisation d'Ollama avec l'API HTTP directe
"""

import requests
import json
import sys

def call_ollama_api(model_name, prompt):
    """Appelle l'API Ollama directement via HTTP."""
    api_url = "http://localhost:60765/api/chat"  # Port 60765 trouvé dans le processus Ollama

    payload = {
        "model": model_name,
        "messages": [{"role": "user", "content": prompt}]
    }

    try:
        print(f"Envoi de la requête à Ollama (modèle: {model_name})...")
        response = requests.post(api_url, json=payload)
        response.raise_for_status()  # Lève une exception en cas d'erreur HTTP

        result = response.json()
        return result["message"]["content"]
    except Exception as e:
        print(f"Erreur lors de l'appel à l'API Ollama: {e}", file=sys.stderr)
        return None

def main():
    # Utiliser le modèle spécifié ou phi3:medium par défaut
    model_name = sys.argv[1] if len(sys.argv) > 1 else ""

    # Message à envoyer
    prompt = """
    Write a 3-minute video script about "history facts" with the following structure:
    1. Title: An engaging title for the video
    2. Introduction: Brief introduction to the topic (30 seconds)
    3. 3 Key Points: Three main points about the topic (2 minutes)
    4. Conclusion: A brief conclusion (30 seconds)

    Format the response as a JSON with the following keys:
    "title", "introduction", "key_point_1", "key_point_2", "key_point_3", "conclusion"
    """

    # Appel à l'API
    response = call_ollama_api(model_name, prompt)

    if response:
        print("\nRéponse d'Ollama:")
        print(response)

        # Essayer de parser le JSON si présent
        try:
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].strip()
            else:
                json_str = response

            script = json.loads(json_str)

            print("\nJSON parsé avec succès:")
            print(json.dumps(script, indent=2, ensure_ascii=False))
        except json.JSONDecodeError as e:
            print(f"\nErreur lors du parsing JSON: {e}", file=sys.stderr)
            print("Contenu brut reçu:")
            print(response)

if __name__ == "__main__":
    main()
